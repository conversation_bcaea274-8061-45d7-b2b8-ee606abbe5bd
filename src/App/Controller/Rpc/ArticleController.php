<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Rpc;

use GuzzleHttp\Exception\GuzzleException;
use SonVideo\Erp\AntiFraud\Manager\ArticleAntiFraudStatusChecker;
use SonVideo\Erp\Article\Manager\ArticleMediaCloner;
use SonVideo\RpcBundle\Controller\ControllerTrait;

class ArticleController
{
    use ControllerTrait;

    /**
     * getClassMethods.
     *
     * Return list of rpc method(s) implemented in this class
     */
    public static function getClassMethods(): array
    {
        $domain = 'article:';

        return [
            $domain . 'clone_medias' => ['payload' => true],
            $domain . 'check_if_there_is_a_product_with_risk_of_fraud' => ['payload' => true],
        ];
    }

    /**
     * @throws \Exception
     * @throws GuzzleException
     */
    public function executeCloneMedias(array $args): bool
    {
        $params = $this->extractParameters($args, [
            'article_id_source',
            'article_id_target',
            'clone_media',
            'clone_document',
        ]);

        /** @var ArticleMediaCloner $article_media_cloner */
        $article_media_cloner = $this->getContainer()->get(ArticleMediaCloner::class);

        $article_media_cloner->clone(
            $params['article_id_source'],
            $params['article_id_target'],
            $params['clone_media'],
            $params['clone_document']
        );

        return true;
    }

    /** @throws \Exception */
    public function executeCheckIfThereIsAProductWithRiskOfFraud(array $args): array
    {
        $params = $this->extractParameters($args, ['article_keys']);

        if (!is_array($params['article_keys'])) {
            throw new \Exception('Payload must be an array.');
        }
        /** @var ArticleAntiFraudStatusChecker $article_anti_fraud_checker */
        $article_anti_fraud_checker = $this->getContainer()->get(ArticleAntiFraudStatusChecker::class);

        return $article_anti_fraud_checker->getExclusionStatusesFor($params['article_keys']);
    }
}
