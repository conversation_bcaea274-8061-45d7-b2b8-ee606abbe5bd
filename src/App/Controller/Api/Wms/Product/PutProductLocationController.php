<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Product;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\SqlErrorMessageException;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use LogicException;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Entity\ProductCorrectionEntity;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\Wms\ProductLocationWriteRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class PutProductLocationController.
 */
class PutProductLocationController extends AbstractApiController implements DataLoaderAwareInterface
{
    use DataLoaderAwareTrait;

    /**
     * Update product location quantity for correction.
     *
     * @Rest\Put(
     *     "/api/v1/wms/product/{product_id}/location/{location_id}",
     *     requirements={"product_id"="^\d+$", "location_id"="^\d+$"},
     *     name="put_product_location"
     * )
     *
     * @Rest\RequestParam(
     *     name="quantity",
     *     nullable=false,
     *     requirements={@Assert\Type("int"), @Assert\PositiveOrZero()},
     *     description="Quantity of products to set in the location")
     *
     * @Rest\RequestParam(
     *     name="comment",
     *     nullable=false,
     *     requirements={@Assert\Type("string"), @Assert\Length(min="15")},
     *     description="Comment of the correction")
     * @Rest\RequestParam(
     *     name="supplier_order_id",
     *     nullable=true,
     *     requirements={@Assert\Type("int")},
     *     description="Supplier order linked to the correction")
     *
     * @OA\Tag(name="WMS/Product")
     * @OA\Response(response=200, description="Update product location quantity")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $product_id,
        int $location_id,
        ParamFetcher $param_fetcher,
        ProductLocationWriteRepository $repository
    ): JsonResponse {
        $required_credentials = [UserPermission::PRODUCT_LOCATION_UPDATE];

        if (null !== $param_fetcher->get('supplier_order_id')) {
            $required_credentials[] = UserPermission::PRODUCT_LOCATION_UPDATE_WITH_SUPPLIER_ORDER;
        }

        $this->checkAuthorization($required_credentials);

        $params = array_merge(['product_id' => $product_id, 'location_id' => $location_id], $param_fetcher->all());
        $params['user'] = $this->getUser();

        try {
            $quantities = $repository->doQuantityCorrection(
                $this->data_loader->hydrate($params, ProductCorrectionEntity::class)
            );
        } catch (LogicException $exception) {
            return JSendResponse::error($exception->getMessage(), JsonResponse::HTTP_BAD_REQUEST);
        } catch (SqlErrorMessageException $exception) {
            return new JsonResponse(
                JSendFormatter::error($exception->getMessage(), 1000),
                JsonResponse::HTTP_BAD_REQUEST
            );
        } catch (InternalErrorException $exception) {
            return JSendResponse::internalError($exception);
        } catch (Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['quantities' => $quantities]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_product_location';
    }
}
