<?php

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Manager\InventoryClosingManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetInventoryCanCloseController extends AbstractApiController
{
    /**
     * Check if we can close an inventory by its id.
     *
     * @Rest\Get("/api/v1/wms/inventory/{inventory_id}/can-close",
     *     requirements={"inventory_id"="^\d+$"},
     *     name="get_wms_inventory_can_close"
     * )
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="Get if we can close an inventory",
     *     @OA\Response(
     *         response="200",
     *         description="The info of the requested inventory activation check"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(int $inventory_id, InventoryClosingManager $inventory_closing_manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::INVENTORY_ADMINISTRATE]);

        try {
            $result = $inventory_closing_manager->canClose($inventory_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success($result);
    }
}
