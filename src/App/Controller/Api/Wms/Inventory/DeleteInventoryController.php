<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryWriteRepository;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class DeleteInventoryController.
 */
class DeleteInventoryController extends AbstractApiController
{
    /**
     * Delete a non closed inventory.
     *
     * @Rest\Delete("/api/v1/wms/inventory/{inventory_id}",
     *     requirements={"inventory_id"="^\d+$"},
     *     name="delete_wms_inventory"
     * )
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="Delete a non closed inventory",
     *     @OA\Response(
     *         response="204",
     *         description="ok - if the inventory has been deleted successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $inventory_id,
        InventoryWriteRepository $repository,
        InventoryReadRepository $inventory_read_repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::INVENTORY_DELETE]);

        try {
            if (!$inventory_read_repository->exists($inventory_id)) {
                throw new NotFoundException('Inventory not found.');
            }

            $repository->delete($inventory_id);
        } catch (NotFoundException $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::noContent();
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'delete_wms_inventory';
    }
}
