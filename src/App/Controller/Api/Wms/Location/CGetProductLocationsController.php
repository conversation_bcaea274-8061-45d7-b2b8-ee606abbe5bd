<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Location;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Product\Mysql\Repository\ProductV2Repository;
use SonVideo\Erp\Repository\Wms\ProductLocationReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CGetProductLocationsController extends AbstractApiController
{
    /**
     * Get list of product's locations.
     *
     * @Rest\Get("/api/v1/wms/product/{product_id}/locations", requirements={"product_id"="^\d+$"},
     *                                                         name="cget_product_locations")
     * @Rest\QueryParam(name="filter", nullable=true, description="Filter parameters")
     * @Rest\QueryParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="location",
     *     description="Sort field."
     * )
     * @Rest\QueryParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     default="asc",
     *     nullable=true,
     *     description="Sort direction."
     * )
     *
     * @OA\Tag(name="WMS - Product")
     * @OA\Response(response=200, description="Get list of product's locations")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $product_id,
        ParamFetcher $param_fetcher,
        ProductLocationReadRepository $product_location_repository,
        ProductV2Repository $product_repository
    ): JsonResponse {
        try {
            // test product's existence => will throw a NotFoundException if not exists
            $product_repository->getOneByIdOrSku($product_id);

            $params = $param_fetcher->all();
            $filter = is_array($params['filter']) ? $params['filter'] : [];

            $product_locations = $product_location_repository->getProductLocationsForProduct(
                $product_id,
                $filter,
                $params['order_by'],
                $params['order_direction']
            );
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), JsonResponse::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['product_locations' => $product_locations]);
    }
}
