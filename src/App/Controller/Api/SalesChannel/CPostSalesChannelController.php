<?php

namespace App\Controller\Api\SalesChannel;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Entity\OptionalColumnDefinition;
use App\Entity\OptionalColumnsLoader;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\SalesChannel\Mysql\Repository\SalesChannelRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostSalesChannelController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a filtered list of sales channel.
     *
     * @Rest\Post("/api/v1/sales-channel", name="cpost_sales_channel")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, allows returning a lighter payload")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="display_order ASC, label ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Rest\RequestParam(
     *     name="included_dependencies",
     *     default={},
     *     description="Array of dependencies to hydrate in the result")
     *
     * @Operation(
     *     tags={"Sales channel"},
     *     summary="Retrieve a filtered list of sales channel",
     *     description="The sales channel can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of sales channel"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        SalesChannelRepository $repository,
        OptionalColumnsLoader $optional_columns_loader
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], $repository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);

            $results = $optional_columns_loader
                ->addColumn(
                    new OptionalColumnDefinition(
                        'statistics_sales_channel',
                        fn (array $sales_channel_ids): array => $repository->findStatisticsSalesChannels(
                            $sales_channel_ids
                        )
                    )
                )
                ->load($pager->getResults(), 'sales_channel_id', $params['included_dependencies']);

            $sales_channels = ColumnHelper::intersect($results, $param_fetcher->get('fields') ?? []);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'sales_channels' => $sales_channels,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_sales_channel';
    }
}
