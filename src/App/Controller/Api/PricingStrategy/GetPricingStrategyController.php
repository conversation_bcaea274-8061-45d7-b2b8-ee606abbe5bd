<?php

namespace App\Controller\Api\PricingStrategy;

use App\Adapter\Serializer\SerializerInterface;
use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\PricingStrategy\Dto\PricingStrategyDto;
use SonVideo\Erp\PricingStrategy\Manager\PricingStrategyManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class GetPricingStrategyController extends AbstractApiController
{
    /**
     * Get the pricing strategy config.
     *
     * @Rest\Get("/api/v1/pricing-strategy/{pricing_strategy_id}",
     * requirements={"pricing_strategy_id"="^\d+$"},
     * name="get_pricing_strategy_config")
     *
     * @OA\Tag(name="Pricing Strategy")
     * @OA\Response(response=200, description="Get the pricing strategy config",@OA\JsonContent(
     *           type="array",
     *           @OA\Items(ref=@Nelmio\Model(type=PricingStrategyDto::class))
     *       ))
     * @Security(name="Bearer")
     *
     * @throws ExceptionInterface
     */
    public function __invoke(
        int $pricing_strategy_id,
        ParamFetcher $param_fetcher,
        PricingStrategyManager $pricing_strategy_manager,
        SerializerInterface $serializer
    ): JsonResponse {
        try {
            $pricing_strategy = $pricing_strategy_manager->getPricingStrategyById($pricing_strategy_id);
        } catch (NotFoundException $e) {
            return JSendResponse::error('Pricing strategy not found', 404);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['pricing_strategy' => $serializer->normalize($pricing_strategy)]);
    }
}
