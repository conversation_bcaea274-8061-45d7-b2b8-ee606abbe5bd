<?php

namespace App\Controller\Api\PricingStrategy;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyProductRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyRepository;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PostPricingStrategyUnassignProductsController extends AbstractApiController
{
    /**
     * @Rest\Post(
     *     "/api/v1/pricing-strategy/{pricing_strategy_id}/unassign-products",
     *     requirements={"pricing_strategy_id"="^\d+$"},
     *     name="post_pricing_strategy_unassign_products"
     * )
     *
     * @Rest\RequestParam(
     *      name="product_ids",
     *      requirements=@Assert\Type("array"),
     *      description="List of sku to remove from the pricing strategy")
     *
     * @Operation(
     *     tags={"Pricing Strategy"},
     *     description="Unassign one or more products from a pricing strategy",
     *     @OA\Response(
     *         response="204",
     *         description="Success"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $pricing_strategy_id,
        ParamFetcher $param_fetcher,
        PricingStrategyRepository $pricing_strategy_config_repository,
        PricingStrategyProductRepository $repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            // check if exist
            $pricing_strategy_config_repository->fetchById($pricing_strategy_id);

            // unassign products
            $repository->unassignProducts($pricing_strategy_id, (array) $param_fetcher->get('product_ids'));

            return JSendResponse::noContent();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
