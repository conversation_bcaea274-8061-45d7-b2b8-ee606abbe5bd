<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class CPostArticlesController.
 */
class CPostArticlesController extends AbstractApiController
{
    /**
     * Retrieve a filtered list of articles.
     *
     * @Rest\Post("/api/v1/articles", name="cpost_articles")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="sku ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Retrieve a filtered list of articles",
     *     description="The articles can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of articles"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        ArticleReadRepository $repository
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], ArticleReadRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'articles' => JSendFormatter::convertNumeric(array_values($pager->getResults())),
            '_request' => JSendFormatter::convertNumeric($params),
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_articles';
    }
}
