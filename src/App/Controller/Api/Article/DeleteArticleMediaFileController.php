<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\Filesystem\ArticleMediaFile;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteArticleMediaFileController extends AbstractApiController
{
    /**
     * @Rest\Delete("/api/v1/article/media/{filename}",
     * requirements={"filename"="^\S+.(?i:jpg|png|pdf)$"},
     * name="delete_article_media_file")
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Delete an article media file from specified path",
     *     @OA\Response(
     *         response="204",
     *         description="ok - if the media has been deleted successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(string $filename, ArticleMediaFile $article_media_file): JsonResponse
    {
        $this->checkAuthorization([UserPermission::ARTICLE_MEDIA_ADMINISTRATE]);

        try {
            $article_media_file->deleteFileIfExists(
                $filename,
                ArticleMediaFile::RAISE_EXCEPTION_WHEN_FILE_DOES_NOT_EXIST
            );

            return JSendResponse::noContent();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'delete_article_media_file';
    }
}
