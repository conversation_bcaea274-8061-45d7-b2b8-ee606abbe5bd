<?php

namespace App\Controller\Api\Article;

use App\Adapter\Serializer\SerializerInterface;
use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticlePlannedPriceUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticlePlannedPriceManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PutArticlePlannedPriceController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/article/{article_id}/planned-price/{article_planned_price_id}",
     *     requirements={"article_id"="^\d+$", "article_planned_price_id"="^\d+$"},
     *     name="put-planned-price")
     *
     * @OA\RequestBody(@Nelmio\Model(type=ArticlePlannedPriceUpdateContextDto::class))
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Update a article planned price",
     *     description="Update a article planned price",
     *     @OA\Response(
     *         response="200",
     *         description="Return the id of the article planned price"
     *     )
     * )
     */
    public function __invoke(
        int $article_id,
        int $article_planned_price_id,
        Request $request,
        ArticlePlannedPriceManager $article_planned_price_manager,
        SerializerInterface $serializer
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            /** @var ArticlePlannedPriceUpdateContextDto $dto */
            $dto = $serializer->denormalize(
                array_merge((array) json_decode($request->getContent()), [
                    'article_planned_price_id' => $article_planned_price_id,
                    'article_id' => $article_id,
                ]),
                ArticlePlannedPriceUpdateContextDto::class
            );

            $updated = $article_planned_price_manager->update($dto);

            return JSendResponse::success(['updated' => $updated]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error(
                'Une erreur est survenue',
                Response::HTTP_BAD_REQUEST,
                $exception->getContext()
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
