<?php

namespace App\Controller\Api\Article;

use App\Adapter\Serializer\SerializerInterface;
use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticlePromoBudgetUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticlePromoBudgetManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PutArticlePromoBudgetController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/article/{article_id}/promo-budget/{promo_budget_id}",
     *     requirements={"article_id"="^\d+$", "promo_budget_id"="^\d+$"},
     *     name="put-promo-budget")
     *
     * @OA\RequestBody(@Nelmio\Model(type=ArticlePromoBudgetUpdateContextDto::class))
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Update a article promo budget",
     *     description="Update a article promo budget",
     *     @OA\Response(
     *         response="200",
     *         description="Return the id of the article promo budget"
     *     )
     * )
     */
    public function __invoke(
        int $article_id,
        int $promo_budget_id,
        Request $request,
        SerializerInterface $serializer,
        ArticlePromoBudgetManager $manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            /** @var ArticlePromoBudgetUpdateContextDto $dto */
            $dto = $serializer->deserialize($request->getContent(), ArticlePromoBudgetUpdateContextDto::class, 'json');
            $dto->id = $promo_budget_id;
            $dto->article_id = $article_id;

            $manager->update($dto);

            return JSendResponse::noContent();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST, $exception->getContext());
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
