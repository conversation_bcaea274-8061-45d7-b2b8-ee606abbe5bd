<?php

namespace App\Controller\Api\Transfer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Transfer\Manager\TransferCollectionManager;
use Symfony\Component\HttpFoundation\JsonResponse;

class CGetTransferReportsController extends AbstractApiController
{
    /**
     * Retrieve a list of transfer reports.
     *
     * @Rest\Get("/api/v1/transfer-reports", name="cget_transfer_reports")
     *
     * @OA\Tag(name="Transfer")
     * @OA\Response(response=200, description="Retrieve a list of Transfer reports")
     * @Security(name="Bearer")
     */
    public function __invoke(TransferCollectionManager $manager): JsonResponse
    {
        try {
            $reports = $manager->getTransferReports();

            $response = JSendResponse::success([
                'reports' => $reports,
            ]);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            $response = JSendResponse::error($exception->getMessage());
        }

        return $response;
    }
}
