<?php

namespace App\Controller\Api\Accounting\Payment;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Accounting\Payment\Manager\PaymentManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class PutPaymentCancelRemitController extends AbstractApiController
{
    /**
     * Cancel a remitted payment.
     *
     * @Rest\Put("/api/v1/accounting/payment/cancel-remit/{customer_order_payment_id}",
     *     requirements={"customer_order_payment_id"="^\d+$"},
     *     name="put_accounting_payment_cancel_remit"
     * )
     *
     * @OA\Tag(name="Customer Order Payment")
     * @OA\Response(response=200, description="Remit a customer order payment")
     * @Security(name="Bearer")
     */
    public function __invoke(int $customer_order_payment_id, PaymentManager $payment_manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CANCEL]);

        try {
            return JSendResponse::success([
                'payments' => $payment_manager->processCancelRemit($customer_order_payment_id),
            ]);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_FORBIDDEN, $exception->getContext());
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error('Payments cancel remit - an unexpected error occurred', [
                'exception' => $exception,
            ]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
