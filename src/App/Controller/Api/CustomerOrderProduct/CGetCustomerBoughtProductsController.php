<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\CustomerOrderProduct;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\CustomerOrderProduct\Entity\BoughtProductEntity;
use SonVideo\Erp\CustomerOrderProduct\Mysql\Repository\CustomerOrderProductRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CGetCustomerBoughtProductsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Get(
     *     "/api/v1/customer/{customer_id}/bought-products",
     *     requirements={"customer_id"="^\d+$"},
     *     name="cget_customer_bought_products"
     * )
     *
     * @Rest\QueryParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="customer_order_created_at desc, selling_price desc",
     *     description="Sort by field.")
     *
     * @Rest\QueryParam(
     *     name="order_direction",
     *     requirements=@Assert\Type("string"),
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\QueryParam(
     *     name="page",
     *     default="1",
     *     requirements={"page"="^\d+$"},
     *     description="Page offset")
     *
     * @Rest\QueryParam(
     *     name="limit",
     *     default="20",
     *     requirements={"limit"="^\d+$"},
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"Customer"},
     *     summary="Retrieve a list of customer's bought products",
     *     description="Customer's bought products can be re-ordered and paginated",
     *     @OA\Response(
     *         response="200",
     *         description="List of bought products"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        string $customer_id,
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        CustomerOrderProductRepository $repository
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere(
                    ['_and' => ['customer_id' => ['_eq' => $customer_id]]],
                    CustomerOrderProductRepository::COLUMNS_MAPPING
                )
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findBoughtProductsPaginated($query_builder);
            $bought_products = $this->mapToEntitiesData($pager->getResults(), BoughtProductEntity::class);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'bought_products' => $bought_products,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cget_customer_bought_products';
    }
}
