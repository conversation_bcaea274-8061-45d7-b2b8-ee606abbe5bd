<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\ReleaseNote;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use App\Sql\Helper\Pager;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\ReleaseNote\Client\ReleaseNotesIoApiClient;
use SonVideo\Erp\ReleaseNote\Manager\ReleaseNoteIoResponseFormatter;
use Symfony\Component\HttpFoundation\JsonResponse;

class CGetReleasesNotesController extends AbstractApiController
{
    /**
     * Get collection of release notes.
     *
     * @Rest\Get("/api/v1/releases", name="cget_release_notes")
     *
     * @Rest\QueryParam(
     *     name="page",
     *     default="1",
     *     requirements={"page"="^\d+$"},
     *     description="Page offset")
     * @OA\Tag(name="Release Note")
     * @OA\Response(response=200, description="Get release notes paginated, from external ReleaseNotes.io service")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ReleaseNotesIoApiClient $release_notes_api_client,
        ParamFetcher $param_fetcher
    ): JsonResponse {
        try {
            $params = ['page' => $param_fetcher->get('page')];
            $response = $release_notes_api_client->get('/releases', $params);
            $response = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

            // Map to our pager response for APi consistency
            $pager = new Pager($response['current_page'], $response['per_page'], $response['total']);

            // Format results
            $release_notes = (new ReleaseNoteIoResponseFormatter($response['data']))->format();
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'release_notes' => $release_notes,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cget_releases';
    }
}
