<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Shipment;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Shipment\Entity\ShipmentDeliveryNoteEntity;
use SonVideo\Erp\Shipment\Mysql\Repository\ShipmentDeliveryNoteRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CpostShipmentDeliveryNotesController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a filtered list of shipment delivery notes.
     *
     * @Rest\Post("/api/v1/shipment/{shipment_id}/delivery-notes", name="cpost_shipment_delivery_notes")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, for a lighter payload"
     * )
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters"
     * )
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="shipment_delivery_note_id asc",
     *     description="Sort field."
     * )
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction."
     * )
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset"
     * )
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page"
     * )
     * @Rest\RequestParam(
     *     name="included_dependencies",
     *     default={},
     *     description="Array of dependencies to hydrate in the result"
     * )
     *
     * @OA\Tag(name="Shipment")
     * @OA\Response(response=200, description="Retrieve delivery notes for a specified shipment")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $shipment_id,
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        ShipmentDeliveryNoteRepository $repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::SHIPMENT_READ]);

        try {
            $params = $param_fetcher->all();

            $where = $param_fetcher->get('where') ?? [];
            $where['shipment_id'] = ['_eq' => $shipment_id];

            $query_builder
                ->setWhere($where, ShipmentDeliveryNoteRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);

            $shipment_delivery_notes = ColumnHelper::intersect(
                $this->mapToEntitiesData($pager->getResults(), ShipmentDeliveryNoteEntity::class),
                $param_fetcher->get('fields') ?? []
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'shipment_delivery_notes' => $shipment_delivery_notes,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_shipment_delivery_notes';
    }
}
