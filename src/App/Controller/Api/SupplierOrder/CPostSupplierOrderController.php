<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\SupplierOrder;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Entity\OptionalColumnsLoader;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\SupplierOrder\Entity\SupplierOrderEntity;
use SonVideo\Erp\SupplierOrder\Mysql\Repository\SupplierOrderRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostSupplierOrderController extends AbstractApiController implements DataLoaderAwareInterface, SerializerAwareInterface
{
    use SerializerAwareTrait;
    use MapToEntityTrait;

    /**
     * @Rest\Post("/api/v1/supplier-orders", name="cpost_supplier_orders")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, for a lighter payload"
     * )
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters"
     * )
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="supplier_order_id",
     *     description="Sort by field."
     * )
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction."
     * )
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset"
     * )
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="20",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page"
     * )
     *
     * @Operation(
     *     tags={"Supplier order"},
     *     summary="Retrieve a list of supplier orders",
     *     description="Supplier orders can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a list of supplier orders"
     *     )
     * )
     *
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        SupplierOrderRepository $repository,
        OptionalColumnsLoader $optional_columns_loader
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($params['where'], SupplierOrderRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'));

            // need to order on 2 differents columns, handle the order by differently
            $backorder_order_by = null;
            if ('backorder_status' === $param_fetcher->get('order_by')) {
                $backorder_order_by = $param_fetcher->get('order_direction');
            } else {
                $query_builder->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));
            }

            $pager = $repository->findAllPaginated($query_builder, $backorder_order_by);

            $supplier_orders = ColumnHelper::intersect(
                $this->serializer->denormalize($pager->getResults(), SupplierOrderEntity::class . '[]'),
                $param_fetcher->get('fields') ?? []
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'supplier_orders' => $this->serializer->normalize($supplier_orders),
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_supplier_orders';
    }
}
