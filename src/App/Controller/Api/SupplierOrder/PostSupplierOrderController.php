<?php

namespace App\Controller\Api\SupplierOrder;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Formatter\Http\JSendResponse;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SupplierOrder\Manager\SupplierOrderCreator;
use Symfony\Component\HttpFoundation\JsonResponse;

class PostSupplierOrderController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Post("/api/v1/supplier-order", name="post_supplier_order")
     *
     * @OA\Tag(name="Supplier Order")
     * @OA\Response(
     *     response=200,
     *     description="Create supplier order",
     * )
     * @OA\Response(
     *     response=400,
     *     description="Something is wrong with the payload",
     * )
     * @OA\Response(
     *     response=401,
     *     description="Unauthorized",
     * )
     * @OA\Response(
     *     response=500,
     *     description="An error occurred while creating the supplier order",
     * )
     * @Rest\RequestParam(
     *     name="supplier_id",
     *     nullable=false,
     *     description="Id du fournisseur")
     *
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        SupplierOrderCreator $supplier_order_creator,
        SerializerInterface $serializer
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_BUYERS_WRITE]);

        $supplier_id = $param_fetcher->get('supplier_id');
        try {
            $supplier_order_id = $supplier_order_creator->create($supplier_id);

            return JSendResponse::success(['supplier_order_id' => $supplier_order_id]);
        } catch (Exception $exception) {
            $this->logger->error('[post_supplier_order] An error occurred while creating the supplier order', [
                'exception' => $exception,
            ]);

            return JSendResponse::error('An error occurred creating the supplier order');
        }
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_supplier_order';
    }
}
