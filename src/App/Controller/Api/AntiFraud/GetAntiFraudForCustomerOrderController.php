<?php

namespace App\Controller\Api\AntiFraud;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Model;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\AntiFraud\Manager\AntiFraudForCustomerOrder;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetAntiFraudForCustomerOrderController extends AbstractApiController
{
    /**
     * Get anti-fraud info related to the specified customer order.
     *
     * @Rest\Get("/api/v1/anti-fraud/{customer_order_id}",
     * requirements={"customer_order_id"="^\d+$"},
     * name="get_anti_fraud_for_customer_order")
     *
     * @OA\Tag(name="Anti fraud")
     * @OA\Response(
     *     response=200,
     *     description="Get anti-fraud info related to the specified customer order.",
     *         @Model(type=\SonVideo\Erp\AntiFraud\Entity\AntiFraudForCustomerOrder\AntiFraudForCustomerOrderResponsePayload::class, groups={"full"})
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $customer_order_id,
        AntiFraudForCustomerOrder $anti_fraud_for_customer_order
    ): JsonResponse {
        try {
            return JSendResponse::success($anti_fraud_for_customer_order->load($customer_order_id));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }
}
