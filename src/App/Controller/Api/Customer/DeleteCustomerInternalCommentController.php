<?php

namespace App\Controller\Api\Customer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\System\Mysql\Repository\SystemEventRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteCustomerInternalCommentController extends AbstractApiController
{
    /**
     * Delete a customer internal comment.
     *
     * @Rest\Delete("/api/v1/system-event/{event_id}",
     *   requirements={"event_id"="^\d+$"},
     *   name="delete_customer_internal_comment")
     *
     * @Operation(
     *     tags={"Customer"},
     *     summary="Delete an internal comment",
     *     @OA\Response(
     *          response=200,
     *          description="ok - if the customer internal comment has been deleted successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(int $event_id, SystemEventRepository $system_event_repository): JsonResponse
    {
        $this->checkAuthorization([UserPermission::CUSTOMER_COMMENT_WRITE]);

        try {
            $deleted = $system_event_repository->deleteInternalComment($event_id);

            if (0 === $deleted) {
                return JSendResponse::error("Event with id $event_id not found.", Response::HTTP_NOT_FOUND);
            }

            return JSendResponse::success(['deleted' => $deleted]);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }
}
