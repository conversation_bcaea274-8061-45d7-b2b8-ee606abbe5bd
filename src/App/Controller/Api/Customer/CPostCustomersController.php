<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Customer;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\OptionalColumnDefinition;
use App\Entity\OptionalColumnsLoader;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\CreditNote\Mysql\Repository\CreditNoteRepository;
use SonVideo\Erp\Customer\Entity\CustomerEntity;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Synapps\Client\RpcClientService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class CPostCustomersController.
 */
class CPostCustomersController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a filtered list of customers.
     *
     * @Rest\Post("/api/{api_version}/customers", name="cpost_customers",
     * requirements={"api_version"="^(v1|v2)$"})
     *
     * @Rest\RequestParam(
     *     name="where",
     *     default={},
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="customer_id ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Rest\RequestParam(
     *     name="included_dependencies",
     *     default={},
     *     description="Array of dependencies to hydrate in the result")
     *
     * @Operation(
     *     tags={"Customer"},
     *     summary="Retrieve a filtered list of customers",
     *     description="The customers can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of customers"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        string $api_version,
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        CustomerRepository $customer_repository,
        CreditNoteRepository $credit_note_repository,
        OptionalColumnsLoader $optional_columns_loader,
        RpcClientService $rpc_client
    ): JsonResponse {
        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($params['where'], CustomerRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $customer_repository->findAllPaginated($query_builder);
            $logger = $this->logger;

            $results = $optional_columns_loader
                ->addColumn(
                    (new OptionalColumnDefinition(
                        'customer_orders_aggregates',
                        fn (array $customer_ids): array => $customer_repository->fetchCustomerOrdersAggregates(
                            $customer_ids
                        )
                    ))->asObject()
                )
                ->addColumn(
                    (new OptionalColumnDefinition(
                        'credit_notes_aggregates',
                        fn (array $customer_ids): array => $credit_note_repository->fetchCustomersCreditNotesAggregates(
                            $customer_ids
                        )
                    ))->asObject()
                )
                ->addColumn(
                    (new OptionalColumnDefinition('newsletter', function (array $customer_emails) use (
                        $rpc_client,
                        $logger
                    ): array {
                        try {
                            $response = $rpc_client->call('bo-cms', 'customer:get_status_newsletter', [
                                current($customer_emails),
                            ]);
                        } catch (\Exception $e) {
                            $logger->error($e->getMessage(), ['exception' => $e]);
                            $response = [
                                'result' => [
                                    'status' => 'unknown',
                                    'origin' => null,
                                    'created_at' => null,
                                    'last_activity' => null,
                                ],
                            ];
                        }

                        return [$response['result'] + ['email_address' => current($customer_emails)]];
                    }))
                        ->asObject()
                        ->withInternalPivot('email_address')
                )
                ->load($pager->getResults(), 'customer_id', $params['included_dependencies']);

            // API v2 map all columns in entity in order to properly format data types issued from the data layer
            // We keep the v1 for older services that may rely on it and format the data client side
            if ('v2' === $api_version) {
                $results = $this->mapToEntitiesData($results, CustomerEntity::class);
            }
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'customers' => $results,
            '_request' => $params,
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_customers';
    }
}
