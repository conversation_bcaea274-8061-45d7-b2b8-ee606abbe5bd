<?php

namespace App\Controller\Api\Customer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Customer\Manager\CustomerManager;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class PostCreateCustomerController.
 */
class PostCreateCustomerController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/customer", name="post_create_customer")
     *
     * @Rest\RequestParam(
     *     name="email",
     *     requirements={"customer_email"="^\S+$"},
     *     description="Email")
     *
     * @OA\Tag(name="Customer")
     * @OA\Response(response=200, description="Create Customer")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        CustomerRepository $repository,
        CustomerManager $customer_manager
    ): JsonResponse {
        $email = $param_fetcher->get('email');
        try {
            $result = $customer_manager->callGetCustomerByEmail($email);

            if (true === $result['status']) {
                return JSendResponse::success(
                    $repository->createAccount($result['account']['customer_id'], $result['account']['email'])
                );
            }

            $account = $customer_manager->createActiveAccountByEmail($email);

            return JSendResponse::success(
                $repository->createAccount($account['customer_id'], $account['email'], CustomerOrderOrigin::BACKOFFICE)
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'post_create_customer';
    }
}
