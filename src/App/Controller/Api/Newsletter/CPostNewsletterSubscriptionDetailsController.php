<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Newsletter;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Synapps\Client\RpcClientService;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class CPostNewsletterSubscriptionDetailsController.
 */
class CPostNewsletterSubscriptionDetailsController extends AbstractApiController
{
    /**
     * Retrieve a filtered list of newsletter subscription details.
     *
     * @Rest\Post("/api/v1/newsletter-subscription-details", name="cpost_newsletter_subscription_details")
     *
     * @Rest\RequestParam(
     *     name="email_address",
     *     description="adresse email")
     *
     * @Operation(
     *     tags={"Newsletter"},
     *     summary="Retrieve a newsletter subscription details",
     *     description="The newsletter subscription details ",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a newsletter subscription details"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, RpcClientService $rpc_client): JsonResponse
    {
        try {
            $params = $param_fetcher->all();

            $results = $rpc_client->call('bo-cms', 'customer:get_status_newsletter', [$params['email_address']]);

            if ([] === $results) {
                throw new \Exception('Email address not found');
            }
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'details' => $results['result'],
        ]);
    }
}
