<?php

namespace App\Controller\Api\SalesPeriod;

use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation as Nelmio;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SalesPeriod\Dto\CreationContext\ArticleSalesPeriodCreationContextDto;
use SonVideo\Erp\SalesPeriod\Manager\ArticleSalesPeriodManager;
use SonVideo\Erp\SalesPeriod\Mysql\Repository\ArticleSalesPeriodRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostArticleSalesPeriodController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/article/{id_or_sku}/article-sales-period",
     *     requirements={"id_or_sku"="^[A-Z0-9]{1}[\-_A-Z0-9]{0,39}$"},
     *     name="post_article_sales")
     *
     * @OA\RequestBody(@Nelmio\Model(type=ArticleSalesPeriodCreationContextDto::class))
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Create a article sales",
     *     description="Create a article sales",
     *     @OA\Response(
     *         response="200",
     *         description="Return the id of the created article sales period"
     *     )
     * )
     */
    public function __invoke(
        int $id_or_sku,
        Request $request,
        SingleArticleReadRepository $single_article_read_repository,
        ArticleSalesPeriodRepository $article_sales_period_repository,
        ArticleSalesPeriodManager $article_sales_period_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            $user = $this->getUser();
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

            $article = $single_article_read_repository->getOneByIdOrSku($id_or_sku);
            if (null === ($article->article_id ?? null)) {
                throw new NotFoundException(sprintf('Article not found with id or sku "%s".', $id_or_sku));
            }

            $data['article_id'] = $article->article_id;

            $article_sales = $article_sales_period_manager->create($data, $user);

            return JSendResponse::success(['article_sales' => $article_sales]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (InternalErrorException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST, $exception->getContext());
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
