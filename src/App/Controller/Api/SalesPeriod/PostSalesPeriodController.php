<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\SalesPeriod;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SalesPeriod\Mysql\Repository\SalesRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class PostSalesPeriodController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/sales-period", name="post_sales_period")
     *
     * @Rest\RequestParam(
     *     name="start_at",
     *     nullable=false,
     *     description="Start date of the sales")
     *
     * @Rest\RequestParam(
     *     name="end_at",
     *     nullable=false,
     *     description="End date of the sales")
     * @OA\Tag(name="Sales Period")
     * @OA\Response(response=200, description="Create a sales period")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, SalesRepository $sales_period_repository): JsonResponse
    {
        $this->checkAuthorization([UserPermission::SALES_PERIOD_WRITE]);

        try {
            $result = $sales_period_repository->create($param_fetcher->get('start_at'), $param_fetcher->get('end_at'));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['result' => $result]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_sales_period';
    }
}
