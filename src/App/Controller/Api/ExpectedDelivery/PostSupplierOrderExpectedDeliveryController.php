<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\ExpectedDelivery;

use App\Contract\DataLoaderInterface;
use App\Controller\Api\AbstractApiController;
use App\Exception\InternalErrorException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SupplierOrderProduct\Entity\ExpectedDeliveryEntity;
use SonVideo\Erp\SupplierOrderProduct\Mysql\Repository\ExpectedDeliveryRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class PostSupplierOrderExpectedDeliveryController extends AbstractApiController
{
    /**
     * @Rest\Post(
     *     "/api/v1/supplier-order/{supplier_order_id}/expected-deliveries",
     *     requirements={"supplier_order_id"="^\d+$"},
     *     name="post_supplier_order_expected_delivery"
     * )
     * @Rest\RequestParam(
     *     name="date",
     *     requirements=@Assert\DateTime(format="Y-m-d"),
     *     description="Expected date"
     * )
     * @Rest\RequestParam(
     *     name="expected_deliveries",
     *     default={},
     *     description="Array of expected quantities on supplier product lines"
     * )
     *
     * @Operation(
     *     tags={"ExpectedDelivery"},
     *     summary="Create a bunch of expected deliveries",
     *     description="Create a bunch of expected deliveries on a supplier order at ones",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a list of supplier order products"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $supplier_order_id,
        ParamFetcher $param_fetcher,
        ExpectedDeliveryRepository $repository,
        DataLoaderInterface $data_loader
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::SUPPLIER_ORDER_UPDATE]);

        try {
            $params = $param_fetcher->all();

            $created = 0;
            $entities = array_map(
                fn ($expected_delivery): ExpectedDeliveryEntity => $data_loader->hydrate(
                    [
                        'supplier_order_product_id' => $expected_delivery['supplier_order_product_id'],
                        'expected_quantity' => $expected_delivery['quantity'],
                        'expected_delivery_date' => $params['date'],
                    ],
                    ExpectedDeliveryEntity::class
                ),
                $params['expected_deliveries']
            );
            if ([] !== $entities) {
                $created = $repository->createAll($entities);
            }
        } catch (InternalErrorException $exception) {
            return JSendResponse::internalError($exception);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'created' => $created,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_supplier_order_expected_delivery';
    }
}
