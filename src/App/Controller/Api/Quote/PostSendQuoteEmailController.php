<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Quote;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Quote\Manager\QuoteEmailSender;
use SonVideo\Erp\Referential\Rpc\BoCmsRpcMethodReferential;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Synapps\Client\RpcClientAwareInterface;
use SonVideo\Synapps\Client\RpcClientAwareTrait;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class PostSendQuoteEmailController extends AbstractApiController implements DataLoaderAwareInterface, RpcClientAwareInterface
{
    use MapToEntityTrait;
    use RpcClientAwareTrait;

    /**
     * @Rest\Post("/api/v1/quote/{quote_id}/send-email",
     *   requirements={"quote_id"="^\d+$"},
     *   name="post_send_email_quote")
     *
     * @Operation(
     *     tags={"Quote"},
     *     summary="Send a quote email",
     *     @OA\Response(
     *         response="200",
     *         description="Send the quote email"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(int $quote_id, QuoteEmailSender $quote_email_sender): JsonResponse
    {
        $this->checkAuthorization([UserPermission::QUOTE_WRITE]);

        try {
            $quote_email_sender->send($quote_id, $this->getUser());
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        // try to push quote to CMS, with silent failure
        try {
            $this->rpc_client->call(
                BoCmsRpcMethodReferential::SERVER_NAME,
                BoCmsRpcMethodReferential::QUOTE_UPSERT_METHOD,
                [$quote_id]
            );
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);
        }

        return JSendResponse::success([]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_send_email_quote';
    }
}
