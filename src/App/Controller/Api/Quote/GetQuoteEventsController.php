<?php

namespace App\Controller\Api\Quote;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Quote\Manager\QuoteEventFetcher;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetQuoteEventsController extends AbstractApiController implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    /**
     * Retrieve a filtered list of quote event.
     *
     * @Rest\Get("/api/v1/quote/{quote_id}/events",
     * requirements={"quote_id"="^\d+$"},
     * name="get_quote_events")
     *
     * @Rest\QueryParam(name="type", nullable=true, requirements="^(all|history|comment)$", description="Type of events", default="all")
     *
     * @OA\Tag(name="System events")
     * @OA\Response(response=200, description="Retrieve a filtered list of system events")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $quote_id,
        QuoteEventFetcher $system_event_fetcher,
        ParamFetcher $param_fetcher
    ): JsonResponse {
        try {
            $type = $param_fetcher->get('type', true);
            $system_events = $system_event_fetcher->fetchAll($quote_id, $type);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'system_events' => $this->serializer->normalize($system_events),
        ]);
    }
}
