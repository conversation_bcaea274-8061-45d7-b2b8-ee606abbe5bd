<?php

namespace App\Controller\Api\Statistics;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DataWarehouse\Manager\CustomerOrderStatsManager;
use SonVideo\Erp\DataWarehouse\Manager\DataParameterManager;
use SonVideo\Erp\Referential\DataWarehouse\DeprecatedConstant;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;

class CPostInvoiceOverviewController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/statistics/invoice/overview", name="cpost_statistics_invoice_overview")
     *
     * @Rest\RequestParam(
     *     name="periods",
     *     nullable=false,
     *     description="Periods to display"
     * )
     *
     * @Rest\RequestParam(
     *     name="filters",
     *     nullable=true,
     *      description="Filters to apply"
     * )
     *
     * @Operation(
     *     tags={"Statistics"},
     *     summary="Calculate customer order statistics based periods",
     *     @OA\Response(
     *         response="200",
     *          description="Customer order overview"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        CustomerOrderStatsManager $manager,
        DataParameterManager $data_parameter_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::STATISTICS_GLOBAL_READ]);

        try {
            $response = $manager->getInvoiceStatisticsOnPeriodsComparedWithLastYear(
                $param_fetcher->get('periods'),
                $param_fetcher->get('filters') ?? []
            );

            $last_sync = $data_parameter_manager
                ->getSyncLastUpdate(DeprecatedConstant::CUSTOMER_ORDER_SYNCHRONIZER_LAST_UPDATE)
                ->get('value');
        } catch (\Throwable $e) {
            return JSendResponse::error($e->getMessage());
        }

        return JSendResponse::success([
            'statistics' => $response,
            'last_sync' => $last_sync,
            '_request' => $param_fetcher->all(),
        ]);
    }
}
