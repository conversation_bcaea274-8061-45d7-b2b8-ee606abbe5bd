<?php

namespace App\Client;

use App\Exception\GraphQLResponseException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;

class GraphQLClient
{
    private string $erp_graphql_api_endpoint;
    private string $erp_graphql_admin_api_key;
    private LoggerInterface $logger;
    private Client $client;
    private string $metadata_api_endpoint;

    public function __construct(
        string $erp_graphql_url,
        string $erp_graphql_admin_api_key,
        LoggerInterface $logger,
        ?Client $client = null
    ) {
        $this->erp_graphql_api_endpoint = $erp_graphql_url . '/v1/graphql';
        $this->metadata_api_endpoint = $erp_graphql_url . '/v1/metadata';
        $this->erp_graphql_admin_api_key = $erp_graphql_admin_api_key;
        $this->logger = $logger;
        $this->client = $client ?? new Client();
    }

    /**
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function call(array $query)
    {
        $this->logger->debug($query['query']);

        if (isset($query['variables']) && is_string($query['variables'])) {
            $query['variables'] = json_decode($query['variables'], true, 512, JSON_THROW_ON_ERROR);
        }

        $this->logger->debug('variables', $query['variables'] ?? []);

        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'x-hasura-admin-secret' => $this->erp_graphql_admin_api_key,
        ];

        $this->logger->debug('Curl headers', $headers);

        $response = $this->client->request('POST', $this->erp_graphql_api_endpoint, [
            'headers' => $headers,
            'body' => json_encode($query, JSON_THROW_ON_ERROR),
        ]);

        $response = $response->getBody()->getContents();

        $this->logger->debug(sprintf('Response : %s', $response));

        $response = json_decode($response, true, 512, JSON_THROW_ON_ERROR);

        if (isset($response['errors'])) {
            throw new GraphQLResponseException(trim(sprintf('GraphQL call failed %s', isset($response['errors'][0]['message']) ? ': ' . $response['errors'][0]['message'] : '')));
        }

        return $response;
    }

    /**
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function triggerEvent(string $name, array $payload, string $source = 'erp'): string
    {
        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'x-hasura-admin-secret' => $this->erp_graphql_admin_api_key,
        ];
        $body = [
            'type' => 'pg_invoke_event_trigger',
            'args' => [
                'name' => $name,
                'source' => $source,
                'payload' => $payload,
            ],
        ];

        $response = $this->client->request('POST', $this->metadata_api_endpoint, [
            'headers' => $headers,
            'body' => json_encode($body, JSON_THROW_ON_ERROR),
        ]);
        $decoded = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

        return $decoded['event_id'];
    }
}
