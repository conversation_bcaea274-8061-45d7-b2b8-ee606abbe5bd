<?php

namespace App\Params;

use FOS\RestBundle\Request\ParamFetcher;

final class FiltersParams
{
    private int $page = 1;

    private int $limit = 50;

    private ?string $order_by = '';

    private ?string $order_direction = '';

    /** @var array<string,mixed> */
    private array $filters = [];

    /** create a new instance */
    public static function create(): self
    {
        return new FiltersParams();
    }

    /** Create a new instance from ParamFetcher */
    public static function fromParamFetcher(ParamFetcher $param_fetcher): FiltersParams
    {
        return FiltersParams::create()
            ->setFilters($param_fetcher->get('where'))
            ->setPage($param_fetcher->get('page'))
            ->setLimit($param_fetcher->get('limit'))
            ->setOrderBy($param_fetcher->get('order_by'))
            ->setOrderDirection($param_fetcher->get('order_direction'));
    }

    public function getFilters(): array
    {
        return $this->filters;
    }

    public function setFilters(?array $filters): self
    {
        $this->filters = $filters ?? [];

        return $this;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function setPage(int $page): self
    {
        $this->page = $page;

        return $this;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function setLimit(int $limit): self
    {
        $this->limit = $limit;

        return $this;
    }

    public function getOrderBy(): ?string
    {
        return $this->order_by;
    }

    public function setOrderBy(string $order_by = null): self
    {
        $this->order_by = $order_by;

        return $this;
    }

    public function getOrderDirection(): ?string
    {
        return $this->order_direction;
    }

    public function setOrderDirection(string $order_direction = null): self
    {
        $this->order_direction = $order_direction;

        return $this;
    }
}
