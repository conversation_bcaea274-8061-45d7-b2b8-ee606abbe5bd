<?php

namespace App\Database\ConnectionProvider;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use SonVideo\Orm\Adapter\DBAL\AbstractRepository;
use SonVideo\Orm\DataMapper;

abstract class AbstractMysqlErpRepository extends AbstractRepository implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    public function __construct(MysqlErpConnectionProvider $connection_provider, DataMapper $data_mapper)
    {
        parent::__construct($connection_provider, $data_mapper);
    }
}
