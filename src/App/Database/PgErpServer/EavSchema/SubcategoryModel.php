<?php

namespace App\Database\PgErpServer\EavSchema;

use App\Database\PgErpServer\EavSchema\AutoStructure\Subcategory as SubcategoryStructure;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

/**
 * SubcategoryModel.
 *
 * Model class for table subcategory.
 *
 * @see Model
 */
class SubcategoryModel extends Model
{
    use WriteQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new SubcategoryStructure();
        $this->flexible_entity_class = Subcategory::class;
    }

    /**
     * Retrieve eav facets definitions for given subcategories.
     *
     * @throws ModelException
     */
    public function findEavsByIds(array $subcategory_ids): array
    {
        $sql = <<<SQL
        WITH
            attribute_value_projection AS (
                SELECT
                    sa.subcategory_id,
                    sa.display_order,
                    sa.filter_status = 'ACTIVE_OPENED' is_open,
                    a.attribute_id,
                    a.meta,
                    a.definition,
                    jsonb_object_agg(
                            'attribute_value_id_' || av.attribute_value_id,
                            jsonb_build_object(
                                    'meta', av.meta,
                                    'value', av.value,
                                    'display_order', av.display_order
                            )
                    ) AS attribute_values
                FROM
                    eav.subcategory s
                        INNER JOIN eav.subcategory_attribute sa ON s.subcategory_id = sa.subcategory_id
                        INNER JOIN eav.attribute a ON sa.attribute_id = a.attribute_id
                        INNER JOIN eav.attribute_value av ON a.attribute_id = av.attribute_id
                WHERE {conditions}
                  AND sa.filter_status != 'INACTIVE'
                GROUP BY 1, 2, 3, 4, 5, 6
            )
        SELECT
            mav.subcategory_id,
            jsonb_object_agg(
                    'attribute_id_' || mav.attribute_id,
                    jsonb_build_object(
                            'meta', mav.meta,
                            'definition', mav.definition,
                            'values', mav.attribute_values,
                            'display_order', mav.display_order,
                            'is_open', is_open
                    )
            ) AS eav_facet_definitions
        FROM attribute_value_projection mav
        GROUP BY mav.subcategory_id
        ;
        SQL;
        $conditions = Where::createWhereIn('s.subcategory_id', $subcategory_ids);

        $sql = strtr($sql, [
            '{conditions}' => $conditions,
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, $conditions->getValues())
            ->extract();
    }

    /**
     * findArticleFacetsByIdAndSkus.
     *
     * @throws ModelException
     */
    public function findArticleFacetsByIdAndSkus(int $subcategory_id, array $skus): array
    {
        // Additional conditions for subcategory are hard coded, facets always need to use filter and specific status
        $sql = <<<SQL
        WITH
            parameters AS (
                SELECT
                    unnest(ARRAY ['{skus}']::citext[]) AS sku
            ),
            article_attribute_projection AS (
                SELECT
                    pv.sku AS sku,
                    jsonb_build_object(
                            'attribute_id', av.attribute_id,
                            'attribute_values', array_agg(DISTINCT av.attribute_value_id)
                    ) AS attributes
                FROM parameters  p
                         INNER JOIN eav.product_value pv on p.sku = pv.sku
                         INNER JOIN eav.attribute_value av ON pv.attribute_value_id = av.attribute_value_id
                         INNER JOIN eav.subcategory_attribute sa ON av.attribute_id = sa.attribute_id
                         INNER JOIN eav.subcategory s ON s.subcategory_id = sa.subcategory_id
                WHERE {conditions}
                  AND s.use_filters
                  AND sa.filter_status != 'INACTIVE'
                GROUP BY pv.sku, av.attribute_id
            )
        SELECT p.sku, json_agg(attributes) filter ( where aap.sku is not null ) AS eav_facets
        FROM parameters p
            LEFT JOIN article_attribute_projection aap ON aap.sku = p.sku
        GROUP BY p.sku
        ORDER BY p.sku
        ;
        SQL;
        $conditions = new Where('s.subcategory_id = $*', [$subcategory_id]);

        $sql = strtr($sql, [
            '{skus}' => implode('\',\'', $skus),
            '{conditions}' => $conditions,
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, $conditions->getValues())
            ->extract();
    }

    /**
     * Retrieve subcategory collection related to an attribute.
     *
     * @return CollectionIterator
     *
     * @throws ModelException
     */
    public function findByRelatedAttribute(int $attribute_id)
    {
        $sql = <<<SQL
        SELECT {projection}
        FROM {subcategory} s
            INNER JOIN {subcategory_attribute} sa USING (subcategory_id)
        WHERE {condition}
        SQL;

        $projection = $projection = $this->createProjection();
        $where = Where::create('sa.attribute_id = $*', [$attribute_id]);
        $subcategory_attribute_model = $this->getSession()->getModel(SubcategoryAttributeModel::class);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('s'),
            '{subcategory}' => $this->structure->getRelation(),
            '{subcategory_attribute}' => $subcategory_attribute_model->getStructure()->getRelation(),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection);
    }
}
