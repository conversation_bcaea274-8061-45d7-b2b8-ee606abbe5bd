<?php

namespace App\Database\PgErpServer\EavSchema;

use App\Database\PgErpServer\EavSchema\AutoStructure\SubcategoryAttribute as SubcategoryAttributeStructure;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

/**
 * SubcategoryAttributeModel.
 *
 * Model class for table subcategory_attribute.
 *
 * @see Model
 */
class SubcategoryAttributeModel extends Model
{
    use WriteQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new SubcategoryAttributeStructure();
        $this->flexible_entity_class = '\\' . SubcategoryAttribute::class;
    }
}
