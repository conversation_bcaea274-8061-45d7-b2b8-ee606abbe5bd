<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\SellInLine as SellInLineStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;
use PommProject\ModelManager\Model\Model;

/**
 * SellInLineModel.
 *
 * Model class for table sell_in_line.
 *
 * @see Model
 */
class SellInLineModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new SellInLineStructure();
        $this->flexible_entity_class = SellInLine::class;
    }

    public function getExportSellInGenerator(int $days_since): \Generator
    {
        $sql = <<<'SQL'
        SELECT
            delivering_at::TIMESTAMP::TEXT AS "delivery_date",
            ordering_at::TIMESTAMP::TEXT AS "ordered_date",
            supplier_order_id,
            reference,
            ean_code,
            model,
            brand,
            domain,
            category,
            subcategory,
            supplier,
            quantity,
            purchase_cost,
            warehouse,
            weight
        FROM
            data.sell_in_line
        WHERE
            delivering_at::DATE < CAST(now() AS DATE)
            AND delivering_at::DATE >= CAST(now() - '%s DAYS'::INTERVAL AS DATE)
        ORDER BY
            sell_in_line_id ASC
        SQL;

        $collection = $this->getSession()
            ->getQueryManager()
            ->query(sprintf($sql, $days_since));

        foreach ($collection as $item) {
            yield $item;
        }
    }
}
