<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgDataWarehouse\DataSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * DataParameter.
 *
 * Structure class for relation data.data_parameter.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 *
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 * @see RowStructure
 */
class DataParameter extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('data.data_parameter')
            ->setPrimaryKey(['key'])
            ->addField('key', 'varchar')
            ->addField('value', 'varchar');
    }
}
