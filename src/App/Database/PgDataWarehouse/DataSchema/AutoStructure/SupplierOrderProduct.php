<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace App\Database\PgDataWarehouse\DataSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * SupplierOrderProduct.
 *
 * Structure class for relation data.supplier_order_product.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 *
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 * @see RowStructure
 */
class SupplierOrderProduct extends RowStructure
{
    /**
     * __construct.
     *
     * Structure definition.
     */
    public function __construct()
    {
        $this->setRelation('data.supplier_order_product')
            ->setPrimaryKey(['supplier_order_product_id', 'date'])
            ->addField('date', 'date')
            ->addField('supplier_id', 'int4')
            ->addField('supplier_order_id', 'int4')
            ->addField('supplier_order_product_id', 'int4')
            ->addField('product_id', 'int4')
            ->addField('ordered_quantity', 'numeric')
            ->addField('delivered_quantity', 'numeric')
            ->addField('expected_delivery_date', 'date')
            ->addField('actual_delivery_date', 'date')
            ->addField('dispute', 'bool')
            ->addField('buying_price', 'numeric');
    }
}
