<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\ArticleSalePrice as ArticleSalePriceStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;

/**
 * ArticleSalePriceModel.
 *
 * Model class for table product_sale_price.
 *
 * @see Model
 */
class ArticleSalePriceModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new ArticleSalePriceStructure();
        $this->flexible_entity_class = ArticleSalePrice::class;
    }
}
