<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\CustomerOrder as CustomerOrderStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Exception\InvalidParameterException;
use PommProject\ModelManager\Model\CollectionIterator;

/**
 * CustomerOrderModel.
 *
 * Model class for table customer_order.
 *
 * @see Model
 */
class CustomerOrderModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    public const COLUMN_MAPPING = [
        'source' => 'c.source',
        'source_group' => 'c.source_group',
        'country' => 'c.country',
        'created_at' => 'c.created_at',
        'order_status' => 'l.order_status',
        'reference' => 'l.reference',
        'model' => 'l.model',
        'brand' => 'l.brand',
        'supplier' => 'l.supplier',
        'subcategory' => 'l.subcategory',
        'category' => 'l.category',
        'domain' => 'l.domain',
        'carrier' => 'l.carrier',
        'warehouse' => 'l.warehouse',
        'invoiced_at' => 'l.invoiced_at',
    ];

    public function __construct()
    {
        $this->structure = new CustomerOrderStructure();
        $this->flexible_entity_class = CustomerOrder::class;
    }

    /**
     * Retreive statistics based on periods.
     *
     * @param array<string,\DateTimeInterface[]> $periods
     * @param string{'YEAR'|'WEEK'} $compare_with
     *
     * @return array{period:string,from:string,ca:array<string,float>,number:array<string,int>,margin:array<string,float>}[]
     */
    public function fetchAllStatisticsOnPeriods(
        array $periods,
        QueryBuilder $query_builder,
        array $pivots = [],
        array $compare_with = [],
        string $join_field = 'created_at'
    ): array {
        // SQL template
        $sql = <<<SQL
        SELECT
            pivot.period,
            pivot.start::TEXT AS "from",
            pivot.end::TEXT AS "to",
            JSON_BUILD_OBJECT({calculated_fields}) AS "current"
            {comparison_fields}
            {pivot}
        FROM (
            SELECT
                period,
                dates.start,
                dates.end
                {comparison_period}
            FROM( {period} ) dates
        ) pivot
        LEFT JOIN (
            SELECT
                c.customer_order_id,
                c.created_at,
                c.source,
                c.source_group,
                l.invoiced_at,
                l.total_net_excl_tax,
                l.total_margin,
                l.order_status,
                l.warehouse,
                l.carrier
            FROM data.customer_order c
            LEFT JOIN data.customer_order_line l ON l.deleted_at IS NULL AND c.customer_order_id = l.customer_order_id
            WHERE {conditions}
        ) c ON {join_field} >= pivot.start AND {join_field} < pivot.end
        {comparison_join}
        GROUP BY pivot.period, pivot.start, pivot.end{pivot}
        ORDER BY pivot.start DESC, (pivot.end - pivot.start) ASC
        SQL;

        // declare calculated fields
        $calculated_fields = [
            'turnover' => 'ROUND(COALESCE(SUM(CASE WHEN {condition} THEN c.total_net_excl_tax ELSE 0 END)))',
            'number' => 'COUNT(DISTINCT CASE WHEN {condition} THEN c.customer_order_id ELSE NULL END)',
            'margin' => 'ROUND(COALESCE(SUM(CASE WHEN {condition} THEN c.total_margin ELSE 0 END)))',
        ];

        if ('TRUE' === $query_builder->hasIncludedDependency('customer_order_ids')) {
            $calculated_fields['customer_order_ids'] =
                'COALESCE(JSON_AGG(DISTINCT c.customer_order_id) FILTER (WHERE {condition}), \'[]\'::JSON)';
        }

        $sql = strtr($sql, [
            // add Periods the query is based from
            '{period}' => $this->parsePeriods($periods),
            // add calculated field for the current period
            '{calculated_fields}' => $this->parseCalculatedFields(
                $calculated_fields,
                '{join_field} >= pivot.start AND {join_field} < pivot.end'
            ),
            // add regroupment criterions
            '{pivot}' => $this->parsePivots($pivots),
        ]);

        return $this->executeQuery(
            // add needed comparison elements
            $this->parseComparisons($sql, $compare_with, $join_field, $calculated_fields),
            $query_builder
        );
    }

    /**
     * Generate calculated fields statement with conditions.
     *
     * @param array<string,string> $fields
     * */
    private function parseCalculatedFields(array $fields, string $condition): string
    {
        $calculated_fields = [];
        foreach ($fields as $alias => $function) {
            $calculated_fields[] = sprintf('\'%s\', %s', $alias, $function);
        }

        return strtr(implode(', ', $calculated_fields), ['{condition}' => $condition]);
    }

    /**
     * Add comparisons SQL.
     *
     * @param array<string,string> $comparisons
     * @param array<string,string> $comparison_fields
     *
     * @throws InvalidParameterException
     */
    private function parseComparisons(
        string $sql,
        array $comparisons,
        string $join_field,
        array $calculated_fields
    ): string {
        foreach ($comparisons as $name => $comparison) {
            if (!preg_match('~^[a-z0-9_]+$~', $name)) {
                throw new InvalidParameterException(sprintf('Comparison name must have the [a-z0-9_]+ pattern: "%s" given', $name));
            }

            $authorized_delay = ['YEAR', 'QUARTER', 'MONTH', 'WEEK', 'DAY'];
            if (!preg_match('~^[\d]+ (?:' . implode(')|(?:', $authorized_delay) . ')$~', $comparison)) {
                throw new InvalidParameterException(sprintf('Comparison delay must have the [\d]+ ' . implode('|', $authorized_delay) . ' pattern: "%s" given', $comparison));
            }

            $calculated = $this->parseCalculatedFields(
                $calculated_fields,
                <<<SQL
                    {join_field} >= pivot.start_$name AND {join_field} < pivot.end_$name
                SQL
            );

            $sql = strtr($sql, [
                '{comparison_period}' => <<<SQL
                    , dates.start - '$comparison'::INTERVAL AS "start_$name", dates.end - '$comparison'::INTERVAL AS "end_$name"{comparison_period}
                SQL
                ,
                '{comparison_fields}' => <<<SQL
                    , JSON_STRIP_NULLS(JSON_BUILD_OBJECT($calculated)) AS "$name"{comparison_fields}
                SQL
                ,
                '{comparison_join}' => <<<SQL
                     OR ({join_field} >= pivot.start_$name AND {join_field} < pivot.end_$name){comparison_join}
                SQL
            ]);
        }

        return strtr(preg_replace('~{comparison_[a-z]+}~', '', $sql), [
            '{join_field}' => substr(
                static::COLUMN_MAPPING[$join_field],
                strrpos(static::COLUMN_MAPPING[$join_field], '.') + 1
            ),
        ]);
    }

    /**
     * Create the select sql periods.
     *
     * @param array<string,\DateTimeInterface[]> $periods
     */
    private function parsePeriods(array $periods): string
    {
        $sql_period = [];
        foreach ($periods as $name => $period) {
            $sql_period[] = sprintf(
                'SELECT %s AS "period", \'%s\'::DATE AS "start", CAST(\'%s\'::DATE + \'1 DAY\'::INTERVAL AS DATE) AS "end"',
                $this->escapeLiteral($name),
                $period[0]->format('Y-m-d H:i:s'),
                $period[1]->format('Y-m-d H:i:s')
            );
        }

        return implode(' UNION ALL ', $sql_period);
    }

    /**
     * Transforme pivots to sql statment.
     *
     * @param string[] $pivots
     */
    private function parsePivots(array $pivots): string
    {
        $pivots = array_filter(
            array_map(function ($item) {
                if ($column = static::COLUMN_MAPPING[$item] ?? null) {
                    $column = substr($column, strrpos($column, '.') + 1);
                }

                return $column;
            }, $pivots)
        );

        return [] === $pivots ? '' : ', ' . implode(', ', $pivots);
    }

    /** Transform and execute a pgsql query */
    public function executeQuery(string $sql, QueryBuilder $query_builder): array
    {
        $sql = strtr($sql, [
            '{conditions}' => preg_replace('#:([\w]+)#', '$*', $query_builder->getWhere()),
        ]);

        /** @var CollectionIterator $collection */
        $collection = $this->getSession()
            ->getQueryManager()
            ->query($sql, array_values($query_builder->getWhereParameters()));

        return $collection->extract();
    }

    /**
     * Queries sales data from the pgsql database.
     *
     * @return Generator<{
     * date:string,
     * year:string,
     * month:string,
     * customer_order_id:int,
     * reference:string,
     * brand:string,
     * domain:string,
     * category:string,
     * subcategory:string,
     * supplier:string,
     * origin:string,
     * country:string,
     * postal_code:string,
     * quantity:int,
     * total_purchase_cost:float,
     * total_gross_excl_tax:float,
     * total_discount_excl_tax:float,
     * total_net_excl_tax:float,
     * total_guarantee_excl_tax:float,
     * total_margin:float,
     * model:string,
     * is_btob:0|1,
     * is_internal:0|1,
     * source:string,
     * source_group:string,
     * sales_location?:string,
     * ean_code:string,
     * created_by?:string,
     * is_destock:0|1
     * order_status:string,
     * carrier:string,
     * warehouse:string
     * }>
     */
    public function getExportSalesGenerator(int $days_since): \Generator
    {
        $sql = <<<'SQL'
        SELECT
            c.created_at::date::TEXT AS "date",
            EXTRACT('Year' FROM c.created_at) AS "year",
            EXTRACT('Month'FROM c.created_at) AS "month",
            c.customer_order_id,
            l.reference,
            l.brand,
            l.domain,
            l.category,
            l.subcategory,
            l.supplier,
            c.origin,
            c.country,
            c.postal_code,
            l.quantity,
            l.total_purchase_cost,
            l.total_gross_excl_tax,
            l.total_discount_excl_tax,
            l.total_net_excl_tax,
            l.total_guarantee_excl_tax,
            l.total_margin,
            l.model,
            c.is_btob::INT,
            c.is_internal::INT,
            c.source,
            c.source_group,
            c.sales_location,
            l.ean_code,
            c.created_by,
            CASE WHEN SUBSTRING(l.reference FOR 7) = 'DESTOCK' THEN 1 ELSE 0 END AS "is_destock",
            l.order_status,
            l.carrier,
            l.warehouse,
            l.weight,
            l.supplier_reference
        FROM
            data.customer_order_line l
        INNER JOIN data.customer_order c ON
            c.customer_order_id = l.customer_order_id
        WHERE
            c.created_at::DATE < CAST(now() AS DATE)
            AND c.created_at::DATE >= CAST(now() - '%s DAYS'::INTERVAL AS DATE)
            AND l.order_status NOT IN ('Erreur', 'Ouverte', 'Annulée', 'Supprimée')
            AND l.deleted_at IS NULL
        ORDER BY
            c.created_at ASC
        SQL;

        $collection = $this->getSession()
            ->getQueryManager()
            ->query(sprintf($sql, $days_since));

        foreach ($collection as $item) {
            yield $item;
        }
    }

    /**
     * Queries customer orders data from the pgsql database.
     *
     * @return Generator<{
     *  date:string,
     *  year:string,
     *  month:string,
     *  week:string,
     *  time:string,
     *  customer_order_id:int,
     *  customer_id:int,
     *  is_btob:0|1,
     *  country:string,
     *  postal_code:string,
     *  department:int,
     *  age:int,
     *  gender:string,
     *  customer_since:string,
     *  shipping_is_billing:0|1,
     *  total_amount_excl_tax_incl_shipping:float,
     *  shipping_cost:float,
     *  carrier_selected:string,
     *  payment_method:sring,
     *  payment_delay_in_days:int,
     *  shipping_delay_in_days:int,
     *  sku_count:int,
     *  promo_code:string,
     *  total_discount:float,
     *  customer_orders_count:int,
     *  customer_orders_amount:float,
     *  source:string,
     *  source_group:string,
     *  origin:string,
     *  created_by:string
     * }>
     */
    public function getExportCustomerOrdersGenerator(int $days_since): \Generator
    {
        $sql = <<<'SQL'
        SELECT
            c.created_at::DATE::TEXT AS "date",
            EXTRACT('Year' FROM c.created_at) AS "year",
            EXTRACT('Month'FROM c.created_at) AS "month",
            EXTRACT('Week'FROM c.created_at) AS "week",
            SUBSTRING(c.created_at::time::TEXT, 1, 5) AS "time",
            c.customer_order_id AS "customer_order_id",
            c.customer_id AS "customer_id",
            c.is_btob::INT AS "is_btob",
            c.country AS "country",
            c.postal_code AS "postal_code",
            CASE WHEN (c.country='France') THEN SUBSTRING(c.postal_code, 1, 2) END AS "department",
            c.customer_age AS "age",
            c.customer_gender AS "gender",
            c.customer_since::DATE::TEXT AS "customer_since",
            c.shipping_is_billing::INT AS "shipping_is_billing",
            SUM(l.total_net_excl_tax) + SUM(l.total_guarantee_excl_tax) + COALESCE(c.shipping_cost, 0) AS "total_amount_excl_tax_incl_shipping",
            c.shipping_cost AS "shipping_cost",
            c.carrier_selected AS "carrier_selected",
            c.payment_method AS "payment_method",
            c.payment_accepted_at::DATE - c.created_at::DATE AS "payment_delay_in_days",
            MAX(l.shipped_at)::DATE - c.payment_created_at::DATE AS "shipping_delay_in_days",
            COUNT(DISTINCT l.reference) AS "sku_count",
            c.promo_code AS "promo_code",
            SUM(l.total_discount_excl_tax) AS "total_discount",
            MAX(t.customer_orders_count) AS "customer_orders_count",
            MAX(t.customer_orders_amount) AS "customer_orders_amount",
            c.source AS "source",
            c.source_group AS "source_group",
            c.sales_location AS "sales_location",
            c.created_by AS "created_by"
        FROM
            data.customer_order c
        INNER JOIN
            data.customer_order_line l ON l.customer_order_id = c.customer_order_id
        INNER JOIN (
            SELECT
                  COUNT(DISTINCT c.customer_order_id) AS "customer_orders_count",
                  SUM(l.total_net_excl_tax) + SUM(l.total_guarantee_excl_tax) + SUM(COALESCE(c.shipping_cost, 0)) AS "customer_orders_amount",
                  c.customer_hash
            FROM data.customer_order c
            INNER JOIN data.customer_order_line l ON l.customer_order_id = c.customer_order_id
            WHERE
               c.created_at::DATE < CAST(now() AS DATE)
                AND l.order_status NOT IN ('Erreur', 'Ouverte', 'Annulée', 'Supprimée')
                AND l.deleted_at IS NULL
            GROUP BY c.customer_hash
        ) t ON t.customer_hash = c.customer_hash
        WHERE
            c.created_at::DATE < CAST(now() AS DATE)
            AND c.created_at::DATE >= CAST(now() - '%s DAYS'::INTERVAL AS DATE)
            AND l.order_status NOT IN ('Erreur', 'Ouverte', 'Annulée', 'Supprimée')
            AND l.deleted_at IS NULL
        GROUP BY c.customer_order_id
        ORDER BY
            c.created_at ASC
        SQL;

        $collection = $this->getSession()
            ->getQueryManager()
            ->query(sprintf($sql, $days_since));

        foreach ($collection as $item) {
            yield $item;
        }
    }

    /**
     * Queries Group Digital VTS data (past week sales) from the pgsql database.
     *
     * @return Generator<{
     *  shop:string,
     *  week:int,
     *  sunday:string,
     *  reference:string,
     *  ean:int,
     *  supplier:string,
     *  brand:string,
     *  quantity:int
     * }>
     */
    public function getExportGroupDigitalVTSGenerator($code_magasin): \Generator
    {
        $sql = <<<'SQL'
        SELECT
            %s AS "shop",
            TO_CHAR(CURRENT_DATE - EXTRACT('dow' FROM CURRENT_DATE)::INT, 'YYYYWW') AS "week",
            TO_CHAR(CURRENT_DATE - EXTRACT('dow' FROM CURRENT_DATE)::INT, 'DD/MM/YYYY') AS "sunday",
            l.reference AS "reference",
            l.ean_code AS "ean",
            l.supplier AS "supplier",
            l.brand AS "brand",
            SUM(l.quantity) AS "quantity"
        FROM
            data.customer_order c
        INNER JOIN
            data.customer_order_line l ON l.customer_order_id = c.customer_order_id
        WHERE
            c.created_at::DATE <= CURRENT_DATE - EXTRACT('dow' FROM CURRENT_DATE)::INT
            AND c.created_at::DATE > CURRENT_DATE - EXTRACT('dow' FROM CURRENT_DATE)::INT - '1 WEEK'::INTERVAL
            AND l.order_status NOT IN ('Erreur', 'Ouverte', 'Annulée', 'Supprimée')
            AND l.deleted_at IS NULL
            AND l.ean_code NOT LIKE 'DESTOCK-%%'
        GROUP BY l.reference, l.supplier,l.brand, l.ean_code
        ORDER BY
            l.reference ASC;
        SQL;

        $collection = $this->getSession()
            ->getQueryManager()
            ->query(sprintf($sql, $code_magasin));

        foreach ($collection as $item) {
            yield $item;
        }
    }

    /**
     * Queries Group Digital CA data (daily sales) from the pgsql database.
     *
     * @return Generator<{
     *  shop:string,
     *  date:string,
     *  reference:string,
     *  ean:int,
     *  supplier:string,
     *  brand:string,
     *  product_range:string,
     *  quantity:int,
     *  total_net_excl_tax:float,
     *  total_net_incl_tax:float
     * }>
     */
    public function getExportGroupDigitalCAGenerator(string $code_magasin, string $from_date): \Generator
    {
        $sql = <<<'SQL'
        SELECT
            %s AS "shop",
            TO_CHAR(c.created_at::DATE, 'DD/MM/YYYY') AS "date",
            l.reference AS "reference",
            l.ean_code AS "ean",
            l.supplier AS "supplier",
            l.brand AS "brand",
            '' AS "product_range",
            SUM(l.quantity) AS "quantity",
            SUM(l.total_net_excl_tax) AS "total_net_excl_tax",
            SUM(ROUND(l.total_net_excl_tax*(1 + l.vat_rate), 2)) AS "total_net_incl_tax"
        FROM
            data.customer_order c
        INNER JOIN
            data.customer_order_line l ON l.customer_order_id = c.customer_order_id
        WHERE
            c.created_at::DATE >= '%s 06:00'
            AND c.created_at < CURRENT_DATE::DATE + '6 HOUR'::INTERVAL
            AND l.order_status NOT IN ('Erreur', 'Ouverte', 'Annulée', 'Supprimée')
            AND l.deleted_at IS NULL
            AND l.ean_code NOT LIKE 'DESTOCK-%%'
        GROUP BY c.created_at::DATE, l.reference, l.supplier,l.brand, l.ean_code
        HAVING SUM(l.quantity) <> 0
            AND SUM(l.total_net_excl_tax) <> 0
        ORDER BY
            c.created_at::DATE ASC, l.reference ASC;
        SQL;

        $collection = $this->getSession()
            ->getQueryManager()
            ->query(sprintf($sql, $code_magasin, $from_date));

        foreach ($collection as $item) {
            yield $item;
        }
    }

    /**
     * Fetch paginated order IDs.
     *
     * @return array{customer_order_id:int, carrier_selected:string, payment_method:string, postal_code:string,
     *                                      country:string, total_count:int, status:string}[]
     */
    public function fetchPaginatedByStatus(QueryBuilder $query_builder): array
    {
        $sql = <<<SQL
        SELECT
            c.customer_order_id,
            c.carrier_selected,
            c.payment_method,
            c.postal_code,
            c.country,
            l.order_status,
            COUNT(*) OVER() AS total_count
        FROM data.customer_order c
        RIGHT JOIN data.customer_order_line l ON l.deleted_at IS NULL AND c.customer_order_id = l.customer_order_id
        WHERE {conditions}
        GROUP BY c.customer_order_id, l.order_status
        {order_by}
        LIMIT {limit} OFFSET {offset}
        SQL;

        $sql = strtr($sql, [
            '{order_by}' => $query_builder->getOrderBy(),
            '{limit}' => $query_builder->getLimit(),
            '{offset}' => ($query_builder->getOffset() - 1) * $query_builder->getLimit(),
        ]);

        return $this->executeQuery($sql, $query_builder);
    }
}
