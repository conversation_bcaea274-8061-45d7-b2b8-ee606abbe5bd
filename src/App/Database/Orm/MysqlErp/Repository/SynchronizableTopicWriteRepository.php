<?php

namespace App\Database\Orm\MysqlErp\Repository;

use App\Database\ConnectionProvider\AbstractMysqlErpRepository;
use App\Database\Orm\MysqlErp\Repository\Entity\SynchronizableTopic;
use App\Database\Orm\MysqlErp\Repository\Model\SynchronizableTopicModel;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicDequeueParameters;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

class SynchronizableTopicWriteRepository extends AbstractMysqlErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = SynchronizableTopicModel::class;
    public const ENTITY_NAME = SynchronizableTopic::class;

    public function lockForSynchronization(
        int $amount_already_locked,
        SynchronizableTopicDequeueParameters $parameters
    ): void {
        if (null !== $parameters->synchronizable_topic_id) {
            $this->updateWhere(
                [
                    'status' => 'LOCKED',
                ],
                'synchronizable_topic_id = :synchronizable_topic_id',
                [
                    'synchronizable_topic_id' => $parameters->synchronizable_topic_id,
                ]
            );

            return;
        }

        $amount_to_lock = $parameters->limit - $amount_already_locked;

        if ($amount_to_lock <= 0) {
            return;
        }

        $sql = <<<'MYSQL'
        UPDATE backOffice.synchronizable_topic
        SET
          status = 'LOCKED'
          WHERE synchronizable_topic_id IN (
            SELECT *
              FROM
                (
                  SELECT synchronizable_topic_id
                    FROM backOffice.synchronizable_topic
                    WHERE status = 'CREATED'
                    AND NOW() >= wait_until
                    ORDER BY wait_until
                    LIMIT {amount_to_lock}
                  ) tmp
            );
        MYSQL;

        $this->getConnection()->executeStatement(strtr($sql, ['{amount_to_lock}' => $amount_to_lock]));
    }

    public function markAsAttempted(int $synchronizable_topic_id): void
    {
        // using expression are kinda tricky for the time being therefore i'm not using
        // the updateWhere() method for this simple query
        $sql = <<<'MYSQL'
        UPDATE backOffice.synchronizable_topic
        SET
          updated_at = NOW(),
          attempt = attempt + 1
          WHERE synchronizable_topic_id = :synchronizable_topic_id;
        MYSQL;

        $this->getConnection()->executeStatement($sql, ['synchronizable_topic_id' => $synchronizable_topic_id]);
    }

    public function removeSynchronizedTopic(int $synchronizable_topic_id): void
    {
        $this->deleteWhere('synchronizable_topic_id = :synchronizable_topic_id', [
            'synchronizable_topic_id' => $synchronizable_topic_id,
        ]);
    }
}
