<?php

declare(strict_types=1);

namespace App\Database\Orm\MysqlErp\Repository\Model;

use SonVideo\Orm\Converter\Type\JsonConverter;
use SonVideo\Orm\Definition as ORM;
use SonVideo\Orm\Definition\SGBDEngine;

/**
 * @ORM\Model(name="backOffice.synchronizable_topic", engine=SGBDEngine::MYSQL)
 */
final class SynchronizableTopicModel
{
    /** @ORM\Column(primary_key=true) */
    public int $synchronizable_topic_id;

    /** @ORM\Column */
    public string $target;

    /** @ORM\Column */
    public string $topic;

    /** @ORM\Column(type=JsonConverter::class) */
    public array $content;

    /** @ORM\Column */
    public string $status;

    /** @ORM\Column */
    public \DateTimeInterface $created_at;

    /** @ORM\Column */
    public \DateTimeInterface $updated_at;

    /** @ORM\Column */
    public \DateTimeInterface $wait_until;

    /** @ORM\Column */
    public ?int $attempt = 1;
}
