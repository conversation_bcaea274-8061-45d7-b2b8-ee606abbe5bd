<?php

declare(strict_types=1);

namespace App\Database\Orm\MysqlErp\Repository\Entity;

class SynchronizableTopic
{
    public int $synchronizable_topic_id;
    public string $target;
    public string $topic;
    public array $content;
    public string $status;
    public \DateTimeInterface $created_at;
    public \DateTimeInterface $updated_at;
    public \DateTimeInterface $wait_until;
    public ?int $attempt = 1;

    public function consolidated(): array
    {
        return array_merge(
            ['synchronizable_topic_id' => $this->synchronizable_topic_id, 'topic' => $this->topic],
            $this->content
        );
    }
}
