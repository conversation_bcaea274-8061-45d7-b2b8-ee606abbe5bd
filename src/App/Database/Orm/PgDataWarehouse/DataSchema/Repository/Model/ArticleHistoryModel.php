<?php

declare(strict_types=1);
/**
 * ArticleHistory Definition.
 *
 * Structure class for relation data.article_history.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Converter\Type\PgTimestampConverter;
use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.article_history.
 *
 * @ORM\Model(name="data.article_history", engine="postgresql")
 */
class ArticleHistoryModel
{
    /** @ORM\Column(primary_key=true) */
    public int $event_id;

    /** @ORM\Column */
    public ?string $event_type = null;

    /** @ORM\Column */
    public ?string $sku = null;

    /** @ORM\Column */
    public ?string $event_author = null;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $event_date = null;
}
