<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity;

class CustomerOrderInitialLine
{
    public int $customer_order_product_initial_id;
    public ?string $reference = null;
    public ?string $model = null;
    public ?string $brand = null;
    public ?string $supplier = null;
    public ?int $quantity = null;
    public ?float $total_purchase_cost = null;
    public ?float $total_gross_excl_tax = null;
    public ?float $total_discount_excl_tax = null;
    public ?float $total_net_excl_tax = null;
    public ?float $total_guarantee_excl_tax = null;
    public ?float $total_margin = null;
    public ?string $subcategory = null;
    public ?int $subcategory_id = null;
    public ?string $category = null;
    public ?int $category_id = null;
    public ?string $domain = null;
    public ?int $domain_id = null;
    public ?int $customer_order_id = null;
    public ?float $vat_rate = null;
    public ?string $promo_code = null;
    public ?string $product_type = null;
    public ?int $available_quantity = null;
    public ?\DateTimeInterface $estimated_supplier_order_delivery_date = null;
    public ?\DateTimeInterface $customer_order_created_at = null;
}
