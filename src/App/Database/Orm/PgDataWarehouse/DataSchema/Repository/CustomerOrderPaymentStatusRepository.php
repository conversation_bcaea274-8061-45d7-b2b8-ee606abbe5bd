<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgDataWarehouseRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrderPaymentStatus;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model\CustomerOrderPaymentStatusModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class CustomerOrderPaymentStatusRepository extends AbstractPgDataWarehouseRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = CustomerOrderPaymentStatusModel::class;
    public const ENTITY_NAME = CustomerOrderPaymentStatus::class;
}
