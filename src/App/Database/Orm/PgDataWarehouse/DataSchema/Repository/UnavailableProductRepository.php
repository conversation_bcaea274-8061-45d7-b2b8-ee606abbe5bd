<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgDataWarehouseRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\UnavailableProduct;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model\UnavailableProductModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class UnavailableProductRepository extends AbstractPgDataWarehouseRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = UnavailableProductModel::class;
    public const ENTITY_NAME = UnavailableProduct::class;
}
