<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\EavSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation eav.product_value.
 *
 * @ORM\Model(name="eav.product_value", engine="postgresql")
 */
class ProductValueModel
{
    /** @ORM\Column(primary_key=true) */
    public string $sku;

    /** @ORM\Column(primary_key=true) */
    public int $attribute_value_id;
}
