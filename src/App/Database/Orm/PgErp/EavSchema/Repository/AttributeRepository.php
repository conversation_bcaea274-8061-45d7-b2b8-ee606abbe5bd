<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\EavSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgErpRepository;
use App\Database\Orm\PgErp\EavSchema\Repository\Entity\Attribute;
use App\Database\Orm\PgErp\EavSchema\Repository\Model\AttributeModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class AttributeRepository extends AbstractPgErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = AttributeModel::class;
    public const ENTITY_NAME = Attribute::class;
}
