<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\SystemSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgErpRepository;
use App\Database\Orm\PgErp\SystemSchema\Repository\Entity\Parameter;
use App\Database\Orm\PgErp\SystemSchema\Repository\Model\ParameterModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class ParameterRepository extends AbstractPgErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = ParameterModel::class;
    public const ENTITY_NAME = Parameter::class;
}
