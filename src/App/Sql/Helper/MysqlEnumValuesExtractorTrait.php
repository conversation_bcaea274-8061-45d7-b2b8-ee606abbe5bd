<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sql\Helper;

trait MysqlEnumValuesExtractorTrait
{
    private function extractEnumValuesFrom(string $sql_definition): array
    {
        $matches = [];
        preg_match_all("/(?<=[(',])([^',)]+)(?=[',)])/", $sql_definition, $matches);

        return $matches[1];
    }
}
