<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sql\Query\Where;

use App\Contract\QueryColumnMappingInterface;

/**
 * Class WhereQueryBuilder.
 */
class WhereQueryBuilder
{
    private const DEFAULT_CONDITION_HANDLER_KEY = '_and';

    protected $condition_handlers = [];
    protected $operator_handlers = [];
    private array $binded_parameters = [];

    /** WhereQueryBuilder constructor. */
    public function __construct(iterable $query_conditions, iterable $query_operators)
    {
        $this->collectHandlers($query_conditions, 'condition_handlers')->collectHandlers(
            $query_operators,
            'operator_handlers'
        );
    }

    /** collectHandlers */
    protected function collectHandlers(iterable $handlers, string $target_collections_key): WhereQueryBuilder
    {
        /*
         * Map handled condition in order to speed condition matching when building up the where clause
         */
        foreach ($handlers as $handler) {
            $handler->setWhereQueryBuilder($this);

            foreach ($handler->getKeys() as $key) {
                $this->{$target_collections_key}[$key] = $handler;
            }
        }

        return $this;
    }

    public function setColumnsMapping(array $columns_mapping = []): WhereQueryBuilder
    {
        foreach ($this->operator_handlers as $operator_handler) {
            if ($operator_handler instanceof QueryColumnMappingInterface) {
                $operator_handler->setColumnsMappings($columns_mapping);
            }
        }

        return $this;
    }

    /**
     * This is a method for convenience
     * This method should be called by the parent class needing a where conditions
     * it returns the statement as an immediately usable string using an AND as default condition splitter.
     *
     * @throws \Exception
     */
    public function build(array $filters): string
    {
        return implode(' AND ', $this->parse($filters));
    }

    /**
     * The parse method should be called by classes within the build process (Operators, Conditions)
     * It returns an array that the children classes can handle in context with what their own logic.
     *
     * @throws \Exception
     */
    public function parse(array $filters): array
    {
        $where_clause = [];

        foreach ($filters as $key => $conditions) {
            $where_clause[] = $this->handle($key, $conditions);
        }

        return $where_clause;
    }

    /**
     * The handle method is called on per filter/line basis
     * It dispatches each line to the proper handler which can be either a Condition or an Operator.
     *
     * @param $key
     * @param $value
     *
     * @return mixed
     *
     * @throws \Exception
     */
    public function handle($key, $value)
    {
        if (is_int($key)) {
            return $this->condition_handlers[self::DEFAULT_CONDITION_HANDLER_KEY]->handle(
                self::DEFAULT_CONDITION_HANDLER_KEY,
                $value
            );
        }

        if (array_key_exists($key, $this->condition_handlers)) {
            return $this->condition_handlers[$key]->handle($key, $value);
        }

        if (!is_array($value)) {
            throw new \Exception(sprintf('Cannot bind key "%s" as no handler was specified.', $key));
        }

        if (1 !== count($value)) {
            throw new \Exception(sprintf('Key "%s" should contain exactly one handler, "%d" were given.', $key, count($value)));
        }

        $operator_key = key($value);
        if (array_key_exists($operator_key, $this->operator_handlers)) {
            $binded_value = current($value);
            $binded_parameter_name = null;

            // PDO does not accept arrays as bind values
            if (!is_array($binded_value)) {
                $binded_parameter_name = $this->addBindParameter($key, $binded_value);
            }

            return $this->operator_handlers[$operator_key]->handle(
                $key,
                $operator_key,
                $binded_value,
                $binded_parameter_name
            );
        }

        return null;
    }

    /**
     * addBindParameter.
     *
     * @param $key
     * @param $value
     */
    public function addBindParameter($key, $value): string
    {
        $this->binded_parameters[$key][] = $value;

        return "{$key}_" . max(array_keys($this->binded_parameters[$key]));
    }

    /** getBindedParameters */
    public function getBindedParameters(): array
    {
        $binded_parameters = [];

        foreach ($this->binded_parameters as $field => $parameters) {
            foreach ($parameters as $i => $parameter) {
                $binded_parameters["{$field}_{$i}"] = $parameter;
            }
        }

        return $binded_parameters;
    }

    /** reset */
    public function reset(): WhereQueryBuilder
    {
        $this->binded_parameters = [];

        return $this;
    }
}
