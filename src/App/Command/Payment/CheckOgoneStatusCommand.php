<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Payment;

use App\Command\MonitorizedCommand;
use SonVideo\Erp\Manager\Payment\OgoneStatusManager;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class CheckOgoneStatusCommand.
 */
class CheckOgoneStatusCommand extends MonitorizedCommand
{
    protected static $defaultName = 'payment:check-ogone-status';

    private const CHECK_UUID = '9c1e5b14-5ec7-441e-8721-d0f26fa318fb';

    private OgoneStatusManager $manager;

    /** CheckOgoneStatusCommand constructor. */
    public function __construct(OgoneStatusManager $manager, string $server_env, string $name = null)
    {
        parent::__construct($server_env, $name);
        $this->manager = $manager;
    }

    /** configure */
    protected function configure()
    {
        $this->setDescription('Check Ogone payment status for autoreponse.');
    }

    /**
     * execute.
     *
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $this->manager->check();

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
