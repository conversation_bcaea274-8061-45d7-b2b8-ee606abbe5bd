<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Marketplace\Cilo;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Export\Cilo\Manager\StockPrice;
use SonVideo\Erp\Filesystem\Manager\CiloFtp;
use SonVideo\Erp\Filesystem\Manager\CiloS3;
use SonVideo\Erp\Referential\Marketplace\Cilo;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class CiloStockPriceExportCommand.
 */
class CiloStockPriceExportCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    protected static $defaultName = 'cilo:stock_price:export';

    protected StockPrice $stock_price;
    protected CiloS3 $cilo_s3;
    protected CiloFtp $cilo_ftp;

    /** CiloStockPriceExportCommand constructor. */
    public function __construct(StockPrice $stock_price, CiloS3 $cilo_s3, CiloFtp $cilo_ftp, string $name = null)
    {
        parent::__construct($name);
        $this->stock_price = $stock_price;
        $this->cilo_s3 = $cilo_s3;
        $this->cilo_ftp = $cilo_ftp;
    }

    /** {@inheritDoc} */
    protected function configure()
    {
        $this->setDescription('Generate an XML stock prices of our products and put it on Cilo FTP server.');
    }

    /**
     * {@inheritDoc}
     *
     * @return int|void|null
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $time_start = time();
        $output->writeln('Job starts...');

        $xml = $this->stock_price->load()->exportAsXml();
        $this->cilo_s3->createOrOverwrite(Cilo::STOCK_PRICE_DIRECTORY, StockPrice::FILENAME, $xml);
        $this->cilo_ftp->createOrOverwrite(Cilo::STOCK_PRICE_DIRECTORY, StockPrice::FILENAME, $xml);

        $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

        return 0;
    }
}
