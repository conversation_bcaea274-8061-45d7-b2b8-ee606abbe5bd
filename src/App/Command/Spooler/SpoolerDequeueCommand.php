<?php

namespace App\Command\Spooler;

use App\Sentry\SentryCronJobMonitor;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Spooler\Manager\SpoolerDequeuer;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\FlockStore;

class SpoolerDequeueCommand extends Command
{
    public $logger;
    protected static $defaultName = 'spooler:dequeue';

    private SentryCronJobMonitor $sentry_cron_job_monitor;
    private SpoolerDequeuer $spooler_dequeuer;

    public function __construct(
        SentryCronJobMonitor $sentry_cron_job_monitor,
        SpoolerDequeuer $spooler_dequeuer,
        LoggerInterface $logger,
        string $name = null
    ) {
        parent::__construct($name);
        $this->sentry_cron_job_monitor = $sentry_cron_job_monitor;
        $this->spooler_dequeuer = $spooler_dequeuer;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this->setDescription('Dequeue spooler actions');
    }

    /** @throws \JsonException */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // START LOCK - prevent command to run in parallel processes
        // TODO extract when the solution has been validated
        $factory = new LockFactory(new FlockStore());
        $lock = $factory->createLock('spooler-dequeue');

        if (!$lock->acquire()) {
            $this->logger->warning('Command still running in another process');

            return 0;
        }
        // END LOCK

        $this->sentry_cron_job_monitor->start('spooler-dequeue', ['dev', 'prod']);

        try {
            $time_start = time();
            $output->writeln('Job starts...');

            $this->spooler_dequeuer->dequeue();

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

            $this->sentry_cron_job_monitor->end();
        } catch (\Exception $e) {
            $this->sentry_cron_job_monitor->fail();

            throw $e;
        }

        return 0;
    }
}
