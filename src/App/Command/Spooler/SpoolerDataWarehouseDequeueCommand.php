<?php

namespace App\Command\Spooler;

use App\Adapter\Serializer\SerializerInterface;
use App\Sentry\SentryCronJobMonitor;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicDequeueParameters;
use Son<PERSON>ideo\Erp\DataWarehouse\Manager\SynchronizableTopicCommandFeedbackHandler;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopicSpoolerDequeuer;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\FlockStore;

class SpoolerDataWarehouseDequeueCommand extends Command
{
    protected static $defaultName = 'spooler:data-warehouse:dequeue';

    private SentryCronJobMonitor $sentry_cron_job_monitor;
    private SynchronizableTopicSpoolerDequeuer $generic_log_spooler_dequeuer;
    private SerializerInterface $serializer;
    private LoggerInterface $logger;

    public function __construct(
        SentryCronJobMonitor $sentry_cron_job_monitor,
        SynchronizableTopicSpoolerDequeuer $generic_log_spooler_dequeuer,
        SerializerInterface $serializer,
        LoggerInterface $logger,
        string $name = null
    ) {
        parent::__construct($name);
        $this->sentry_cron_job_monitor = $sentry_cron_job_monitor;
        $this->generic_log_spooler_dequeuer = $generic_log_spooler_dequeuer;
        $this->serializer = $serializer;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this->setDescription('Dequeue spooler data-warehouse enqueued topics')
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Maximum number of topics to dequeue', 10000)
            ->addOption('topic-id', 't', InputOption::VALUE_OPTIONAL, 'Specific synchronizable_topic_id to dequeue');
    }

    /** @throws \JsonException */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // START LOCK - prevent command to run in parallel processes
        // TODO extract when the solution has been validated
        $factory = new LockFactory(new FlockStore());
        $lock = $factory->createLock('spooler-data-warehouse-dequeue');

        if (!$lock->acquire()) {
            $this->logger->warning('Command still running in another process');

            return 0;
        }
        // END LOCK

        $this->sentry_cron_job_monitor->start('spooler-data-warehouse-dequeue', ['dev', 'prod']);

        try {
            $time_start = time();
            $output->writeln('Job starts...');

            $feedback_handler = new SynchronizableTopicCommandFeedbackHandler($input, $output);
            $feedback_handler->setSerializer($this->serializer);
            $feedback_handler->setLogger($this->logger);

            // Create parameters from command options
            $parameters = new SynchronizableTopicDequeueParameters(
                (int) $input->getOption('limit'),
                $input->getOption('topic-id') ? (int) $input->getOption('topic-id') : null
            );

            $this->generic_log_spooler_dequeuer->dequeue($feedback_handler, $parameters);

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

            $this->sentry_cron_job_monitor->end();
        } catch (\Exception $e) {
            $this->sentry_cron_job_monitor->fail();

            throw $e;
        }

        return 0;
    }
}
