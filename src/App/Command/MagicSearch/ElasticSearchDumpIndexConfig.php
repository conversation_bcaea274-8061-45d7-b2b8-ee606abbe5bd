<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\MagicSearch;

use SonVideo\Erp\MagicSearch\Collection\MagicSearchDataProviderCollection;
use SonVideo\Erp\MagicSearch\Collection\MagicSearchQueryProviderCollection;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchFullIndexInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchIndexDataProviderInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;
use SonVideo\Erp\Referential\MagicSearch;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class ElasticSearchDumpIndexConfig extends Command
{
    protected static $defaultName = 'magic-search:elastic-search:dump-index-config';

    private MagicSearchDataProviderCollection $collection;

    private MagicSearchQueryProviderCollection $contexts;

    /** ElasticSearchGenerateFullIndexCommand constructor. */
    public function __construct(
        MagicSearchDataProviderCollection $collection,
        MagicSearchQueryProviderCollection $contexts,
        string $name = null
    ) {
        parent::__construct($name);
        $this->collection = $collection;
        $this->contexts = $contexts;
    }

    /** {@inheritDoc} */
    protected function configure(): void
    {
        $this->setDescription('Dump index config for usage in Kibana.')->addOption(
            'index',
            null,
            InputOption::VALUE_OPTIONAL,
            'Allow user to define which index to dump - default to all',
            MagicSearch::ALL_INDEX
        );
    }

    /**
     * {@inheritDoc}
     *
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $index = [];

        // collect config for indexing
        $search_providers = array_filter(
            $this->collection->getHandlers($input->getOption('index')),
            static fn ($search_provider): bool => $search_provider instanceof MagicSearchFullIndexInterface
        );

        /** @var MagicSearchIndexDataProviderInterface $search_provider */
        foreach ($search_providers as $search_provider) {
            $index_name = $search_provider->getMainIndexName();
            $index_config = $search_provider->getIndexConfig();

            $index[$index_name][] = [
                '### Delete temp index if exist',
                sprintf('DELETE /%s-temp', $index_name),
                '',
                '### Create temp index with config and mapping',
                sprintf('PUT /%s-temp', $index_name),
                json_encode($index_config, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT),
                '',
                '### Copy data from main index to temp index with new config and mapping',
                '### This may timeout on big index which takes more than 60 seconds to be treated',
                'POST /_reindex',
                json_encode(
                    [
                        'source' => [
                            'index' => $index_name,
                        ],
                        'dest' => ['index' => sprintf('%s-temp', $index_name)],
                    ],
                    JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT
                ),
                '',
                '### Verify afterward',
                sprintf('GET /%s-temp/_count', $index_name),
                sprintf('GET /%s-temp/_settings', $index_name),
                sprintf('GET /%s-temp/_mapping', $index_name),
                '',
                '### Move to main index',
                sprintf('DELETE /%s', $index_name),
                '',
                sprintf('PUT /%s', $index_name),
                json_encode($index_config, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT),
                '',
                'POST /_reindex',
                json_encode(
                    [
                        'source' => [
                            'index' => sprintf('%s-temp', $index_name),
                        ],
                        'dest' => ['index' => $index_name],
                    ],
                    JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT
                ),
                '',
                sprintf('DELETE /%s-temp', $index_name),
                '',
            ];
        }

        // collect config for indexing
        $context_providers = $this->contexts->getHandlers($input->getOption('index'));

        /** @var MagicSearchQueryProviderInterface $context_provider */
        foreach ($context_providers as $context_provider) {
            $index_name = $context_provider->getIndexName();

            $params = new MagicSearchHttpRequestPayload();
            $params->search_terms = 'foo';

            $query_config = $context_provider->getBody($params);

            $index[$index_name][] = [
                '### Search config (default with a string - search config may differ depending on the search term type)',
                sprintf('GET /%s/_search', $index_name),
                json_encode($query_config, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT),
                '',
                '',
                '',
            ];
        }

        foreach ($index as $items) {
            foreach ($items as $item) {
                $output->writeln($item);
            }
        }

        return 0;
    }
}
