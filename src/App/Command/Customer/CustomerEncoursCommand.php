<?php

namespace App\Command\Customer;

use App\Command\MonitorizedCommand;
use SonVideo\Erp\Customer\Manager\CustomerEncoursUpdater;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class CustomerEncoursCommand extends MonitorizedCommand
{
    protected static $defaultName = 'customer:encours:sync';

    private CustomerEncoursUpdater $customer_encours_updater;

    private const CHECK_UUID = '0682a8ea-410c-4b66-a470-acfa96db0d5e';

    public function __construct(
        string $server_env,
        CustomerEncoursUpdater $customer_encours_updater,
        string $name = null
    ) {
        parent::__construct($server_env, $name);

        $this->customer_encours_updater = $customer_encours_updater;
    }

    /** {@inheritDoc} */
    protected function configure(): void
    {
        $this->setDescription('Modifies the encours of the customer (prospect)');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->pingMonitoring(self::CHECK_UUID, 'start');

        try {
            $time_start = time();
            $output->writeln('Job starts...');

            $this->customer_encours_updater->execute();

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
