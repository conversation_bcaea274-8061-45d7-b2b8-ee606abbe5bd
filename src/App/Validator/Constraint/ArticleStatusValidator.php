<?php

namespace App\Validator\Constraint;

use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Referential\ArticleStatus as ArticleStatusReferential;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ArticleStatusValidator extends ConstraintValidator
{
    private ArticleRepository $article_repository;

    public function __construct(ArticleRepository $article_repository)
    {
        $this->article_repository = $article_repository;
    }

    /** {@inheritdoc} */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof ArticleStatus) {
            throw new UnexpectedTypeException($constraint, ArticleStatus::class);
        }
        $dto = $this->context->getRoot();
        $article = $this->article_repository->getOneById($dto->article_id, [
            'name',
            'selling_price',
            'tariff_tax_excluded',
            'selling_price',
            'pvgc',
            'stock_location',
            'weight',
        ]);

        if (
            !in_array($value, [
                ArticleStatusReferential::A_VOIR,
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::NON,
                ArticleStatusReferential::OUI,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::TMP,
                ArticleStatusReferential::YAPU,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unknown_value')
                ->setParameter('{{ status }}', $value)
                ->setParameter('{{ reason }}', 'unknown')
                ->addViolation();
        }

        if (
            'b' === $article['stock_location'] &&
            !in_array($value, [
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::YAPU,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unauthorized_for_destock')
                ->setParameter('{{ status }}', $value)
                ->setParameter('{{ reason }}', 'unauthorized for a destock')
                ->addViolation();
        }

        if (
            '' === $article['name'] &&
            in_array($value, [
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::OUI,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::TMP,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unauthorized_when_no_name')
                ->setParameter('{{ status }}', $value)
                ->setParameter('{{ reason }}', 'unauthorized for a product with no name')
                ->addViolation();
        }

        if (
            $article['selling_price'] < 0.005 &&
            in_array($value, [
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::OUI,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::TMP,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unauthorized_when_no_selling_price')
                ->setParameter('{{ status }}', $value)
                ->setParameter('{{ reason }}', 'unauthorized for a product with no selling price')
                ->addViolation();
        }

        if (
            $article['tariff_tax_excluded'] < 0.005 &&
            in_array($value, [
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::OUI,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::TMP,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unauthorized_when_no_purchase_price')
                ->setParameter('{{ status }}', $value)
                ->setParameter('{{ reason }}', 'unauthorized for a product with no purchase price')
                ->addViolation();
        }

        if (
            $article['selling_price'] - $article['pvgc'] > 0.005 &&
            in_array($value, [
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::OUI,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::TMP,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unauthorized_when_retail_price_too_high')
                ->setParameter('{{ status }}', $value)
                ->setParameter(
                    '{{ reason }}',
                    'unauthorized for a product with a retail price above the price generally found'
                )
                ->addViolation();
        }

        if (
            $article['weight'] < 0.0005 &&
            in_array($value, [
                ArticleStatusReferential::TODO,
                ArticleStatusReferential::OUI,
                ArticleStatusReferential::LAST,
                ArticleStatusReferential::TMP,
            ])
        ) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'unauthorized_when_no_weight')
                ->setParameter('{{ status }}', $value)
                ->setParameter('{{ reason }}', 'unauthorized for a product with no weight')
                ->addViolation();
        }
    }
}
