<?php

namespace App\Validator\Constraint;

use SonVideo\Erp\Article\Entity\ArticleV2Entity;
use SonVideo\Erp\Article\Entity\PackagedArticleEntity;
use SonVideo\Erp\Article\Mysql\Repository\PackagedArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Referential\Product;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ArticleSellingPriceValidator extends ConstraintValidator
{
    private SingleArticleReadRepository $single_article_read_repository;

    private PackagedArticleRepository $packaged_article_repository;

    public function __construct(
        SingleArticleReadRepository $single_article_read_repository,
        PackagedArticleRepository $packaged_article_repository
    ) {
        $this->single_article_read_repository = $single_article_read_repository;
        $this->packaged_article_repository = $packaged_article_repository;
    }

    /** {@inheritdoc} */
    public function validate($selling_price, Constraint $constraint): void
    {
        if (!$constraint instanceof ArticleSellingPrice) {
            throw new UnexpectedTypeException($constraint, ArticleSellingPrice::class);
        }

        $dto = $this->context->getRoot();
        $article = $this->single_article_read_repository->getOneByIdOrSku($dto->article_id);

        if ($article->is_package && $selling_price > $this->getPackagedArticlesTotalSellingPrice($dto->article_id)) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'package_selling_price_too_high')
                ->setParameter('{{ selling_price }}', $selling_price)
                ->setParameter('{{ reason }}', 'The selling price must be below the packaged articles price')
                ->addViolation();
        }

        if ($selling_price > $dto->pvgc) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'selling_price_too_high')
                ->setParameter('{{ selling_price }}', $selling_price)
                ->setParameter('{{ reason }}', 'The selling price must be below the PVGC')
                ->addViolation();
        }

        if ($this->getMargin($dto, $article) < 0) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'negative_margin')
                ->setParameter('{{ selling_price }}', $selling_price)
                ->setParameter('{{ reason }}', 'the margin must not be negative')
                ->addViolation();
        }

        if ($selling_price != $article->prices->selling_price && $article->is_on_sale) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ error_key }}', 'article_on_sale')
                ->setParameter('{{ selling_price }}', $selling_price)
                ->setParameter('{{ reason }}', 'the selling price cannot be changed when on sale')
                ->addViolation();
        }
    }

    private function getMargin($dto, ArticleV2Entity $article): float
    {
        if ($article->stock > 0 && $article->prices->weighted_cost > 0) {
            $buying_price_ht =
                $article->prices->weighted_cost -
                ($article->prices->promo_budget['amount'] ?? 0) -
                ($article->prices->unconditional_discount ?? 0);
        } else {
            $buying_price_ht =
                $dto->tariff_tax_excluded -
                ($article->prices->promo_budget['amount'] ?? 0) -
                ($article->prices->unconditional_discount ?? 0);
        }

        return $dto->selling_price - $dto->ecotax - $dto->sorecop - $buying_price_ht * (1 + Product::VAT_BASE);
    }

    private function getPackagedArticlesTotalSellingPrice(int $article_id): float
    {
        return array_reduce(
            $this->packaged_article_repository->findAllByPackageId($article_id),
            static fn ($carry, PackagedArticleEntity $packaged_article): float => $carry +
                $packaged_article->quantity * $packaged_article->unit_selling_price,
            0.0
        );
    }
}
