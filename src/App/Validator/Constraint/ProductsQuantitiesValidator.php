<?php

namespace App\Validator\Constraint;

use PommProject\Foundation\Exception\PommException;
use PommProject\Foundation\ParameterHolder;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * Class ProductsQuantitiesValidator.
 */
class ProductsQuantitiesValidator extends ConstraintValidator
{
    /** {@inheritdoc} */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof ProductsQuantities) {
            throw new UnexpectedTypeException($constraint, ProductsQuantities::class);
        }

        if (!is_array($value)) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ reason }}', 'Value is not an array.')
                ->addViolation();
        }

        foreach ($value as $one_value) {
            try {
                (new ParameterHolder($one_value))->mustHave('product_id')->mustHave('quantity');
            } catch (PommException $e) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->setParameter('{{ reason }}', $e->getMessage())
                    ->addViolation();
            }

            foreach (['product_id', 'quantity'] as $field) {
                if (!is_numeric($one_value[$field]) || $one_value[$field] < 1) {
                    $this->context
                        ->buildViolation($constraint->message)
                        ->setParameter('{{ reason }}', sprintf('Field "%s" must be a numeric greater than 0.', $field))
                        ->addViolation();
                }
            }
        }
    }
}
