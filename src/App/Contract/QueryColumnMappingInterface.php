<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Contract;

/**
 * Interface QueryColumnMappingInterface.
 */
interface QueryColumnMappingInterface
{
    public function setColumnsMappings(array $columns_mapping = []): QueryColumnMappingInterface;

    public function applyColumnMapping(string $column_name): string;
}
