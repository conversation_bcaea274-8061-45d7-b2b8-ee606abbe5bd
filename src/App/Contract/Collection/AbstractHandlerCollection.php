<?php

declare(strict_types=1);

namespace App\Contract\Collection;

use Psr\Log\LoggerInterface;

abstract class AbstractHandlerCollection implements HandlerCollectionInterface
{
    private const COLLECT_ALL = 1;
    private const COLLECT_ONLY_ONE = 2;

    /** @var iterable|\Countable */
    protected iterable $handlers;

    protected LoggerInterface $logger;

    public function __construct(iterable $handlers, LoggerInterface $logger)
    {
        foreach ($handlers as $handler) {
            if (!$handler instanceof CollectableInterface) {
                throw new \UnexpectedValueException(sprintf('The handler "%s" must implement the interface "%s"', get_class($handler), CollectableInterface::class));
            }
        }

        $this->handlers = $handlers;
        $this->logger = $logger;
    }

    /** @{@inheritDoc} */
    public function getHandlers(string $handler_key): array
    {
        return $this->collect($handler_key);
    }

    /** @{@inheritDoc} */
    public function getHandler(string $handler_key): ?CollectableInterface
    {
        $handler = $this->collect($handler_key, self::COLLECT_ONLY_ONE);

        return $handler[0] ?? null;
    }

    /** @return CollectableInterface[] */
    private function collect(string $handler_key, int $mode = self::COLLECT_ALL): array
    {
        $handlers = [];
        $this->logger->debug(sprintf('"%s" has %d handler(s)', get_class($this), count($this->handlers)));

        /** @var $handler CollectableInterface */
        foreach ($this->handlers as $handler) {
            $this->logger->debug(sprintf('Checking handler "%s" ...', get_class($handler)));

            if (false === $handler->canHandle($handler_key)) {
                continue;
            }

            if (self::COLLECT_ONLY_ONE === $mode) {
                return [$handler];
            }

            $handlers[] = $handler;
        }

        return $handlers;
    }
}
