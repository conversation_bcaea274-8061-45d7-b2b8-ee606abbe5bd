<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Contract;

/**
 * Interface DataLoaderAwareInterface.
 *
 * @deprecated Use directly the symfony serializer instead
 */
interface DataLoaderAwareInterface
{
    /**
     * setDataLoader.
     *
     * @return mixed
     */
    public function setDataLoader(DataLoaderInterface $data_loader);
}
