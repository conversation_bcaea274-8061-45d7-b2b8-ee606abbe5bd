<?php

namespace App\Contract;

use App\Adapter\Serializer\SerializerInterface;
use SonVideo\Orm\Entity\PaginatedCollectionRequest;
use Symfony\Component\HttpFoundation\Request;

trait PaginatedCollectionCreatorTrait
{
    public static function createPaginatedCollectionFromRequest(
        SerializerInterface $serializer,
        Request $request
    ): PaginatedCollectionRequest {
        /** @var PaginatedCollectionRequest $request_dto */
        $request_dto = $serializer->deserialize(
            $request->getContent() ?: '{}',
            PaginatedCollectionRequest::class,
            'json'
        );

        return $request_dto;
    }
}
