<?php
/*
 * This file is part of sisyphus package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Contract;

/**
 * Interface ExporterCollectionInterface.
 */
interface ExporterCollectionInterface
{
    /** getExporters */
    public function getExporters(): iterable;

    /** getExporter */
    public function getExporter(string $exporter_key): ?ExporterInterface;
}
