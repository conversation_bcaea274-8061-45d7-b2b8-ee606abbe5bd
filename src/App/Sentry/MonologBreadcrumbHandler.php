<?php
/*
 * This file is part of payment-api package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Sentry;

use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Logger;
use Sentry\Breadcrumb;
use Sentry\State\HubInterface;

/**
 * Class MonologBreadcrumbHandler.
 *
 * This implementation is a based on the original Monolog handler provided by Sentry
 */
final class MonologBreadcrumbHandler extends AbstractProcessingHandler
{
    private HubInterface $hub;

    /**
     * @param HubInterface $hub    The hub to which errors are reported
     * @param int          $level  The minimum logging level at which this
     *                             handler will be triggered
     * @param bool         $bubble Whether the messages that are handled can
     *                             bubble up the stack or not
     */
    public function __construct(HubInterface $hub, $level = Logger::DEBUG, bool $bubble = true)
    {
        $this->hub = $hub;

        parent::__construct($level, $bubble);
    }

    /** {@inheritdoc} */
    protected function write(array $record): void
    {
        // Some application message should not be logged
        if ($this->shouldBeIgnored($record)) {
            return;
        }

        // Don't keep log_id for sentry only
        if (isset($record['context']['log_id'])) {
            unset($record['context']['log_id']);
        }

        $breadcrumb = new Breadcrumb(
            $this->getBreadcrumbLevel($record['level']),
            $this->getBreadcrumbType($record['level']),
            $record['channel'],
            $record['message'],
            ($record['context'] ?? []) + ($record['extra'] ?? []),
            $record['datetime']->getTimestamp()
        );

        $this->hub->addBreadcrumb($breadcrumb);
    }

    private function shouldBeIgnored(array $record): bool
    {
        // sentry uses the 'nobreadcrumb' attribute to skip reporting
        if (!empty($record['context']['nobreadcrumb'])) {
            return true;
        }

        // Don't create breadcrumbs for useless information
        if (false !== strpos($record['message'], 'Pomm: Registering new client')) {
            return true;
        }

        if (false !== strpos($record['message'], 'Pomm: ListenerPooler: notification received.')) {
            return true;
        }

        if (false !== strpos($record['message'], 'User Deprecated:')) {
            return true;
        }

        if (false !== strpos($record['message'], 'Notified event "{event}" to listener "{listener}".')) {
            return true;
        }

        return false !== strpos($record['message'], 'Pheanstalk command:');
    }

    private function getBreadcrumbLevel($level): string
    {
        switch ($level) {
            case Logger::DEBUG:
                return Breadcrumb::LEVEL_DEBUG;
            case Logger::INFO:
            case Logger::NOTICE:
                return Breadcrumb::LEVEL_INFO;
            case Logger::WARNING:
                return Breadcrumb::LEVEL_WARNING;
            case Logger::ERROR:
                return Breadcrumb::LEVEL_ERROR;
            default:
                return Breadcrumb::LEVEL_FATAL;
        }
    }

    private function getBreadcrumbType(int $level): string
    {
        if ($level >= Logger::ERROR) {
            return Breadcrumb::TYPE_ERROR;
        }

        return Breadcrumb::TYPE_DEFAULT;
    }
}
