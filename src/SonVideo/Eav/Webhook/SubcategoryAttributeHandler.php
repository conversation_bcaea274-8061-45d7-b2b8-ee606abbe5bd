<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Eav\Webhook;

/**
 * Class SubcategoryAttributeHandler.
 */
class SubcategoryAttributeHandler extends AbstractEavWebhookHandler
{
    protected const HANDLED_EVENTS = ['eav.subcategory_attribute.update', 'eav.subcategory_attribute.delete'];

    /**
     * {@inheritDoc}
     *
     * @return array{facets: string[], attributes: string[]}
     */
    public function process(array $payload): array
    {
        return [
            'facets' => $this->processFacets($payload),
            'attributes' => $this->processAttributes($payload),
        ];
    }

    /**
     * Push the updates for eav facets.
     *
     * @return string[]
     */
    protected function processFacets(array $payload): array
    {
        // check if subcategory should be synchronized
        $subcategory_id = $payload['event']['data']['old']['subcategory_id'];
        if (!$this->isSubcategoryUsingFilters($subcategory_id)) {
            return [
                'message' => sprintf('Subcategory %d does not use the filters. Nothing to do.', $subcategory_id),
            ];
        }

        // switch based on operation
        $operation = strtolower($payload['event']['op']);
        if ('delete' === $operation) {
            return $this->handleDeletion($payload);
        }

        return $this->handleUpdate($payload);
    }

    /**
     * Push the updates for eav attributes.
     *
     * @return string[]
     */
    protected function processAttributes(array $payload): array
    {
        $operation = strtolower($payload['event']['op']);
        if ('update' !== $operation) {
            return ['message' => 'Not an update, nothing to do.'];
        }

        // check if display_order changed
        if ($payload['event']['data']['old']['display_order'] === $payload['event']['data']['new']['display_order']) {
            return [
                'message' => 'Display order has not changed, nothing to do.',
            ];
        }

        $attribute_id = $payload['event']['data']['new']['attribute_id'];
        $subcategory_id = $payload['event']['data']['new']['subcategory_id'];

        // retrieve linked product values
        $product_values = $this->product_value_model->findSkusByAttributeId($attribute_id);
        if ([] === $product_values) {
            return [
                'message' => 'No product use this attribute, nothing to do.',
            ];
        }

        // retrieve eavs for skus, filtered by subcategory
        $skus = array_map(
            static fn (array $pv) => $pv['sku'],
            array_filter($product_values, static fn ($pv): bool => $pv['subcategory_id'] === $subcategory_id)
        );
        $articles = $this->product_value_model->getAttributesBySkus($skus);

        // synchronize
        $response = $this->rpc_client->call('bo-cms', self::RPC_UPDATE_ATTRIBUTES_METHOD, [$articles]);

        return $response['result'];
    }

    /**
     * Handle deletion:
     * - Should update both if the attribute was activated.
     *
     * @return array
     */
    protected function handleDeletion(array $payload)
    {
        $attribute_id = $payload['event']['data']['old']['attribute_id'];
        $was_inactive = 'INACTIVE' === $payload['event']['data']['old']['filter_status'];
        if ($was_inactive) {
            return [
                'message' => sprintf('Attribute %d was inactive. Deletion has no side effect.', $attribute_id),
            ];
        }

        return $this->updateSubcategoryAndRelatedFacets($payload['event']['data']['old']['subcategory_id']);
    }

    /**
     * Handle update:
     * - on filter status (de)activation, alter both subcategory and related articles
     * - on other filter update, synchronize the category
     * - on display order alteration, update subcategory.
     */
    protected function handleUpdate(array $payload): array
    {
        // On filter_status (de)activation
        $old_filter_status = $payload['event']['data']['old']['filter_status'];
        $new_filter_status = $payload['event']['data']['new']['filter_status'];
        if (
            $old_filter_status !== $new_filter_status &&
            ('INACTIVE' === $old_filter_status || 'INACTIVE' === $new_filter_status)
        ) {
            return $this->updateSubcategoryAndRelatedFacets($payload['event']['data']['old']['subcategory_id']);
        }

        // On other update or display order alteration
        $old_display_order = $payload['event']['data']['old']['display_order'];
        $new_display_order = $payload['event']['data']['new']['display_order'];
        if ($old_filter_status !== $new_filter_status || $old_display_order !== $new_display_order) {
            return $this->updateSubcategoryFacets($payload['event']['data']['old']['subcategory_id']);
        }

        return ['message' => 'This update has no side effect.'];
    }
}
