<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Eav\Webhook;

use App\Database\PgErpServer\EavSchema\AttributeValueModel;
use App\Database\PgErpServer\EavSchema\ProductValueModel;
use App\Database\PgErpServer\EavSchema\SubcategoryModel;
use SonVideo\Eav\Repository\SubcategoryEavReadRepository;
use SonVideo\Erp\Webhook\Collection\Hasura\AbstractWebhookHandler;

/**
 * Class AbstractWebhookHandler.
 */
abstract class AbstractEavWebhookHandler extends AbstractWebhookHandler
{
    public const RPC_DEACTIVATE_METHOD = 'eav:deactivate_facets_for_subcategory';
    public const RPC_UPDATE_FACETS_METHOD = 'eav:update_facets';
    public const RPC_UPDATE_ATTRIBUTES_METHOD = 'eav:update_articles_attributes';
    public const ARTICLES_PER_BATCH = 50;

    protected SubcategoryEavReadRepository $subcategory_repo;

    protected SubcategoryModel $subcategory_model;

    protected AttributeValueModel $attribute_value_model;

    protected ProductValueModel $product_value_model;

    /** SubcategoryHandler constructor. */
    public function __construct(
        SubcategoryEavReadRepository $subcategory_repo,
        SubcategoryModel $subcategory_model,
        AttributeValueModel $attribute_value_model,
        ProductValueModel $product_value_model
    ) {
        $this->subcategory_repo = $subcategory_repo;
        $this->subcategory_model = $subcategory_model;
        $this->attribute_value_model = $attribute_value_model;
        $this->product_value_model = $product_value_model;
    }

    /**
     * Retrieve subcategory's facets and sync it.
     *
     * @return array
     */
    protected function updateSubcategoryFacets(int $subcategory_id)
    {
        $subcategories = $this->subcategory_model->findEavsByIds([$subcategory_id]);
        $response = $this->rpc_client->call('bo-cms', static::RPC_UPDATE_FACETS_METHOD, [$subcategories, []]);

        return $response['result'];
    }

    /**
     * Retrieve subcategory's facets, related articles faced and sync them.
     *
     * @return array
     */
    protected function updateSubcategoryAndRelatedFacets(int $subcategory_id)
    {
        // retrieve subcategory and linked articles facets
        $skus = $this->subcategory_repo->findActiveAssignedArticleSkus($subcategory_id);

        $subcategories = $this->subcategory_model->findEavsByIds([$subcategory_id]);
        $articles = [] !== $skus ? $this->subcategory_model->findArticleFacetsByIdAndSkus($subcategory_id, $skus) : [];

        // push to cms
        $response = $this->batchUpdate($subcategories, $articles);

        return $response['result'];
    }

    /** Do multiple call to cms for syncing articles instead of one big call. */
    protected function batchUpdate(array $subcategories, array $articles): array
    {
        $nb_batch = ceil((count($articles) + 1) / self::ARTICLES_PER_BATCH);
        $response = [];

        // Do nb_batch calls with a portion of the articles each time.
        // The subcategories are synchronize only once, on the first call
        for ($i = 0; $i < $nb_batch; ++$i) {
            $articles_to_sync = array_slice($articles, $i * self::ARTICLES_PER_BATCH, self::ARTICLES_PER_BATCH);
            if (0 === $i) {
                $call_response = $this->rpc_client->call('bo-cms', static::RPC_UPDATE_FACETS_METHOD, [
                    $subcategories,
                    $articles_to_sync,
                ]);
            } else {
                $call_response = $this->rpc_client->call('bo-cms', static::RPC_UPDATE_FACETS_METHOD, [
                    [],
                    $articles_to_sync,
                ]);
            }

            $response = array_merge_recursive($response, $call_response);
        }

        return $response;
    }

    /**
     * Check if subcategory use filters.
     * If not exists in referential, consider that it does not use filters and dont throw error.
     *
     * @return bool
     */
    protected function isSubcategoryUsingFilters(int $subcategory_id)
    {
        $subcategory = $this->subcategory_model->findByPK(['subcategory_id' => $subcategory_id]);
        if (null === $subcategory) {
            return false;
        }

        return $subcategory->get('use_filters');
    }
}
