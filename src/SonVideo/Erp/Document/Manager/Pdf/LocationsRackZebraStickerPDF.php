<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Document\Manager\Pdf;

use App\Sql\Query\QueryBuilder;
use Son<PERSON>ideo\Erp\Document\Contract\DocumentGeneratorInterface;
use SonVideo\Erp\Document\Manager\HtmlToPdfGenerator;
use SonVideo\Erp\Repository\LocationRepository;
use SonVideo\Erp\Utility\Contract\QrCodeTrait;

/**
 * Class LocationsRackZebraStickerPDF.
 */
final class LocationsRackZebraStickerPDF implements DocumentGeneratorInterface
{
    use QrCodeTrait;

    private const PAPER_SIZE = [4, 6];
    private const USE_LANDSCAPE = true;
    private const TEMPLATE_PATH = 'document/pdf/locations_rack_zebra_sticker.html.twig';

    private QueryBuilder $query_builder;

    private LocationRepository $location_repository;

    private HtmlToPdfGenerator $html_to_pdf_generator;

    /** LocationsRackZebraStickerPDF constructor. */
    public function __construct(
        QueryBuilder $query_builder,
        LocationRepository $location_repository,
        HtmlToPdfGenerator $html_to_pdf_generator
    ) {
        $this->query_builder = $query_builder;
        $this->location_repository = $location_repository;
        $this->html_to_pdf_generator = $html_to_pdf_generator;
    }

    /** {@inheritDoc} */
    public function canHandle(string $key): bool
    {
        return 'pdf_location_zebra_rack_sticker' === $key;
    }

    /**
     * @return array
     *
     * @throws \Exception
     */
    public function generate(array $options = []): string
    {
        if (
            !isset($options['locations']) ||
            (is_countable($options['locations']) ? count($options['locations']) : 0) === 0
        ) {
            throw new \UnexpectedValueException('No locations were provided.');
        }

        $this->query_builder
            ->setWhere(['location_id' => ['_in' => $options['locations']]], LocationRepository::COLUMNS_MAPPING)
            ->setOrderBy(
                'substr(code, 4, 2) ASC, substr(code, 7, 1) ASC, substr(code, 9, 2) ASC, substr(code, 12, 2) DESC, substr(code, 15, 2) ASC'
            );

        $locations = $this->location_repository->findAllPaginated($this->query_builder)->getResults();

        $index = 1;
        $columns = [];
        $collected = [];

        foreach ($locations as $location) {
            $location = (array) $location;
            $location['qrcode'] = $this->getQrCode($location['code']);

            $collected[] = $location;

            if (0 === $index % 3) {
                $columns[] = $collected;
                $collected = [];
                $index = 1;
            } else {
                ++$index;
            }
        }

        if ([] !== $collected) {
            $columns[] = $collected;
        }

        return $this->html_to_pdf_generator->generate(['columns' => $columns], self::TEMPLATE_PATH, [
            'paper_size' => self::PAPER_SIZE,
            'use_landscape' => self::USE_LANDSCAPE,
        ]);
    }
}
