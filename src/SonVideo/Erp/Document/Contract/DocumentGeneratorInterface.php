<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Document\Contract;

use App\Contract\Collection\CollectableInterface;

interface DocumentGeneratorInterface extends CollectableInterface
{
    public function generate(array $options = []): string;
}
