<?php

namespace SonVideo\Erp\Competitor\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Competitor\Entity\CompetitorEntity;

class CompetitorRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'id' => 'c.id',
        'name' => 'c.competitor_code',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
                SELECT
                    c.id,
                    c.competitor_code
                FROM backOffice.competitors c
                WHERE {conditions}
                GROUP BY c.id
                ) tmp
            {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->setColumnsMapping(static::COLUMNS_MAPPING)->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            CompetitorEntity::class
        );
    }
}
