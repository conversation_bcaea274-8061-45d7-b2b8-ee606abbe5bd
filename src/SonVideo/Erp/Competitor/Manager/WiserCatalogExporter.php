<?php

namespace SonVideo\Erp\Competitor\Manager;

use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use SonVideo\Erp\Competitor\Mysql\Repository\CatalogExporterRepository;
use SonVideo\Erp\Export\Statistic\Manager\AbstractCvsExporter;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;

final class WiserCatalogExporter extends AbstractCvsExporter
{
    protected const CSV_SEPARATOR = ';';

    /** @var string[] */
    protected const EXPORT_HEADERS = [
        'universe' => 'Universe (Managed by WorkIT)',
        'subcategory_name' => 'Categories (Mandatory)',
        'segment1' => 'Segment (Optional)',
        'segment2' => 'Segment2 (Optional)',
        'segment3' => 'Segment3 (Optional)',
        'brand_name' => 'Brand (Mandatory)',
        'title' => 'Title (Mandatory)',
        'sku' => 'SKU (Mandatory)',
        'colour' => 'Colour (Optional)',
        'pvgc' => 'RRP (Mandatory)',
        'ean' => 'EAN (Recommended)',
        'selling_price' => 'PV',
    ];

    private CatalogExporterRepository $catalog_exporter_repository;
    private FilesystemInterface $wiser_ftp_filesystem;

    public function __construct(
        ExportedFile $exported_file,
        CatalogExporterRepository $catalog_exporter_repository,
        MountManager $mount_manager
    ) {
        parent::__construct($exported_file);

        $this->catalog_exporter_repository = $catalog_exporter_repository;
        $this->wiser_ftp_filesystem = $mount_manager->getFilesystem('wiser_ftp_filesystem');
    }

    protected function getRecords(string $days_since): \Generator
    {
        return $this->catalog_exporter_repository->fetchAllWithGenerator();
    }

    protected function getExportName(): string
    {
        return sprintf('Export_catalog_wiser_%s.csv', (new \DateTime())->format('YmdHi'));
    }

    public function export(): void
    {
        parent::export();

        // Put the created CSV in the Wiser FTP
        $this->wiser_ftp_filesystem->write(
            basename($this->getExportName()),
            $this->exported_file->getFilesystem()->read($this->getExportName())
        );
    }
}
