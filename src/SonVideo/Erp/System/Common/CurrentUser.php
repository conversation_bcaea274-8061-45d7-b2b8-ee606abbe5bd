<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\System\Common;

use Psr\Log\LoggerInterface;
use <PERSON>\Uuid\Uuid;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\User\Entity\UserEntity;
use SonVideo\HalMiddlewareBundle\Application\Gateway\Owner\OwnerGatewayInterface;
use SonVideo\HalMiddlewareBundle\Domain\Permission\Owner;
use SonVideo\HalMiddlewareBundle\Infrastructure\Adapter\Security\AuthenticatedApiUser;
use SonVideo\HalMiddlewareBundle\Infrastructure\Adapter\Security\AuthorizationChecker;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

/**
 * Class CurrentUser.
 */
class CurrentUser
{
    public const MODE_RETURNS_BOOLEAN = 'bool';
    public const MODE_THROW_EXCEPTION = 'throw_exception';

    private AuthorizationChecker $authorization_checker;

    private TokenStorageInterface $token_storage;

    private AccountQueryRepository $account_repository;

    private OwnerGatewayInterface $owner_gateway;

    private LoggerInterface $logger;

    private ?Owner $owner = null;

    /** TransactionStateDecorator constructor. */
    public function __construct(
        AuthorizationChecker $authorization_checker,
        TokenStorageInterface $token_storage,
        AccountQueryRepository $account_repository,
        OwnerGatewayInterface $owner_gateway,
        LoggerInterface $logger
    ) {
        $this->authorization_checker = $authorization_checker;
        $this->token_storage = $token_storage;
        $this->account_repository = $account_repository;
        $this->owner_gateway = $owner_gateway;
        $this->logger = $logger;
    }

    public function load(): void
    {
        if (!$this->owner instanceof Owner) {
            $oauth_token = $this->token_storage->getToken();
            if (!$oauth_token instanceof TokenInterface) {
                throw new \RuntimeException('Token was not found in token storage.');
            }

            $authenticated_api_user = $oauth_token->getUser();
            if (!$authenticated_api_user instanceof AuthenticatedApiUser) {
                throw new AccessDeniedException('AuthenticatedApiUser was not found in token storage.');
            }

            // the account must have the legacy username
            $owner = $this->owner_gateway->findById(Uuid::fromString($authenticated_api_user->getUserIdentifier()));
            if (!$owner instanceof Owner) {
                throw new \UnexpectedValueException(sprintf('Owner with id "%s" not found.', $authenticated_api_user->getUserIdentifier()));
            }

            if (null === ($owner->getMeta()['username'] ?? null)) {
                throw new \RuntimeException(sprintf('Username was not found for owner with id "%s".', $owner->getOwnerId()));
            }

            $this->owner = $owner;
        }
    }

    /**
     * This load a user re-hydrated with some legacy info
     * That means that an application owner type may not this class.
     */
    public function entity(): UserEntity
    {
        $this->load();

        // retrieve user from this application
        try {
            $legacy_user = $this->account_repository->getUser($this->owner->getMeta()['username']);
        } catch (\Exception $exception) {
            throw new AccessDeniedException(sprintf('Could not load account with username "%s" (%s)', $this->owner->getMeta()['username'], $exception->getMessage()));
        }

        $information = $this->owner->getMeta();
        $information['username'] = (string) $this->owner->getOwnerId();
        $legacy_user->fromArray($information);

        $this->logger->debug('current legacy user - loaded', [
            'legacy_id_utilisateur' => $legacy_user->id_utilisateur,
            'legacy_utilisateur' => $legacy_user->utilisateur,
            'email' => $legacy_user->email,
        ]);

        return $legacy_user;
    }

    /**
     * hasPermissions.
     *
     * Check authorization for all given permissions.
     * If one is not authorized, throw an exception.
     */
    public function hasPermissions(array $permissions, string $mode = self::MODE_RETURNS_BOOLEAN): bool
    {
        $this->logger->info('current user - has permissions', ['permissions' => $permissions]);

        try {
            $this->load();
            $this->authorization_checker->hasPermissions($this->owner->getOwnerId(), $permissions);
        } catch (AccessDeniedException $exception) {
            if (self::MODE_THROW_EXCEPTION === $mode) {
                throw $exception;
            }

            // If access denied exception has been raised just log the exception and return false
            // we don't catch other exceptions
            $this->logger->info('current user - access denied', [
                'message' => $exception->getMessage(),
            ]);

            return false;
        }

        // If all given permissions are granted
        return true;
    }
}
