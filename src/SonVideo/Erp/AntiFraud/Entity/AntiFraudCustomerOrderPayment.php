<?php

namespace SonVideo\Erp\AntiFraud\Entity;

final class AntiFraudCustomerOrderPayment
{
    private int $customer_order_payment_id;

    private string $status;

    private AntiFraudFormattedReason $reason;

    public function __construct(int $customer_order_payment_id, string $status, AntiFraudFormattedReason $reason)
    {
        $this->customer_order_payment_id = $customer_order_payment_id;
        $this->status = $status;
        $this->reason = $reason;
    }

    /** @return array{customer_order_payment_id: int, status: string, reason: string} */
    public function toArray(): array
    {
        return [
            'customer_order_payment_id' => $this->customer_order_payment_id,
            'status' => $this->status,
            'reason' => $this->reason->to<PERSON>son(),
        ];
    }
}
