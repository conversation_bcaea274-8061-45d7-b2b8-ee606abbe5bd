<?php

namespace SonVideo\Erp\AntiFraud\Manager;

use App\Contract\DataLoaderAwareTrait;
use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudFormattedArticleReason;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudFormattedReason;
use SonVideo\Erp\AntiFraud\Entity\ArticleForAntiFraudModule;
use SonVideo\Erp\Referential\AntiFraudReason;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;

final class ArticleAntiFraudStatusChecker
{
    use DataLoaderAwareTrait;

    public const EXCLUSION_RULES_KEY = 'customer_order.anti_fraud_module.product_exclusion_rules';

    private ArticleReadRepository $article_read_repository;

    private ParameterModel $parameter_model;

    private QueryBuilder $query_builder;

    private bool $rejection_rules_are_cached = false;

    /** @var array */
    private $rejection_rules;

    public function __construct(
        ArticleReadRepository $article_read_repository,
        ParameterModel $parameter_model,
        QueryBuilder $query_builder
    ) {
        $this->article_read_repository = $article_read_repository;
        $this->parameter_model = $parameter_model;
        $this->query_builder = $query_builder;
    }

    /** @throws NotFoundException|SqlErrorMessageException
     * @throws \JsonException
     */
    public function getExclusionStatusesFor(array $article_ids_or_skus): array
    {
        $result = [];
        $result_by_sku = [];
        $result_by_article_id = [];

        $articles = $this->loadArticles($article_ids_or_skus);

        foreach ($articles as $article) {
            $is_excluded = $this->isExcluded(ArticleForAntiFraudModule::from($article));

            $result_by_sku[$article['sku']] = $is_excluded;
            $result_by_article_id[$article['product_id']] = $is_excluded;
        }

        foreach ($article_ids_or_skus as $article_id_or_sku) {
            $mapped =
                ctype_digit($article_id_or_sku) || is_int($article_id_or_sku)
                    ? $result_by_article_id[(int) $article_id_or_sku] ?? null
                    : $result_by_sku[$article_id_or_sku] ?? null;

            if (null !== $mapped) {
                $result[$article_id_or_sku] = $mapped;
            }
        }

        return $result;
    }

    private function loadArticles(array $article_ids_or_skus): array
    {
        $article_ids = [];
        $skus = [];

        foreach ($article_ids_or_skus as $article_id_or_sku) {
            if (ctype_digit($article_id_or_sku) || is_int($article_id_or_sku)) {
                $article_ids[] = (int) $article_id_or_sku;

                continue;
            }

            $skus[] = $article_id_or_sku;
        }

        $conditions = [];
        if ([] !== $article_ids) {
            $conditions['product_id'] = ['_in' => array_unique($article_ids)];
        }

        if ([] !== $skus) {
            $conditions['sku'] = ['_in' => array_unique($skus)];
        }

        $where = [] !== $conditions ? ['_or' => $conditions] : QueryBuilder::WHERE_FALSE;

        $this->query_builder->setWhere($where, ArticleReadRepository::COLUMNS_MAPPING);

        return $this->article_read_repository->findAllPaginated($this->query_builder)->getResults();
    }

    private function isExcluded(ArticleForAntiFraudModule $article): bool
    {
        return $this->check($article) instanceof AntiFraudFormattedReason;
    }

    public function check(ArticleForAntiFraudModule $article): ?AntiFraudFormattedReason
    {
        $reasons = [];

        foreach ($this->getRejectionRules() as $rejection_rule) {
            $reason = $this->checkAgainstBrandRule($rejection_rule, $article);
            if ($reason instanceof AntiFraudFormattedArticleReason) {
                $reasons[] = $reason;

                continue;
            }

            $reason = $this->checkAgainstSubcategoryRule($rejection_rule, $article);
            if ($reason instanceof AntiFraudFormattedArticleReason) {
                $reasons[] = $reason;

                continue;
            }

            $reason = $this->checkAgainstSkuRule($rejection_rule, $article);
            if ($reason instanceof AntiFraudFormattedArticleReason) {
                $reasons[] = $reason;
            }
        }

        if ([] === $reasons) {
            return null;
        }

        return AntiFraudFormattedReason::create(AntiFraudReason::HAS_INVALID_ARTICLES)->withDetails(
            array_map(static fn (AntiFraudFormattedArticleReason $reason): array => $reason->toArray(), $reasons)
        );
    }

    /** @throws \JsonException */
    private function getRejectionRules()
    {
        if (!$this->rejection_rules_are_cached) {
            $this->rejection_rules = json_decode(
                $this->parameter_model->getParameter(self::EXCLUSION_RULES_KEY)->extract()[0]['value'],
                null,
                512,
                JSON_THROW_ON_ERROR
            );

            $this->rejection_rules_are_cached = true;
        }

        return $this->rejection_rules;
    }

    private function checkAgainstBrandRule(
        object $rejection_rule,
        ArticleForAntiFraudModule $article
    ): ?AntiFraudFormattedArticleReason {
        if ('brand' !== $rejection_rule->type) {
            return null;
        }

        if ($rejection_rule->brand_id !== $article->brand_id) {
            return null;
        }

        $reason = new AntiFraudFormattedArticleReason($article->sku, AntiFraudReason::INVALID_BRAND);

        if (isset($rejection_rule->min_price)) {
            if ($rejection_rule->min_price <= $article->selling_price_tax_included) {
                return $reason->withDetails([
                    'brand_id' => $article->brand_id,
                    'article_selling_price' => $article->selling_price_tax_included,
                    'min_price' => $rejection_rule->min_price,
                ]);
            }

            return null;
        }

        return $reason->withDetails([
            'brand_id' => $article->brand_id,
        ]);
    }

    private function checkAgainstSubcategoryRule(
        object $rejection_rule,
        ArticleForAntiFraudModule $article
    ): ?AntiFraudFormattedArticleReason {
        if ('subcategory' !== $rejection_rule->type) {
            return null;
        }

        if ($rejection_rule->subcategory_id !== $article->subcategory_id) {
            return null;
        }

        $reason = new AntiFraudFormattedArticleReason($article->sku, AntiFraudReason::INVALID_SUBCATEGORY);

        if (isset($rejection_rule->min_price)) {
            if ($rejection_rule->min_price <= $article->selling_price_tax_included) {
                return $reason->withDetails([
                    'subcategory_id' => $article->subcategory_id,
                    'article_selling_price' => $article->selling_price_tax_included,
                    'min_price' => $rejection_rule->min_price,
                ]);
            }

            return null;
        }

        return $reason->withDetails([
            'subcategory_id' => $article->subcategory_id,
        ]);
    }

    private function checkAgainstSkuRule(
        object $rejection_rule,
        ArticleForAntiFraudModule $article
    ): ?AntiFraudFormattedArticleReason {
        if ('sku' !== $rejection_rule->type) {
            return null;
        }

        if (in_array($article->sku, $rejection_rule->skus, true)) {
            return new AntiFraudFormattedArticleReason($article->sku, AntiFraudReason::INVALID_SKU);
        }

        return null;
    }
}
