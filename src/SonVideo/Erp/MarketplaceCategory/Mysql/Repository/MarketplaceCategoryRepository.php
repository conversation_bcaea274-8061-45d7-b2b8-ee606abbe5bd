<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MarketplaceCategory\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

class MarketplaceCategoryRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'marketplace_category_id' => 'ctcm.id',
        'marketplace_id' => 'ctcm.marketplace_id',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM (
            SELECT
                ctcm.id,
                ctcm.marketplace_id,
                CONCAT(
                    COALESCE(ctcm.niveau_1, ''), ' > ',
                    COALESCE(ctcm.niveau_2, ''), ' > ',
                    COALESCE(ctcm.niveau_3, ''), ' > ',
                    COALESCE(ctcm.niveau_4, '')
                ) AS path
            FROM backOffice.CTG_TXN_categorie_marketplace ctcm
            WHERE {conditions}
            ORDER BY ctcm.id, ctcm.marketplace_id
        ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
