<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\SubCategory\Mysql\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\DataLoader\MapToEntityTrait;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Referential\SubcategoryType;
use SonVideo\Erp\SubCategory\Dto\CreationContext\SubcategoryContextDto;
use SonVideo\Erp\SubCategory\Dto\UpdateContext\SubcategoryContextDto as SubcategoryUpdateContextDto;

class SubcategoryRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use MapToEntityTrait;
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'subcategory_id' => 'cts.id',
        'name' => 'cts.souscategorie',
        'parent_category_id' => 'cts.dft_categorie_id',
        'bbac_subtype_id' => 'ctc.id_sous_type_bbac',
        'parent_category_name' => 'ctc.categorie',
        'associated_products' => 'COUNT(p.id_produit)',
        'associated_products_on_sale' => "COUNT(CASE WHEN a.status IN ('oui', 'last') THEN p.id_produit END)",
        'outsize' => 'cts.hors_gabarit',
        'charged_delivery' => 'cts.port_facture',
        'custom_code' => 'cts.code_douanier',
        'ecotax_code' => 'cts.code_ecotaxe',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
              SELECT
                  cts.id                        AS subcategory_id,
                  cts.souscategorie             AS name,
                  cts.dft_categorie_id          AS parent_category_id,
                  cts.id_sous_type_bbac         AS bbac_subtype_id,
                  ctc.categorie                 AS parent_category_name,
                  COUNT(p.id_produit)           AS associated_products,
                  COUNT(CASE WHEN a.status IN ('oui', 'last') THEN p.id_produit END) AS associated_products_on_sale,
                  stbb.nom                      AS bbac_subtype_name,
                  cts.warranty_type             AS warranty_type,
                  cts.hors_gabarit              AS outsize,
                  cts.port_facture              AS charged_delivery,
                  cts.subcategory_type          AS subcategory_type,
                  cts.seller_commission_config  AS seller_commission_config,
                  IF(cts.code_douanier IS NULL OR cts.code_douanier = '', ctc.code_douanier, cts.code_douanier) AS custom_code,
                  IF(cts.code_douanier IS NULL OR cts.code_douanier = '', 'category', 'subcategory') AS custom_code_origin,
                  cts.code_ecotaxe              AS ecotax_code
                FROM
                  backOffice.CTG_TXN_souscategorie        cts
                  INNER JOIN backOffice.CTG_TXN_categorie ctc ON cts.dft_categorie_id = ctc.id_categorie
                  LEFT JOIN backOffice.CTG_TXN_sous_type_bbac stbb ON cts.id_sous_type_bbac = stbb.id_sous_type_bbac
                  LEFT JOIN backOffice.produit           p ON cts.id = p.id_souscategorie
                  LEFT JOIN backOffice.article           a ON p.id_produit = a.id_produit
                WHERE {conditions}
                GROUP BY subcategory_id
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /**
     * @throws InternalErrorException
     * @throws \JsonException
     * @throws NotFoundException
     */
    public function update(SubcategoryUpdateContextDto $subcategory): int
    {
        $sql = <<<SQL
        UPDATE backOffice.CTG_TXN_souscategorie
        SET souscategorie = :name,
            id_sous_type_bbac = :bbac_subtype_id,
            dft_categorie_id = :parent_category_id,
            warranty_type = :warranty_type,
            hors_gabarit = :outsize,
            port_facture = :charged_delivery,
            subcategory_type = :subcategory_type,
            seller_commission_config = :seller_commission_config,
            code_douanier = :custom_code,
            code_ecotaxe = :ecotax_code
        WHERE id = :subcategory_id
        SQL;

        try {
            $nb_affected_rows = $this->legacy_pdo->fetchAffected($sql, [
                'subcategory_id' => $subcategory->subcategory_id,
                'name' => $subcategory->name,
                'bbac_subtype_id' => $subcategory->bbac_subtype_id,
                'parent_category_id' => $subcategory->parent_category_id,
                'warranty_type' => $subcategory->warranty_type,
                'outsize' => $subcategory->outsize,
                'charged_delivery' => $subcategory->charged_delivery,
                'subcategory_type' => $subcategory->subcategory_type,
                'seller_commission_config' => json_encode($subcategory->seller_commission_config, JSON_THROW_ON_ERROR),
                'custom_code' => $subcategory->custom_code,
                'ecotax_code' => $subcategory->ecotax_code,
            ]);
        } catch (Exception $exception) {
            throw new InternalErrorException(InternalError::GENERIC, $exception);
        }

        return $nb_affected_rows;
    }

    public function create(SubcategoryContextDto $dto): int
    {
        try {
            $this->legacy_pdo->beginTransaction();

            $sql = <<<SQL
                INSERT INTO backOffice.CTG_TXN_souscategorie(souscategorie, dft_categorie_id, subcategory_type)
                VALUES (:subcategory_name, :category_id, :subcategory_type)
            SQL;

            $this->legacy_pdo->fetchAffected($sql, [
                'subcategory_name' => $dto->subcategory_name,
                'category_id' => $dto->category_id,
                'subcategory_type' => SubcategoryType::UNDEFINED,
            ]);

            $subcategory_id = $this->legacy_pdo->lastInsertId();

            $sql = <<<SQL
                INSERT INTO backOffice.CTG_TXN_souscategorie_CTG_TXN_categorie(id_liaison_dom_cat, id_souscategorie, id_categorie)
                VALUES (:domain_id, :subcategory_id, :category_id)
            SQL;

            $this->legacy_pdo->fetchAffected($sql, [
                'domain_id' => $dto->domain_id,
                'subcategory_id' => $subcategory_id,
                'category_id' => $dto->category_id,
            ]);

            $this->legacy_pdo->commit();

            return $subcategory_id;
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();
            throw $exception;
        }
    }

    public function doesSubcategoryNameExist(string $subcategory_name): bool
    {
        $sql = <<<SQL
        select exists(SELECT * FROM backOffice.CTG_TXN_souscategorie where lower(souscategorie) = lower(:subcategory_name)) as isSubcategoryExist
        SQL;

        return $this->legacy_pdo->fetchOne($sql, [
            'subcategory_name' => $subcategory_name,
        ])['isSubcategoryExist'];
    }
}
