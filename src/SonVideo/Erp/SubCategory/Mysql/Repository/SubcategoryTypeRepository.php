<?php

namespace SonVideo\Erp\SubCategory\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

class SubcategoryTypeRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'type' => 'type',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
                SELECT
                    type,
                    label
                FROM backOffice.subcategory_type
                WHERE {conditions}
            ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
