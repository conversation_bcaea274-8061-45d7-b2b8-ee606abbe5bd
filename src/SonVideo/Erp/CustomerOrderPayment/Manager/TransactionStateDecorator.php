<?php

namespace SonVideo\Erp\CustomerOrderPayment\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Entity\CustomerOrderPaymentEntity;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;
use SonVideo\Erp\Referential\Payment\PaymentId;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\System\Common\CurrentUser;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class TransactionStateDecorator implements SerializerAwareInterface, LoggerAwareInterface
{
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    private CurrentUser $current_user;

    private CustomerOrderPaymentReadRepository $customer_order_payment_repository;

    private array $user_permissions;

    public function __construct(
        CurrentUser $current_user,
        CustomerOrderPaymentReadRepository $customer_order_payment_repository
    ) {
        $this->current_user = $current_user;
        $this->customer_order_payment_repository = $customer_order_payment_repository;

        $this->user_permissions = [
            UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT => $this->current_user->hasPermissions([
                UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT,
            ]),
            UserPermission::CUSTOMER_ORDER_PAYMENT_ACCEPT_CETELEM => $this->current_user->hasPermissions([
                UserPermission::CUSTOMER_ORDER_PAYMENT_ACCEPT_CETELEM,
            ]),
            UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CREDIT_NOTE_WITH_PRIOR_PAYMENT => $this->current_user->hasPermissions(
                [UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CREDIT_NOTE_WITH_PRIOR_PAYMENT]
            ),
            UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_EASYLOUNGE => $this->current_user->hasPermissions([
                UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_EASYLOUNGE,
            ]),
            UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_FULLCB => $this->current_user->hasPermissions([
                UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_FULLCB,
            ]),
            UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_RETAIL_CETELEM => $this->current_user->hasPermissions([
                UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_RETAIL_CETELEM,
            ]),
            UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CANCEL => $this->current_user->hasPermissions([
                UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CANCEL,
            ]),
        ];
    }

    /** @throws ExceptionInterface */
    public function apply(array $transactions): array
    {
        if ([] === $transactions) {
            return $transactions;
        }

        $customer_order_ids = [];
        foreach (array_column($transactions, 'customer_order_id') as $customer_order_id) {
            $customer_order_ids[$customer_order_id] = 1;
        }

        $this->logger->info('transaction state decorator - extracted customer_order_ids', [
            'customer_order_ids' => array_keys($customer_order_ids),
        ]);

        $customer_orders = $this->customer_order_payment_repository->fetchCustomerOrderForPaymentDecoration(
            array_keys($customer_order_ids)
        );

        foreach ($transactions as $key => $transaction) {
            $transaction = (array) $transaction;
            $transaction_entity = $this->serializer->denormalize($transaction, CustomerOrderPaymentEntity::class);

            $additional_computed_columns = [
                'can_be_accepted' => $this->canBeAccepted($transaction_entity),
                'can_be_cancelled' => $this->canBeCancelled($transaction_entity),
                'can_be_remitted' => $this->canBeRemitted($transaction_entity),
                'can_cancel_remit' => $this->canCancelRemit(
                    $transaction_entity,
                    $customer_orders[$transaction['customer_order_id']]['status']
                ),
                'should_be_remitted' => $this->canBeRemitted($transaction_entity, false),
            ];

            $transactions[$key] = array_merge(
                $this->serializer->normalize($transaction_entity),
                $additional_computed_columns
            );

            $this->logger->info('transaction state decorator - formatted transaction', [
                'transaction' => $transactions[$key],
            ]);
        }

        return $transactions;
    }

    protected function canBeAccepted(CustomerOrderPaymentEntity $transaction): bool
    {
        if ($transaction->accepted_at instanceof \DateTimeInterface) {
            return false;
        }

        if ('N' === $transaction->payment_created_remotely) {
            return true;
        }

        if (
            'Y' === $transaction->payment_created_remotely &&
            in_array($transaction->auto_status, ['accepte', 'remise'])
        ) {
            return true;
        }

        if (
            'Y' === $transaction->payment_created_remotely &&
            in_array($transaction->auto_status, ['attente', '']) &&
            'remboursement' === $transaction->type
        ) {
            return true;
        }

        if (
            PaymentId::PRESTO === $transaction->payment_id &&
            $this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_ACCEPT_CETELEM]
        ) {
            if (in_array($transaction->auto_status, ['attente', 'absent'])) {
                return true;
            }

            if (
                'refuse' == $transaction->auto_status &&
                in_array($transaction->auto_status_detail, [
                    'Dossier en cours d\'étude - ne pas livrer',
                    'Annulation Client',
                ])
            ) {
                return true;
            }
        }

        return false;
    }

    protected function canBeCancelled(CustomerOrderPaymentEntity $transaction): bool
    {
        // For now, we don't handle "REFUND" type in ERP Server
        if ('remboursement' === $transaction->type) {
            return false;
        }

        // Perform checks for "PAYMENT" type
        if (
            $transaction->remitted_at instanceof \DateTimeInterface ||
            $transaction->cancelled_at instanceof \DateTimeInterface
        ) {
            return false;
        }

        if ('N' === $transaction->payment_created_remotely) {
            return true;
        }

        if (null !== $transaction->remitted_proof || $transaction->remit_asked_at instanceof \DateTimeInterface) {
            return false;
        }

        return in_array($transaction->auto_status, ['accepte', 'refuse']);
    }

    protected function canBeRemitted(CustomerOrderPaymentEntity $transaction, bool $check_permissions = true): bool
    {
        if (
            $transaction->remitted_at instanceof \DateTimeInterface ||
            $transaction->cancelled_at instanceof \DateTimeInterface ||
            !$transaction->accepted_at instanceof \DateTimeInterface
        ) {
            return false;
        }

        if ($this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT]) {
            return true;
        }

        if ('Y' === $transaction->payment_created_remotely) {
            return false;
        }

        if ('auto' === $transaction->remit_proof_source) {
            return true;
        }

        // If we don't want to check permissions (eg: should be remitted, eventually by someone else)
        if (false === $check_permissions) {
            return 'manuel' === $transaction->remit_proof_source ||
                in_array(
                    $transaction->payment_id,
                    [
                        PaymentId::CREDIT_NOTE_WITH_PRIOR_PAYMENT,
                        PaymentId::EASYLOUNGE,
                        PaymentId::FULLCB3X,
                        PaymentId::FULLCB4X,
                        PaymentId::RETAIL_CETELEM,
                    ],
                    true
                );
        }

        // If manual remit and not in excluded payments list
        if (
            'manuel' === $transaction->remit_proof_source &&
            !in_array(
                $transaction->payment_id,
                [
                    PaymentId::CREDIT_NOTE_WITH_PRIOR_PAYMENT,
                    PaymentId::EASYLOUNGE,
                    PaymentId::FULLCB3X,
                    PaymentId::FULLCB4X,
                    PaymentId::RETAIL_CETELEM,
                ],
                true
            )
        ) {
            return true;
        }

        if (
            PaymentId::CREDIT_NOTE_WITH_PRIOR_PAYMENT === $transaction->payment_id &&
            $this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CREDIT_NOTE_WITH_PRIOR_PAYMENT]
        ) {
            return true;
        }

        if (
            PaymentId::EASYLOUNGE === $transaction->payment_id &&
            $this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_EASYLOUNGE]
        ) {
            return true;
        }

        if (
            PaymentId::RETAIL_CETELEM === $transaction->payment_id &&
            $this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_RETAIL_CETELEM]
        ) {
            return true;
        }

        return in_array($transaction->payment_id, [PaymentId::FULLCB3X, PaymentId::FULLCB4X], true) &&
            $this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_FULLCB];
    }

    protected function canCancelRemit(CustomerOrderPaymentEntity $transaction, string $customer_order_status): bool
    {
        if (!$this->user_permissions[UserPermission::CUSTOMER_ORDER_PAYMENT_REMIT_CANCEL]) {
            return false;
        }

        if ('cloture' === $customer_order_status) {
            return false;
        }

        if (
            !$transaction->remitted_at instanceof \DateTimeInterface ||
            $transaction->cancelled_at instanceof \DateTimeInterface
        ) {
            return false;
        }

        // the payment type isn't FullCB, Ecranlounge or Avoir Paiement Antérieur (Last one asked by caroline)

        /*
        if (
            in_array(
                (int) $transaction->payment_id,
                [
                    PaymentId::CREDIT_NOTE_WITH_PRIOR_PAYMENT,
                    PaymentId::EASYLOUNGE,
                    PaymentId::FULLCB3X,
                    PaymentId::FULLCB4X,
                    PaymentId::RETAIL_CETELEM,
                ],
                true
            )
        ) {
            return false;
        }
        */

        return null !== $transaction->payment_id;
    }
}
