<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Utility\Printer;

use App\Exception\NotFoundException;
use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use League\Flysystem\MountManager;
use SonVideo\Erp\Referential\Printer;
use SonVideo\Erp\Repository\PrinterRepository;
use Symfony\Component\HttpKernel\KernelInterface;

class ZebraPrinter
{
    public const LOCAL_FILESYSTEM = 'local_filesystem';

    private PrinterRepository $printer_repository;

    private MountManager $mount_manager;

    private KernelInterface $kernel;

    private CupsPrinter $cups_printer;

    /** @var int */
    private $printer_id;

    /** @var string */
    private $printer_name;

    /** ZebraPrinter constructor. */
    public function __construct(
        PrinterRepository $printer_repository,
        MountManager $mount_manager,
        KernelInterface $kernel,
        CupsPrinter $cups_printer
    ) {
        $this->printer_repository = $printer_repository;
        $this->mount_manager = $mount_manager;
        $this->kernel = $kernel;
        $this->cups_printer = $cups_printer;
    }

    /**
     * load.
     *
     * @return $this
     *
     * @throws \Exception
     */
    public function load(int $printer_id, string $type = Printer::TYPE_102x152): self
    {
        $printer = $this->printer_repository->findByIdAndType($printer_id, $type);

        if ('active' !== $printer->status) {
            throw new \Exception(sprintf('The printer "%s" is not active.', $printer->name));
        }
        $this->printer_id = $printer->printer_id;
        $this->printer_name = $printer->cups_name;

        return $this;
    }

    /**
     * load.
     *
     * @return $this
     *
     * @throws NotFoundException
     */
    public function loadByName(string $printer_name, string $type = Printer::TYPE_102x152): self
    {
        $printer = $this->printer_repository->findByNameAndType($printer_name, $type);

        if ('active' !== $printer->status) {
            throw new \Exception(sprintf('The printer "%s" is not active.', $printer->name));
        }
        $this->printer_id = $printer->printer_id;
        $this->printer_name = $printer->cups_name;

        return $this;
    }

    /**
     * print.
     *
     * @return $this
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function print(string $filename, string $content): self
    {
        if (null === $this->printer_name) {
            throw new \UnexpectedValueException('The zebra printer is not loaded.');
        }

        // Make a copy of the file to print on the current instance temp directory
        // On prod, since we're using docker with no volumes, those files will be destroyed each new release
        $this->createLocalTemporaryFile($filename, $content);

        // Cups require direct access to the file
        $file_path = sprintf('%s/var/tmp/%s', $this->kernel->getProjectDir(), $filename);

        // Use cups to print the file
        $this->cups_printer->send($file_path, $this->printer_name);

        return $this;
    }

    /**
     * createLocalTemporaryFile.
     *
     * @return $this
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    protected function createLocalTemporaryFile(string $filename, string $content): self
    {
        $local_filesystem = $this->mount_manager->getFilesystem(static::LOCAL_FILESYSTEM);

        // if file exists, remove it
        if ($local_filesystem->has($filename)) {
            $local_filesystem->delete($filename);
        }

        // write the new file in local folder
        $local_filesystem->write($filename, $content);

        return $this;
    }

    /** getPrinterId */
    public function getPrinterId(): int
    {
        return $this->printer_id;
    }
}
