<?php

namespace SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper;

use App\Contract\DataLoaderAwareInterface;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use SonVideo\Erp\Country\Mysql\Repository\CountryRepository;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\CustomerOrder\Contract\CustomerOrderCreationDataMapperInterface;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Entity\CustomerOrderEntity;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderUtils;
use SonVideo\Erp\CustomerOrderProduct\Contract\CustomerOrderProductWarrantiesTrait;
use SonVideo\Erp\Product\Mysql\Repository\ProductV2Repository;
use SonVideo\Erp\Quote\Entity\QuoteSubtypeEntity;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;
use SonVideo\Erp\Referential\CustomerOrderInternalStatus;
use SonVideo\Erp\Referential\CustomerOrderIpAddress;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use SonVideo\Erp\Referential\CustomerType;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\PaymentWorkflow;

abstract class AbstractCreationDataMapper implements CustomerOrderCreationDataMapperInterface, DataLoaderAwareInterface
{
    use MapToEntityTrait;
    use CustomerOrderProductWarrantiesTrait;

    private CountryRepository $country_repository;

    private ShipmentMethodReadRepository $shipment_method_read_repository;

    private QuoteRepository $quote_repository;

    private AccountQueryRepository $account_repository;

    private ProductV2Repository $product_repository;

    private CustomerRepository $customer_repository;

    public function __construct(
        CountryRepository $country_repository,
        ShipmentMethodReadRepository $shipment_method_read_repository,
        QuoteRepository $quote_repository,
        AccountQueryRepository $account_repository,
        ProductV2Repository $product_repository,
        CustomerRepository $customer_repository
    ) {
        $this->country_repository = $country_repository;
        $this->shipment_method_read_repository = $shipment_method_read_repository;
        $this->quote_repository = $quote_repository;
        $this->account_repository = $account_repository;
        $this->product_repository = $product_repository;
        $this->customer_repository = $customer_repository;
    }

    /** {@inheritDoc} */
    abstract public function canHandle(string $key): bool;

    abstract public function getTagsLines(array $data): array;

    /** @throws NotFoundException */
    public function map(array $data): CustomerOrderCreationContextEntity
    {
        $shipping_address_country_id = $this->country_repository->getFrom2LettersCode(
            $data['shipping_address']['country_code']
        )->country_id;

        $customer = null;
        try {
            $customer = $this->customer_repository->getById($data['customer_id']);
        } catch (NotFoundException $ignore) {
        }

        $vat = $this->country_repository->getVat($shipping_address_country_id);

        $customer_order_products = $this->extractCustomerOrderProducts($data, $vat);
        $customer_order_products = $this->adjustDiscounts($customer_order_products);

        // shipment method product
        $customer_order_products[] = CreationDataMapperUtils::getShipmentMethodLine(
            $data['customer_order_id'],
            $data['shipment_method']['cost'],
            $vat
        );

        $customer_order_payments = $this->extractCustomerOrderPayments($data);

        $shipment_method = $this->shipment_method_read_repository->findOneById(
            $data['shipment_method']['shipment_method_id']
        );

        // tags
        $customer_order_tags = $this->getTagsLines($data);

        if (isset($data['quote_id'])) {
            $quote = $this->quote_repository->findById($data['quote_id']);
            $quote_creator = $this->account_repository->findOneById($quote->created_by, null);
            $data['warehouse_id'] = $quote_creator->warehouse_id;

            if (QuoteSubtypeEntity::INTRAGROUP === $quote->get('quote_subtype')) {
                $data['is_intragroup'] = true;
            }

            if (
                QuoteSubtypeEntity::CLASSIQUE === $quote->get('quote_subtype') &&
                    CustomerType::COMPANY === $customer->type ?? CustomerType::PRIVATE
            ) {
                $data['is_b2b'] = true;
                $data['quote_creator_username'] = $quote_creator->utilisateur;
            }

            $customer_order_tags[] = [
                'customer_order_id' => $data['customer_order_id'],
                'tag_id' => CustomerOrderUtils::getTagIdForSvdSource(
                    $quote->get('quote_subtype'),
                    $quote_creator->seller_commission_role,
                    $customer->type ?? CustomerType::PRIVATE
                ),
            ];
        }

        $customer_order_data = array_merge($data, [
            'original_customer_order_id' => $data['customer_order_id'],
            'internal_status' => CustomerOrderInternalStatus::ON_GOING,
            'invoice_comment' => '',
            'billing_address_company_name' => $data['billing_address']['company_name'] ?? '',
            'billing_address_civility' => $data['billing_address']['civility'],
            'billing_address_firstname' => $data['billing_address']['firstname'],
            'billing_address_lastname' => $data['billing_address']['lastname'],
            'billing_address_address' => $data['billing_address']['address'],
            'billing_address_postal_code' => $data['billing_address']['postal_code'],
            'billing_address_city' => $data['billing_address']['city'],
            'billing_address_country_id' => $this->country_repository->getFrom2LettersCode(
                $data['billing_address']['country_code']
            )->country_id,
            'billing_address_phone' => $data['billing_address']['phone'],
            'billing_address_cellphone' => $data['billing_address']['cellphone'],
            'billing_address_email' => $data['billing_address']['email'],
            'shipping_address_company_name' => $data['shipping_address']['company_name'] ?? '',
            'shipping_address_civility' => $data['shipping_address']['civility'],
            'shipping_address_firstname' => $data['shipping_address']['firstname'],
            'shipping_address_lastname' => $data['shipping_address']['lastname'],
            'shipping_address_address' => $data['shipping_address']['address'],
            'shipping_address_postal_code' => $data['shipping_address']['postal_code'],
            'shipping_address_city' => $data['shipping_address']['city'],
            'shipping_address_country_id' => $shipping_address_country_id,
            'shipping_address_phone' => $data['shipping_address']['phone'],
            'shipping_address_cellphone' => $data['shipping_address']['cellphone'],
            'shipping_address_email' => $data['shipping_address']['email'],
            'carrier_id' => $shipment_method->carrier_id,
            'shipment_method_id' => $data['shipment_method']['shipment_method_id'],
            'store_pickup_id' => $shipment_method->store_pickup_id,
            'payments' => $customer_order_payments,
            'products' => $customer_order_products,
            'relay_id' => $data['shipment_method']['relay_id'] ?? null,
            'chrono_precise_appointment' => $data['shipment_method']['chrono_precise_appointment'] ?? null,
            'svd_header' => true,
            'ip_address' => $data['ip_address'] ?? CustomerOrderIpAddress::SITE,
            'tags' => $customer_order_tags,
            'is_excluding_tax' => true === $data['is_excluding_tax'] ? 'oui' : 'non',
        ]);

        /** @var CustomerOrderCreationContextEntity $customer_order */
        $customer_order = $this->hydrateEntity($customer_order_data, CustomerOrderCreationContextEntity::class);

        return $customer_order;
    }

    /** @throws NotFoundException */
    protected function extractCustomerOrderProducts(array $data, float $vat): array
    {
        $customer_order_products = [];
        foreach ($data['products'] as $product) {
            $discount_type = null;
            $discount_description = null;
            $product_info = $this->product_repository->getOneByIdOrSku($product['sku']);

            if (0.0 !== (float) $product['unit_discount_amount']) {
                $discount_type =
                    null !== $data['quote_id']
                        ? CustomerOrderEntity::TYPE_QUOTE
                        : CustomerOrderEntity::TYPE_CUSTOMER_ORDER;
                $discount_description = null !== $data['quote_id'] ? 'Remise devis' : 'Remise exceptionnelle';
            }

            $group_type = $discount_type;
            $group_description = $discount_description;
            $promo_code = null;

            if (null !== $data['promotion_code'] && in_array($product['sku'], $data['promotion_linked_products'])) {
                $group_type = CustomerOrderEntity::TYPE_PROMOCODE;
                $group_description = $data['promotion_code'];
                $promo_code = $data['promotion_code'];
            }

            $select_warranties = $product['selected_warranties'] ?? [];

            $customer_order_products[] = array_merge(
                $product,
                [
                    'customer_order_id' => $data['customer_order_id'],
                    'product_id' => $product_info->product_id,
                    'discount_type' => $discount_type,
                    'discount_description' => $discount_description,
                    'discount_amount' => $product['unit_discount_amount'] * $product['quantity'],
                    'group_type' => $group_type,
                    'group_description' => $group_description,
                    'vat' => $vat,
                    'type' => $product_info->product_type,
                    'promo_code' => $promo_code,
                ],
                $this->extractWarranties($select_warranties)
            );
        }

        return $customer_order_products;
    }

    protected function extractCustomerOrderPayments(array $data): array
    {
        $customer_order_payments = [];
        foreach ($data['payments'] as $key => $payment) {
            if ('TEL' === $payment['payment_mean']) {
                $payment['payment_mean'] = PaymentMean::CREDIT_CARD_OGONE_TELEPHONE;
            }

            $customer_order_payments[] = array_merge($payment, [
                'customer_order_id' => $data['customer_order_id'],
                'unique_id' => $key + 1,
                'origin' => CustomerOrderOrigin::SITE,
                'created_proof' => $data['customer_order_id'] . ('-' . ($key + 1)),
                'workflow' => $payment['workflow'] ?? PaymentWorkflow::LEGACY,
            ]);
        }

        return $customer_order_payments;
    }

    private function adjustDiscounts(array $products): array
    {
        $total_discount = 0.0;
        $total_rounded_discount = 0.0;
        $first_discounted_index = null;

        // round each product discount
        foreach ($products as $index => $product) {
            $total_discount += $product['discount_amount'];
            $discount_amount_rounded = round($product['discount_amount'], 2);
            $total_rounded_discount += $discount_amount_rounded;

            if (null === $first_discounted_index && $discount_amount_rounded < 0) {
                $first_discounted_index = $index;
            }

            $products[$index]['discount_amount'] = $discount_amount_rounded;
        }

        // compensate total discount on the first product having a discount
        if (null !== $first_discounted_index) {
            $total_discount_applied = round($total_discount, 2);
            $products[$first_discounted_index]['discount_amount'] += $total_discount_applied - $total_rounded_discount;
        }

        return $products;
    }
}
