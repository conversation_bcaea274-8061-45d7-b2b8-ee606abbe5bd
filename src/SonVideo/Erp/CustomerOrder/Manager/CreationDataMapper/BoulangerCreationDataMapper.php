<?php

namespace SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper;

use SonVideo\Erp\Referential\CustomerOrderOrigin;
use SonVideo\Erp\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\SalesChannel;

class BoulangerCreationDataMapper extends AbstractMarketplaceCreationDataMapper
{
    protected const CUSTOMER_ORDER_ORIGIN = CustomerOrderOrigin::BOULANGER;
    protected const PAYMENT_MEAN = PaymentMean::BOULANGER;

    public function canHandle(string $key): bool
    {
        return self::CUSTOMER_ORDER_ORIGIN === $key;
    }

    public function overloadData($data): array
    {
        $data['sales_channel_id'] = SalesChannel::BOULANGER;

        return $data;
    }

    public function getTagsLines(int $customer_order_id, array $data): array
    {
        return [
            [
                'customer_order_id' => $customer_order_id,
                'tag_id' => CustomerOrderTag::SOURCE_BOULANGER,
            ],
        ];
    }
}
