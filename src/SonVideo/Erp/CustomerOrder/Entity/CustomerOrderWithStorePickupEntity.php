<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Entity;

use App\Entity\AbstractEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\CommentEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\CustomerOrderPaymentEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\CustomerOrderProductEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\DeliveryNoteEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\InvoiceEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\LastInternalCommentEntity;

/**
 * Class CustomerOrderWithStorePickupEntity.
 */
class CustomerOrderWithStorePickupEntity extends AbstractEntity
{
    public int $customer_order_id;

    public int $warehouse_id;

    public bool $is_shipped;

    public bool $is_detaxed;

    public bool $postal_code_is_valid;

    public \DateTimeInterface $created_at;

    public int $customer_id;

    public string $customer_type;

    public ?string $customer_name = null;

    public ?string $company_name = null;

    public bool $is_blacklisted;

    public float $amount_all_tax_included;

    public ?string $payment_fraud_detection = null;

    /** @var CustomerOrderPaymentEntity[]|null */
    public array $payments = [];

    /** @var CustomerOrderProductEntity[]|null */
    public array $articles = [];

    /** @var DeliveryNoteEntity[]|null */
    public array $delivery_notes = [];

    /** @var InvoiceEntity[]|null */
    public array $invoices = [];

    /** @var LastInternalCommentEntity[]|null */
    public array $last_internal_comment = [];

    /** @var CommentEntity[]|null */
    public array $last_customer_comment = [];
}
