<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Entity\Children;

use App\Entity\AbstractEntity;

/**
 * Class CustomerOrderPaymentEntity.
 */
class CustomerOrderPaymentEntity extends AbstractEntity
{
    public int $customer_order_id;

    public int $payment_id;

    public string $payment_mean;

    public string $payment_name;

    public float $amount;

    public float $creation_amount;

    public ?float $acceptation_amount = null;

    public ?float $cancellation_amount = null;

    public ?float $remit_amount = null;

    public \DateTimeInterface $created_at;

    public ?\DateTimeInterface $accepted_at = null;

    public ?string $auto_status = null;

    public ?string $auto_status_detail = null;

    public ?string $auto_warranty = null;

    public string $type;

    public string $status;
}
