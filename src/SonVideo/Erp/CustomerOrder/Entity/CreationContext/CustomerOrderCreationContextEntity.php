<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Entity\CreationContext;

use App\Entity\AbstractEntity;
use SonVideo\Erp\Referential\SalesChannel;

class CustomerOrderCreationContextEntity extends AbstractEntity
{
    public int $customer_order_id;

    public string $original_customer_order_id;

    public ?int $clone_customer_order_id = null;

    public string $origin;

    public string $internal_status;

    public \DateTimeInterface $created_at;

    public ?\DateTimeInterface $estimated_delivery_date = null;

    public ?int $customer_id = null;

    public ?int $quote_id = null;

    public ?int $warehouse_id = null;

    public string $invoice_comment;

    public string $ip_address;

    public ?string $billing_address_company_name = null;

    public string $billing_address_civility;

    public string $billing_address_firstname;

    public string $billing_address_lastname;

    public string $billing_address_address;

    public string $billing_address_postal_code;

    public string $billing_address_city;

    public int $billing_address_country_id;

    public string $billing_address_phone;

    public string $billing_address_cellphone;

    public string $billing_address_email;

    public ?string $billing_vat_number = null;

    public ?string $shipping_address_company_name = null;

    public string $shipping_address_civility;

    public string $shipping_address_firstname;

    public string $shipping_address_lastname;

    public string $shipping_address_address;

    public string $shipping_address_postal_code;

    public string $shipping_address_city;

    public int $shipping_address_country_id;

    public string $shipping_address_phone;

    public string $shipping_address_cellphone;

    public string $shipping_address_email;

    public ?int $carrier_id = null;

    public ?int $shipment_method_id = null;

    public ?int $store_pickup_id = null;

    public ?string $relay_id = null;

    public bool $svd_header;

    public ?string $quote_creator_username = null;

    public string $is_excluding_tax = 'non';

    public int $sales_channel_id = SalesChannel::SON_VIDEO;

    public ?int $promotion_id = null;

    public bool $is_intragroup = false;

    public bool $is_b2b = false;

    public bool $new_customer = false;

    /** @var CustomerOrderTagEntity[]|null */
    public ?array $tags = [];

    /** @var CustomerOrderPaymentCreationContextEntity[]|null */
    public ?array $payments = [];

    /** @var CustomerOrderProductCreationContextEntity[]|null */
    public array $products = [];

    public ?CustomerOrderCreationChronoPreciseAppointmentContextEntity $chrono_precise_appointment = null;

    public function hasTag(string $name): bool
    {
        /** @var CustomerOrderTagEntity $tag */
        foreach ($this->tags as $tag) {
            if ($tag->tag_id === $name) {
                return true;
            }
        }

        return false;
    }
}
