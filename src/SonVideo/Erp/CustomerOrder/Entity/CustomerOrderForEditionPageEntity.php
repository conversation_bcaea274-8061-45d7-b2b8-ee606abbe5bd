<?php

namespace SonVideo\Erp\CustomerOrder\Entity;

use App\DataLoader\Type\JsonType;
use OpenApi\Annotations as OA;

class CustomerOrderForEditionPageEntity
{
    public int $customer_order_id;

    public ?string $original_customer_order_id = null;

    public ?string $origin = null;

    public ?string $sales_channel_origin = null;

    public ?int $sales_channel_id = null;

    public string $created_at;

    public string $modified_at;

    public string $status;

    public string $computed_status;

    public int $customer_id;

    public ?int $number_of_visits = null;

    public ?string $relay_id = null;

    public ?int $quote_id = null;

    public ?string $ip_address = null;

    public string $invoicing_mode;

    public bool $should_recall_customer;

    public bool $is_store_pickup;

    public bool $is_detaxed;

    public bool $is_intragroup;

    public bool $has_ongoing_premium_warranty;

    public bool $is_paid;

    public bool $has_inconsistent_carrier;

    public float $total_price_vat_included;

    public float $total_price_vat_excluded;

    public float $ecotax_price;

    public float $total_accepted_amount;

    public float $total_remitted_amount;

    public float $vat_rate;

    public float $shipping_price;

    public ?int $pickup_store_id = null;

    public ?int $warehouse_id = null;

    public ?int $carrier_id = null;

    public ?int $shipment_method_id = null;

    public ?string $max_delivery_date = null;

    public ?string $confirm_email_date = null;

    public ?string $sms_date = null;

    public ?string $store_label = null;

    /**
     * @var array|JsonType|null
     * @OA\Property(type="object")
     */
    public ?array $billing_address = null;

    /**
     * @var array|JsonType|null
     * @OA\Property(type="object")
     */
    public ?array $shipping_address = null;

    /**
     * @var array|JsonType|null
     * @OA\Property(type="object")
     */
    public ?array $aggregated_products = null;

    /**
     * @var array|JsonType|null
     * @OA\Property(type="object")
     */
    public ?array $payments = null;

    /**
     * @var array|JsonType|null
     * @OA\Property(type="object")
     */
    public ?array $delivery_note_ids = null;

    /**
     * @var array|JsonType|null
     * @OA\Property(type="object")
     */
    public ?array $tags = null;
}
