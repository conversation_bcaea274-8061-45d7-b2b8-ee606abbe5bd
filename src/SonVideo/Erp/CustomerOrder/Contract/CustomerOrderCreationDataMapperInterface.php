<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Contract;

use App\Contract\Collection\CollectableInterface;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;

interface CustomerOrderCreationDataMapperInterface extends CollectableInterface
{
    public function map(array $data): CustomerOrderCreationContextEntity;
}
