<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Manager;

class HeraldMailjetUtility
{
    private const APP_DEFAULT_CULTURE = 'FR';
    private const APP_CURRENCY = 'EUR';

    /** @return array{to: mixed, cc: mixed, bcc: mixed, cid: mixed} */
    public static function extractDataFromResponseForLogging(array $response): array
    {
        $extracted = $response['result']['data']['Messages'][0];

        return [
            'to' => $extracted['To'],
            'cc' => $extracted['Cc'],
            'bcc' => $extracted['Bcc'],
            'cid' => '' !== $extracted['CustomID'] ? $extracted['CustomID'] : null,
        ];
    }

    /** Format a value to the app currency. */
    public static function formatToCurrency(float $value): string
    {
        return \NumberFormatter::create(self::APP_DEFAULT_CULTURE, \NumberFormatter::CURRENCY)->formatCurrency(
            $value,
            self::APP_CURRENCY
        );
    }

    /** Format a value to the app currency without the decimals if there are only trailing zeroes. */
    public static function formatWithoutTrailingZeroDecimals(float $value): string
    {
        $formatted_with_zero = self::formatToCurrency($value);

        $formatter = \NumberFormatter::create(self::APP_DEFAULT_CULTURE, \NumberFormatter::CURRENCY);
        $decimal_separator = $formatter->getSymbol(\NumberFormatter::DECIMAL_SEPARATOR_SYMBOL);

        if ('0' === $decimal_separator) {
            return $formatted_with_zero;
        }

        return str_replace(sprintf('%s00', $decimal_separator), '', $formatted_with_zero);
    }

    /**
     * Simple \Datetime wrapper to format a date to french format for our emails.
     *
     * @throws \Exception
     */
    public static function formatDateToFrenchFormat(string $date): string
    {
        return (new \DateTime($date))->format('d/m/Y');
    }
}
