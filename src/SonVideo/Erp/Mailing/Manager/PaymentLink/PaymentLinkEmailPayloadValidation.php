<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Manager\PaymentLink;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;

class PaymentLinkEmailPayloadValidation
{
    public static function rules(): Constraint
    {
        return new Assert\Collection([
            // recipient of the email
            'to' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
            // sender of the email
            'from' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'email' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
                    'name' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                ]),
            ]),
            // Context contain the template variables required in the email content
            'context' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'subject' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                    'content' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                    'payment_link' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Url(),
                        new Assert\Type('string'),
                    ]),
                    'customer_order_id' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                ]),
            ]),
            // Business key used for logging after the email has been successfully sent
            '_rel' => new Assert\Required([
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'customer_order' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                ]),
            ]),
            // Indicates which legacy user_id triggered the email
            '_sent_by' => new Assert\Required([new Assert\NotBlank(), new Assert\NotNull(), new Assert\Type('integer')]),
        ]);
    }
}
