<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Manager\ConfirmStorePickup;

use League\Flysystem\FileNotFoundException;
use League\Flysystem\MountManager;
use SonVideo\Erp\Mailing\Manager\AbstractEmailDispatcher;
use SonVideo\Erp\Mailing\Manager\EmailSystemEventFactory;
use SonVideo\Erp\Mailing\ValueObject\MailjetAttachment;
use Symfony\Component\Validator\Constraint;

final class ConfirmStorePickupEmailDispatcher extends AbstractEmailDispatcher
{
    public const LEGACY_FILESYSTEM_NAME = 'legacy_filesystem';

    protected const BUSINESS_KEY = 'confirm_store_pickup';
    protected const EMAIL_SENDER = [
        'name' => 'Son-Vidéo.com',
        'email' => '<EMAIL>',
    ];

    private const INVOICE_FILE_PATH = 'sonvideopro.com/data/backoffice/factures/';

    private MountManager $mount_manager;

    public function __construct(MountManager $mount_manager, EmailSystemEventFactory $email_system_event_factory)
    {
        $this->mount_manager = $mount_manager;
        parent::__construct($email_system_event_factory);
    }

    /** {@inheritDoc} */
    protected function getValidationRules(array $data): Constraint
    {
        return ConfirmStorePickupPickupEmailPayloadValidation::rules();
    }

    /**
     *  {@inheritDoc}
     *
     * @throws FileNotFoundException
     */
    protected function getAttachments(array $data): array
    {
        // by default, we always want to send the invoice
        if (false === $this->getOption('send_invoice') ?? true) {
            return [];
        }

        // Load invoice PDF (only on s3 or if copied locally in legacy_adapter)
        $filesystem = $this->mount_manager->getFilesystem(self::LEGACY_FILESYSTEM_NAME);
        $filename = sprintf(
            '%s%s%s.pdf',
            self::INVOICE_FILE_PATH,
            date('Y/m/d/', strtotime($data['context']['customer_order']['invoiced_at'])),
            $data['_rel']['invoice']
        );

        // The invoice PDF must exist
        if (!$filesystem->has($filename)) {
            throw new \UnexpectedValueException(sprintf('Invoice not found at "%s"', $filename));
        }

        // Extract file raw content, base64 encoded in the attachments key
        return [
            new MailjetAttachment(
                $filesystem->read($filename),
                sprintf('%s.pdf', $data['_rel']['invoice']),
                MailjetAttachment::TYPE_PDF
            ),
        ];
    }
}
