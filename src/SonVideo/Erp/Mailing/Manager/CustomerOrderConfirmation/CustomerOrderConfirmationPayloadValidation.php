<?php

namespace SonVideo\Erp\Mailing\Manager\CustomerOrderConfirmation;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;

class CustomerOrderConfirmationPayloadValidation
{
    public static function rules(): Constraint
    {
        return new Assert\Collection([
            'to' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
            'context' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer_order_id' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'created_at' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                    'customer' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Collection([
                            'customer_id' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('integer'),
                            ]),
                            'civility' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'firstname' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                            'lastname' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                            'email' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Email(),
                            ]),
                            'company' => new Assert\Required([new Assert\Type('string')]),
                            'postal_code' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'city' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'country_name' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'phone' => new Assert\Required([new Assert\Type('string')]),
                            'office_phone' => new Assert\Required([new Assert\Type('string')]),
                            'mobile_phone' => new Assert\Required([new Assert\Type('string')]),
                        ]),
                    ]),
                    'delivery_address' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Collection([
                            'company' => new Assert\Required([new Assert\Type('string')]),
                            'firstname' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'lastname' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'civility' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'address' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'postal_code' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'city' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'country_name' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                            'phone' => new Assert\Required([new Assert\Type('string')]),
                            'office_phone' => new Assert\Required([new Assert\Type('string')]),
                            'mobile_phone' => new Assert\Required([new Assert\Type('string')]),
                            'pickup_store_id' => new Assert\Required([new Assert\Type('integer')]),
                            'store' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\Collection([
                                    'city' => new Assert\Required([new Assert\Type('string')]),
                                    'address' => new Assert\Required([new Assert\Type('string')]),
                                    'postal_code' => new Assert\Required([new Assert\Type('string')]),
                                    'url' => new Assert\Required([new Assert\Type('string')]),
                                ]),
                            ]),
                        ]),
                    ]),
                    'is_quotation_for_abroad_delivery' => new Assert\Required([
                        new Assert\NotNull(),
                        new Assert\Type('boolean'),
                    ]),
                    'articles' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Type('array'),
                        new Assert\All([
                            new Assert\Collection([
                                'sku' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('string'),
                                ]),
                                'quantity' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('integer'),
                                ]),
                                'description' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                                'selling_price' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('numeric'),
                                ]),
                                'picture' => new Assert\Required([new Assert\Type('string'), new Assert\NotNull()]),
                                'warranty_duration' => new Assert\Required([new Assert\Type('integer')]),
                                'warranty_price' => new Assert\Required([new Assert\Type('numeric')]),
                            ]),
                        ]),
                    ]),
                    'prices' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Collection([
                            'discount_amount' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'delivery_fees' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'extra_cost' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                            'selling_price_tax_included' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('numeric'),
                            ]),
                        ]),
                    ]),
                    'payments' => new Assert\Required([
                        new Assert\Type('array'),
                        new Assert\All([
                            new Assert\Collection([
                                'description' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                                'amount' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('numeric'),
                                ]),
                            ]),
                        ]),
                    ]),
                ]),
            ]),
            '_sent_by' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Type('integer'),
            ]),
            '_rel' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'customer_order' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                ]),
            ]),
        ]);
    }
}
