<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Manager\SendGiftCard;

use SonVideo\Erp\Mailing\Manager\AbstractEmailDispatcher;
use <PERSON><PERSON>fony\Component\Validator\Constraint;

final class SendGiftCardEmailDispatcher extends AbstractEmailDispatcher
{
    protected const BUSINESS_KEY = 'send_gift_card';
    protected const EMAIL_SENDER = [
        'name' => 'Son-Vidéo.com',
        'email' => '<EMAIL>',
    ];

    /** {@inheritDoc} */
    protected function getValidationRules(array $data): Constraint
    {
        return SendGiftCardPayloadValidation::rules();
    }

    /** {@inheritDoc} */
    protected function formatPayload(array $payload): array
    {
        $payload['context']['ts'] = time();

        return $payload;
    }
}
