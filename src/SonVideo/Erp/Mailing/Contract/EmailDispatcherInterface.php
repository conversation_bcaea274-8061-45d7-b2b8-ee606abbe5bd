<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Contract;

use App\Contract\Collection\CollectableInterface;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;

interface EmailDispatcherInterface extends CollectableInterface
{
    public function dispatch(array $data): ?LoggableSystemEvent;
}
