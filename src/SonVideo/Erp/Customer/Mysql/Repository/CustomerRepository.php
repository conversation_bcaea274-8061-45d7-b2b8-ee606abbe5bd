<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Customer\Mysql\Repository;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Helper\Where;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Customer\Dto\CustomerUpdateRequestDto;
use SonVideo\Erp\Entity\CustomerEntity;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use SonVideo\Erp\User\Entity\UserEntity;

class CustomerRepository extends AbstractLegacyRepository implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    public const COLUMNS_MAPPING = [
        'customer_id' => 'p.id_prospect',
        'email_address' => 'p.email',
        'type' => 'p.type',
        'civility' => 'p.cnt_civilite',
        'firstname' => 'p.cnt_prenom',
        'lastname' => 'p.cnt_nom',
        'company_name' => 'p.societe',
        'address' => 'p.cnt_adresse',
        'zip_code' => 'p.cnt_code_postal',
        'country_id' => 'p.cnt_id_pays',
        'phone' => 'p.cnt_telephone',
        'mobile_phone' => 'p.cnt_mobile',
        'customer_type' => 'p.cnt_type',
        'is_blacklisted' => 'p.blacklist',
        'accept_marketing_emails' => 'p.envoi_email',
        'created_at' => 'p.date_creation',
        'modified_at' => 'p.date_modification',
        'reversed_phone' => 'p.cnt_telephone_reversed',
        'reversed_mobile_phone' => 'p.cnt_mobile_reversed',
        'encours_interne' => 'p.encours_interne',
        'encours_sfac' => 'p.encours_sfac',
        'tva_number' => 'p.cnt_numero_tva',
        'classification' => 'p.classification',
        'balance_acceptance' => 'p.acceptation_relicat',
        'atradius' => 'p.atradius',
        'incoterm' => 'p.incoterm',
        'npai' => 'p.npai',
    ];

    /** @return CustomerEntity[] */
    public function findWhere(Where $where): array
    {
        $sql = <<<SQL
        SELECT * FROM backOffice.prospect {where}
        SQL;
        $sql = strtr($sql, ['{where}' => $where]);
        $result = $this->legacy_pdo->fetchAssoc($sql, $where->getParams());

        $results = [];
        foreach ($result as $order) {
            $results[] = $this->data_loader->hydrate($order, CustomerEntity::class);
        }

        return $results;
    }

    /** @throws NotFoundException */
    public function getById(int $customer_id): CustomerEntity
    {
        $results = $this->findWhere((new Where())->addCondition('id_prospect', 'prospect', $customer_id));

        if (1 !== count($results)) {
            throw new NotFoundException(sprintf('Customer with id %d not found.', $customer_id));
        }

        return $results[0];
    }

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        SELECT SQL_CALC_FOUND_ROWS
          tmp.*,
          concat(tmp.civility, ' ', upper(tmp.firstname), ' ', upper(tmp.lastname)) AS computed_name,
          pa.pays             AS country_name,
          pa.code_2_lettres   AS country_code,
          backOffice.PCT_has_ongoing_premium_warranty(tmp.customer_id) AS has_ongoing_premium_warranty
        FROM
            (
              SELECT
                p.id_prospect       AS customer_id,
                p.email             AS email_address,
                p.type              AS type,
                p.cnt_civilite      AS civility,
                IF(p.cnt_prenom != '', p.cnt_prenom, p.prenom) AS firstname,
                IF(p.cnt_nom != '', p.cnt_nom, p.nom) AS lastname,
                p.societe           AS company_name,
                p.cnt_adresse       AS address,
                p.cnt_code_postal   AS zip_code,
                p.cnt_id_pays       AS country_id,
                p.cnt_telephone     AS phone,
                p.cnt_mobile        AS mobile_phone,
                p.cnt_type          AS customer_type,
                p.blacklist         AS is_blacklisted,
                p.envoi_email       AS accept_marketing_emails,
                p.date_creation     AS created_at,
                p.date_modification AS modified_at,
                p.date_naissance    AS birthdate,
                p.encours_interne   AS encours_interne,
                p.encours_sfac      AS encours_sfac,
                p.cnt_numero_tva    AS tva_number,
                p.classification    AS classification,
                p.acceptation_relicat AS balance_acceptance,
                p.atradius          AS atradius,
                p.incoterm          AS incoterm,
                p.npai              AS npai
                FROM
                  backOffice.prospect          p
                    INNER JOIN backOffice.pays pa ON p.cnt_id_pays = pa.id_pays
                WHERE {conditions}
              ) tmp
              INNER JOIN backOffice.pays pa ON tmp.country_id = pa.id_pays
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /** @throws SqlErrorMessageException */
    public function getOrCreateAccount(int $customer_id, string $email, string $origin = null)
    {
        $sql = <<<SQL
        SELECT id_prospect FROM backOffice.prospect WHERE id_prospect = :customer_id
        SQL;

        $customer = $this->legacy_pdo->fetchOne($sql, ['customer_id' => $customer_id]);

        if (false !== $customer) {
            return $customer['id_prospect'];
        }

        return $this->createAccount($customer_id, $email, $origin);
    }

    /** @throws SqlErrorMessageException */
    public function createAccount(
        int $customer_id,
        string $email,
        string $origin = CustomerOrderOrigin::BACKOFFICE
    ): ?string {
        $sql = <<<SQL
        CALL backOffice.PCT_prospect__add(:customer_id, :email, :origin);
        SQL;

        return $this->legacy_pdo->fetchValue($sql, [
            'customer_id' => $customer_id,
            'email' => $email,
            'origin' => $origin,
        ]);
    }

    /**
     * @return mixed|string
     *
     * @throws SqlErrorMessageException
     */
    public function anonymize(UserEntity $user, int $customer_id): ?string
    {
        $sql = <<<SQL
        CALL backOffice.PCT_RGPD_anonymize(:customer_id, :username );
        SQL;

        return $this->legacy_pdo->fetchValue($sql, ['customer_id' => $customer_id, 'username' => $user->utilisateur]);
    }

    /**
     * @return mixed|string
     *
     * @throws SqlErrorMessageException
     */
    public function updateEmail(string $old_email, string $new_email): ?string
    {
        $sql = <<<SQL
        CALL backOffice.PCT_update_email(:old_email, :new_email);
        SQL;

        return $this->legacy_pdo->fetchValue($sql, ['old_email' => $old_email, 'new_email' => $new_email]);
    }

    public function fetchCustomerOrdersAggregates(array $customer_ids): array
    {
        $sql = <<<SQL
        SELECT
          tmp.customer_id AS customer_id,
          COALESCE(tmp2.paid_customer_orders, 0) AS count,
          SUM(tmp.amount) AS revenue_generated
          FROM
            (
              SELECT
                p.id_prospect AS customer_id,
                CASE WHEN backOffice.CMD_is_paid(c.flux, c.V_statut_traitement)
                  THEN COALESCE(c.V_montant_ttc, 0)
                  ELSE 0 END AS amount
                FROM
                  backOffice.prospect p
                    LEFT JOIN backOffice.commande c ON p.id_prospect = c.id_prospect
                WHERE p.id_prospect IN (:customer_ids)
              ) tmp LEFT JOIN
            (
              SELECT
                p.id_prospect AS customer_id,
                COUNT(p.id_prospect) AS paid_customer_orders
                FROM
                  backOffice.prospect p
                    INNER JOIN backOffice.commande c ON p.id_prospect = c.id_prospect
                WHERE p.id_prospect IN (:customer_ids)
                AND backOffice.CMD_is_paid(c.flux, c.V_statut_traitement)
              GROUP BY p.id_prospect
              ) tmp2 ON tmp.customer_id = tmp2.customer_id
          GROUP BY tmp2.customer_id
        ;
        SQL;

        return $this->legacy_pdo->fetchAssoc($sql, ['customer_ids' => $customer_ids]);
    }

    public function updateCustomerWith(CustomerUpdateRequestDto $customer_update_request_dto): int
    {
        $sql = <<<SQL
        UPDATE backOffice.prospect
          SET
        type = :type,
        cnt_numero_tva = :tva_number,
        blacklist = :blacklist,
        envoi_email = :accept_marketing_emails,
        societe = :company_name,
        encours_sfac = :encours_sfac,
        classification = :classification,
        acceptation_relicat = :balance_acceptance,
        atradius = :atradius,
        npai = :npai,
        incoterm = :incoterm
          WHERE id_prospect = :customer_id
        SQL;

        $this->legacy_pdo->fetchAffected($sql, $this->serializer->normalize($customer_update_request_dto));

        return $customer_update_request_dto->customer_id;
    }
}
