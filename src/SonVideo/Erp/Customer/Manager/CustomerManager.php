<?php

namespace SonVideo\Erp\Customer\Manager;

use App\Client\GraphQLClient;
use App\Exception\SqlErrorMessageException;
use App\Sql\LegacyPdo;
use Exception;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Customer\Contract\CustomerFromCmsInterface;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\Referential\Rpc\BoCmsRpcMethodReferential;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Synapps\Client\RpcClientAwareInterface;
use SonVideo\Synapps\Client\RpcClientAwareTrait;
use UnexpectedValueException;

class CustomerManager implements RpcClientAwareInterface, LoggerAwareInterface, CustomerFromCmsInterface
{
    use RpcClientAwareTrait;
    use LoggerAwareTrait;

    private const ORIGIN = 'erp-server';
    private const LOCALE = 'fr';
    private const FOCMS_RESET_PASSWORD_RELATIVE_URL = '/mon-compte/changer-mot-de-passe/{TOKEN}';
    private const FOCMS_RESET_EMAIL_RELATIVE_URL = '/mon-compte/confirmer-nouvel-email/{TOKEN}';

    private string $fo_cms_url;

    private LegacyPdo $legacy_pdo;

    private CustomerRepository $customer_repository;

    private GraphQLClient $graphql_client;

    private CurrentUser $current_user;

    /** CustomerManager constructor. */
    public function __construct(
        string $fo_cms_url,
        LegacyPdo $legacy_pdo,
        CustomerRepository $customer_repository,
        GraphQLClient $graphql_client,
        CurrentUser $current_user
    ) {
        $this->fo_cms_url = $fo_cms_url;
        $this->legacy_pdo = $legacy_pdo;
        $this->customer_repository = $customer_repository;
        $this->graphql_client = $graphql_client;
        $this->current_user = $current_user;
    }

    public function callGetCustomerByEmail(string $customer_email): array
    {
        $response = $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_GET_METHOD,
            [utf8_encode($customer_email)]
        );

        if (!isset($response['result']['status'])) {
            throw new UnexpectedValueException('Could not retrieve customer account on BO-CMS via JSON RPC (status is missing)');
        }

        return $response['result'];
    }

    /** @throws SqlErrorMessageException */
    public function createAccountByEmail(string $customer_email, string $origin = null)
    {
        $response = $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_GET_OR_CREATE_METHOD,
            [$customer_email]
        );

        if (!isset($response['result']['status'])) {
            throw new UnexpectedValueException('Fail to create customer in CMS');
        }

        $account = $response['result']['account'];

        return $this->customer_repository->getOrCreateAccount($account['customer_id'], $account['email'], $origin);
    }

    /** @throws SqlErrorMessageException */
    public function createIfNotExist(int $customer_id, string $customer_email, string $origin): void
    {
        $this->customer_repository->getOrCreateAccount($customer_id, $customer_email, $origin);
    }

    /** @throws Exception */
    public function createActiveAccountByEmail(string $customer_email): array
    {
        $random = str_shuffle(
            substr(str_shuffle('**********'), 0, 2) .
                substr(str_shuffle('abcdefghijklmnopqrstvwxyz'), 0, 2) .
                substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXZ'), 0, 2) .
                substr(str_shuffle('!?&#$*'), 0, 2)
        );
        $url = $this->fo_cms_url . self::FOCMS_RESET_PASSWORD_RELATIVE_URL;
        $origin = self::ORIGIN;
        $locale = self::LOCALE;

        $responseAccount = $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_CREATE_ACTIVE_ACCOUNT_METHOD,
            [$customer_email, $random, $url, $locale, $origin]
        );

        if (!isset($responseAccount['result']) || !isset($responseAccount['result']['account'])) {
            throw new Exception('An error occurred while creating a new account in the CMS');
        }

        $account = $responseAccount['result']['account'];

        $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_ADDRESSES_CREATE_IF_NOT_EXISTS_METHOD,
            [$account['customer_id']]
        );

        return $account;
    }

    /** @throws Exception */
    public function anonymize(int $customer_id): void
    {
        try {
            $this->legacy_pdo->beginTransaction();

            $this->customer_repository->anonymize($this->current_user->entity(), $customer_id);

            $this->rpc_client->call(
                BoCmsRpcMethodReferential::SERVER_NAME,
                BoCmsRpcMethodReferential::CUSTOMER_ANONYMIZE_METHOD,
                [$customer_id]
            );

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }
    }

    /** @throws SqlErrorMessageException */
    public function updateEmail(string $old_email, string $new_email): void
    {
        try {
            $this->legacy_pdo->beginTransaction();

            $this->customer_repository->updateEmail($old_email, $new_email);
            $this->rpc_client->call(
                BoCmsRpcMethodReferential::SERVER_NAME,
                BoCmsRpcMethodReferential::CUSTOMER_UPDATE_EMAIL_METHOD,
                [$old_email, $new_email]
            );

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }
    }

    public function askChangeEmail(string $old_email, string $new_email): void
    {
        $url = $this->fo_cms_url . self::FOCMS_RESET_EMAIL_RELATIVE_URL;
        $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_ASK_CHANGE_EMAIL_METHOD,
            [$old_email, $new_email, $url, self::LOCALE]
        );
    }

    public function createAddress(int $customer_id, array $address): array
    {
        $response = $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_ADDRESSES_CREATE_METHOD,
            [$customer_id, json_encode($address, JSON_THROW_ON_ERROR)]
        );

        if (true !== $response['result']['status']) {
            throw new \UnexpectedValueException('fail to create address in CMS');
        }

        return $response['result'];
    }

    /** @throws Exception */
    public function deleteAddress(int $customer_id, int $index): void
    {
        $response = $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_ADDRESSES_DELETE_METHOD,
            [$customer_id, $index]
        );

        if (true !== $response['result']['status']) {
            throw new \UnexpectedValueException('fail to delete address in CMS');
        }
    }

    public function updateAddress(int $customer_id, array $address, int $index): array
    {
        $response = $this->rpc_client->call(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_ADDRESSES_UPDATE_METHOD,
            [$customer_id, json_encode($address, JSON_THROW_ON_ERROR), $index]
        );

        if (true !== $response['result']['status']) {
            throw new \UnexpectedValueException('fail to update address in CMS');
        }

        return $response['result'];
    }

    /** @param string[] $fields (addresses, birthday, cellphone, firstname, lastname, phone, preferences, pseudonym, title) */
    public function getCmsAccountInformations(int $customer_id, array $fields = []): array
    {
        $query /** @lang GraphQL */ = <<<'GQL'
        query getFeatureFlagsFromCms($customer_id: Int!) {
          customer: customer_account_information_by_pk(customer_id: $customer_id) {
            preferences
          }
          global: cms_system_parameter_by_pk(name: "feature.flags") {
            value
          }
        }
        GQL;

        $result = $this->graphql_client->call([
            'query' => strtr($query, ['pseudonym' => implode(',', $fields)]),
            'variables' => [
                'customer_id' => $customer_id,
            ],
        ])['data'];

        $infos = $result['customer'] ?? [];

        // Merge scoped features flags with global feature flags
        $infos['preferences']['feature_flags'] = array_merge(
            $infos['preferences']['feature_flags'] ?? [],
            json_decode($result['global']['value'] ?? '[]', true, 512, JSON_THROW_ON_ERROR)
        );

        return $infos;
    }
}
