<?php

namespace SonVideo\Erp\Customer\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Database\PgErpServer\SystemSchema\EventModel;
use App\Exception\NotFoundException;
use SonVideo\Erp\System\Contract\FormatMysqlEventsTrait;
use SonVideo\Erp\System\Entity\SystemEventEntity;
use SonVideo\Erp\System\Mysql\Repository\SystemEventRepository;

class CustomerEventFetcher implements DataLoaderAwareInterface, SerializerAwareInterface
{
    use SerializerAwareTrait;
    use FormatMysqlEventsTrait;
    use DataLoaderAwareTrait;

    private EventModel $event_model;
    private SystemEventRepository $system_event_repository;

    public function __construct(EventModel $event_model, SystemEventRepository $system_event_repository)
    {
        $this->event_model = $event_model;
        $this->system_event_repository = $system_event_repository;
    }

    /**
     * @throws NotFoundException
     * @throws \Exception
     */
    public function fetchAll(int $customer_id, string $type): array
    {
        $mysql_events = $this->system_event_repository->findAllByQuoteId($customer_id, $type);

        // formatting date
        $mysql_events = $this->formatMysqlEvents($mysql_events);

        $events = array_merge($mysql_events);

        usort($events, fn ($event_a, $event_b): int => $event_a['created_at'] < $event_b['created_at'] ? 1 : -1);

        // sort events by created_at desc
        $events = array_map(static function (array $event) {
            $event['created_at'] = $event['created_at']->format('Y-m-d H:i:s');

            return $event;
        }, $events);

        return $this->serializer->denormalize($events, SystemEventEntity::class . '[]');
    }
}
