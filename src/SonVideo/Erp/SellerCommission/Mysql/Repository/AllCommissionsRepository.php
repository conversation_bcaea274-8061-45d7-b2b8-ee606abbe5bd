<?php

namespace SonVideo\Erp\SellerCommission\Mysql\Repository;

use App\DataLoader\MapToEntityTrait;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\SellerCommission\Entity\AllCommissionsEntity;

class AllCommissionsRepository extends AbstractLegacyRepository
{
    use MapToEntityTrait;

    public function findAllBetween(string $begin, string $end): array
    {
        $sql = <<<SQL
        SELECT
                COALESCE(ROUND(t.total_turnover_tax_excluded) + ROUND(t.total_ldw_amount_tax_excluded),0) AS turnover_tax_excluded,
                COALESCE(ROUND(t.total_turnover_tax_excluded),0) AS product_turnover_tax_excluded,
                COALESCE(ROUND(t.total_ldw_amount_tax_excluded),0) AS GLD_turnover_tax_excluded,
                COALESCE(ROUND(t.total_margin_tax_excluded),0) AS margin_tax_excluded,
                COALESCE(ROUND(t.total_margin_tax_excluded *100 /(t.total_turnover_tax_excluded+t.total_ldw_amount_tax_excluded) ,2),0) AS markup_rate,
                t.full_name,
                t.username
            FROM (SELECT
                backOffice.format_person_fullname(sgup.prenom, sgup.nom) AS full_name,
                sgu.username,
                sum(CASE WHEN ip.invoice_created_at BETWEEN :begin AND :end THEN ip.margin_tax_excluded  ELSE 0 END) AS total_margin_tax_excluded,
                sum(CASE WHEN ip.invoice_created_at BETWEEN :begin AND :end THEN ip.turnover_tax_excluded  ELSE 0 END) AS total_turnover_tax_excluded,
                sum(CASE WHEN ip.invoice_created_at BETWEEN :begin AND :end THEN ip.ldw_amount_tax_excluded  ELSE 0 END) AS total_ldw_amount_tax_excluded
                FROM backOffice.sf_guard_user_profile sgup
                INNER JOIN backOffice.sf_guard_user sgu ON sgup.id = sgu.id
                LEFT JOIN data_warehouse.invoice_product ip ON sgup.id = ip.user_id
                WHERE sgu.is_active = 1
                GROUP BY full_name
                ) AS t
            GROUP BY t.full_name
            ORDER BY 2 DESC;
        SQL;

        return $this->mapToEntities(
            $this->legacy_pdo->fetchObjects($sql, ['begin' => $begin, 'end' => $end]),
            AllCommissionsEntity::class
        );
    }
}
