<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Shipment\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

class ShipmentDeliveryNoteRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'shipment_id' => 'e.id',
        'shipment_delivery_note_id' => 'ebl.id',
        'delivery_note_id' => 'bl.id_bon_livraison',
        'parcel_weight' => 'COALESCE(ebl.maj_poids, SUM(pbl.quantite * a.poids))',
        'have_insurrance' => 'IF(bl.lvr_assurance = "Y", 1, 0)',
        'shipment_method_id' => 'tp.code_produit',
        'shipment_method_name' => 'tp.libelle_produit',
        'carrier_id' => 'tpt.id_transporteur',
        'carrier_name' => 'tpt.transporteur',
        'parcel_quantity' => 'COUNT(pbl.quantite)',
        'status' => 'ebl.statut',
        'customer.civility' => 'bl.cnt_civilite',
        'customer.lastname' => 'TRIM(bl.cnt_nom)',
        'customer.firstname' => 'TRIM(bl.cnt_prenom)',
        'customer.company' => 'TRIM(bl.cnt_societe)',
        'customer.postal_code' => 'bl.cnt_code_postal',
        'customer.city' => 'bl.cnt_code_postal',
        'customer.email' => 'bl.cnt_email',
        'customer.country' => 'p.pays',
        'customer.country_id' => 'p.id_pays',
    ];

    /** Fetch the shipment delivery notes */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<'SQL'
        SELECT SQL_CALC_FOUND_ROWS *
        FROM ( {base_sql} ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getbaseSql($query_builder->getWhere()),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /*
     * Find a shipment delivery note by a delivery note id
     * @return false|array
     */
    public function findOneByDeliveryNoteId(int $delivery_note_id)
    {
        $sql = $this->getbaseSql('bl.id_bon_livraison = :delivery_note_id');

        return $this->legacy_pdo->fetchOne($sql, ['delivery_note_id' => $delivery_note_id]);
    }

    /** Get the base SQL for shipment_delivery_note */
    private function getbaseSql(string $condition = ''): string
    {
        $base_sql = <<<'SQL'
        SELECT
            bl.shipment_delivery_note_id,
            bl.delivery_note_id,
            bl.parcel_weight,
            bl.carrier_id,
            bl.shipment_method_id,
            bl.shipment_method_name,
            bl.carrier_name,
            bl.have_insurance,
            bl.status,
            bl.customer,
            bl.shipment_id,
            COUNT(eblc.id) AS parcel_quantity,
            -- JSON_ARRAYAGG doesn't perform DISTINCT operation
            CONCAT('[', GROUP_CONCAT(DISTINCT JSON_OBJECT('id', eblc.id, 'number', eblc.no_colis)),']') AS parcels
        FROM (
            SELECT
                ebl.id AS shipment_delivery_note_id,
                bl.id_bon_livraison AS delivery_note_id,
                COALESCE(ebl.maj_poids, SUM(pbl.quantite * a.poids)) AS parcel_weight,
                IF(bl.lvr_assurance = 'Y', 1, 0) AS have_insurance,
                tp.id AS shipment_method_id,
                tp.libelle_produit AS shipment_method_name,
                tpt.id_transporteur AS carrier_id,
                tpt.transporteur AS carrier_name,
                ebl.statut AS status,
                e.id AS shipment_id,
                JSON_OBJECT(
                    'civility', bl.cnt_civilite,
                    'firstname', TRIM(bl.cnt_prenom),
                    'lastname', TRIM(bl.cnt_nom),
                    'company', bl.cnt_societe,
                    'postal_code', bl.cnt_code_postal,
                    'city', TRIM(bl.cnt_ville),
                    'address', CAST(
                        IF(
                            POSITION('\n' IN bl.cnt_adresse) > 0,
                            CONCAT('["', REPLACE(REPLACE(bl.cnt_adresse,'"','\\"'), '\n', '","'), '"]'),
                            CONCAT('["', TRIM(REPLACE(bl.cnt_adresse,'"','\\"')), '","","",""]')
                        ) AS JSON
                    ),
                    'email', bl.cnt_email,
                    'phone', FORMAT_phone_number(bl.cnt_telephone),
                    'cellphone', FORMAT_phone_number(bl.cnt_mobile),
                    'country', p.pays,
                    'country_id', p.id_pays
                ) AS customer
            FROM backOffice.BO_EXP_expeditions_bl ebl
                INNER JOIN backOffice.bon_livraison bl ON bl.id_bon_livraison = ebl.bon_livraison_id
                INNER JOIN backOffice.produit_bon_livraison pbl ON pbl.id_bon_livraison = bl.id_bon_livraison
                INNER JOIN backOffice.article a ON pbl.id_produit = a.id_produit
                INNER JOIN backOffice.BO_EXP_expeditions e ON e.id = ebl.expedition_id
                INNER JOIN backOffice.transporteur tpt ON tpt.id_transporteur = e.transporteur_id
                INNER JOIN backOffice.BO_TPT_PDT_liste tp ON tp.id = bl.id_pdt_transporteur
                INNER JOIN backOffice.pays p ON p.id_pays = bl.cnt_id_pays
            WHERE {conditions}
            GROUP BY bl.id_bon_livraison
        ) bl
        LEFT JOIN backOffice.BO_EXP_expeditions_bl_colis eblc ON eblc.expedition_bl_id = bl.shipment_delivery_note_id
        LEFT JOIN backOffice.colis c ON c.id_bon_livraison = bl.delivery_note_id
        GROUP BY bl.delivery_note_id
        SQL;

        return strtr($base_sql, ['{conditions}' => $condition]);
    }
}
