<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\BusinessRule\CustomerOrder;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\Referential\Rpc\BridgeRpcMethodReferential;
use SonVideo\Erp\Repository\CustomerOrder\CarrierEligibilityContextRepository;
use SonVideo\Erp\Repository\CustomerOrder\InconsistentCarrierRepository;
use SonVideo\Synapps\Client\RpcClientService;

/**
 * Class CarrierBusinessRule.
 */
class CarrierBusinessRule implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    protected CarrierEligibilityContextRepository $repository;

    protected RpcClientService $client_service;
    protected InconsistentCarrierRepository $inconsistent_carrier_repository;
    private CustomerOrderRepository $customer_order_repository;

    /** @var bool */
    protected $debug_mode = false;

    /** CarrierBusinessRule constructor. */
    public function __construct(
        CarrierEligibilityContextRepository $repository,
        RpcClientService $client_service,
        InconsistentCarrierRepository $inconsistent_carrier_repository,
        CustomerOrderRepository $customer_order_repository
    ) {
        $this->repository = $repository;
        $this->client_service = $client_service;
        $this->inconsistent_carrier_repository = $inconsistent_carrier_repository;
        $this->customer_order_repository = $customer_order_repository;
    }

    /** setDebug */
    public function setDebug(bool $flag): CarrierBusinessRule
    {
        $this->debug_mode = $flag;

        return $this;
    }

    /** isInconsistentForCustomerOrder */
    public function isInconsistentForCustomerOrder(int $customer_order_id): bool
    {
        // Bail out if the customer order does not need any delivery note
        $should_continue = $this->customer_order_repository->hasRemainingProductToDeliverOnCustomerOrder(
            $customer_order_id
        );

        if (false === $should_continue) {
            return false;
        }

        // Build an order context for bridge
        try {
            $context = $this->repository->fetchById($customer_order_id);
            $current_shipment_method_id = $context['shipment_method']['shipment_method_id'];
            $current_customer_order_creation_origin = $context['customer']['creation_origin'];
        } catch (\Exception $exception) {
            // Cannot check eligibility - don't block other processes
            $this->logger->warning(
                sprintf('Failed to build an order context for customer order "%d"', $customer_order_id)
            );

            return false;
        }

        if ($this->debug_mode) {
            $this->logger->notice(sprintf('[DEBUG] - Current shipment method id : "%s"', $current_shipment_method_id));
        }

        // We do not test chronopost shipment service
        if (
            in_array(
                $current_shipment_method_id,
                CarrierEligibilityContextRepository::CHRONOPOST_SHIPMENT_SERVICE_SHIPMENT_ID
            )
        ) {
            return false;
        }

        if (
            in_array(
                $current_customer_order_creation_origin,
                CarrierEligibilityContextRepository::CUSTOMER_ORDER_CREATION_ORIGIN_EXCLUDED
            )
        ) {
            return false;
        }

        // No article remaining to deliver
        if (!is_countable($context['items']) || 0 === count($context['items'])) {
            if ($this->debug_mode) {
                $this->logger->notice('[DEBUG] - No article remaining to deliver, bailing out');
            }

            return false;
        }

        // Don't flag order using a store pickup
        $store_pickup_shipment_methods = array_column(
            $this->repository->fetchAllStorePickupShipmentMethodIds(),
            'shipment_method_id'
        );

        if ($this->debug_mode) {
            $this->logger->notice(
                sprintf(
                    '[DEBUG] - Store pickup shipment methods to ignore : "%s"',
                    implode(', ', $store_pickup_shipment_methods)
                )
            );
        }

        if (in_array($current_shipment_method_id, $store_pickup_shipment_methods)) {
            return false;
        }

        // RPC call to bridge to fetch list of eligible carriers
        $eligible_carriers = $this->client_service->call(
            BridgeRpcMethodReferential::SERVER_NAME,
            BridgeRpcMethodReferential::CARRIER_ELIGIBILTY_ENDPOINT,
            [$context]
        );

        $eligible_method_ids = array_column($eligible_carriers['result'], 'shipment_method_id');

        if ($this->debug_mode) {
            $this->logger->notice(
                sprintf('[DEBUG] - Eligible method IDs returned by bridge : "%s"', implode(', ', $eligible_method_ids))
            );
        }

        // Customer orders requiring a quotation on the shipping price will most likely
        // use a non-eligible shipment method selected manually in the BO, don't flag the order
        if (in_array(CarrierEligibilityContextRepository::QUOTATION_SHIPMENT_ID, $eligible_method_ids)) {
            return false;
        }

        // return true if the current shipment method id is not in the list sent back by bridge
        return !in_array($current_shipment_method_id, $eligible_method_ids);
    }

    /** logInconsistentCarrierIfNeeded */
    public function logInconsistentCarrierIfNeeded(
        int $customer_order_id,
        bool $is_carrier_inconsistent
    ): CarrierBusinessRule {
        $exists = $this->inconsistent_carrier_repository->exists($customer_order_id);

        if ($exists && false === $is_carrier_inconsistent) {
            $this->inconsistent_carrier_repository->delete($customer_order_id);
        }

        if (false === $exists && $is_carrier_inconsistent) {
            $this->inconsistent_carrier_repository->create($customer_order_id);
        }

        return $this;
    }

    /** checkForCarrierInconsistencyOnCustomerOrders */
    public function checkForCarrierInconsistencyOnCustomerOrders(): CarrierBusinessRule
    {
        $data = $this->repository->fetchAllOrdersThatNeedDelivery();

        $this->logger->info(sprintf('"%d" customer orders to verify', count($data)));

        foreach ($data as $idx => $entry) {
            $is_inconsistent = $this->isInconsistentForCustomerOrder($entry->customer_order_id);
            $this->logger->info(
                sprintf(
                    ' - (%d) Customer order "%s" is "%s" after checking its eligibility',
                    $idx + 1,
                    $entry->customer_order_id,
                    $is_inconsistent ? 'INCONSISTENT' : 'NOT INCONSISTENT'
                )
            );

            $this->logInconsistentCarrierIfNeeded($entry->customer_order_id, $is_inconsistent);
        }

        return $this;
    }
}
