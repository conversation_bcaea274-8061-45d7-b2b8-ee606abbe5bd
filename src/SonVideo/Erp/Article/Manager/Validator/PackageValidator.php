<?php

namespace SonVideo\Erp\Article\Manager\Validator;

use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use SonVideo\Erp\Article\Dto\CreationContext\PackagedArticleContextDto as PackagedArticleCreationContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\PackagedArticleContextDto as PackagedArticleUpdateContextDto;
use SonVideo\Erp\Article\Entity\PackagedArticleEntity;
use SonVideo\Erp\Article\Mysql\Repository\PackagedArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PackageValidator
{
    private SingleArticleReadRepository $single_article_read_repository;

    private PackagedArticleRepository $packaged_article_repository;

    private ValidatorInterface $validator;

    /** ShipmentProcessor constructor. */
    public function __construct(
        SingleArticleReadRepository $single_article_read_repository,
        PackagedArticleRepository $packaged_article_repository,
        ValidatorInterface $validator
    ) {
        $this->single_article_read_repository = $single_article_read_repository;
        $this->packaged_article_repository = $packaged_article_repository;
        $this->validator = $validator;
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws \Exception
     */
    public function checkIfCanAddArticle(PackagedArticleCreationContextDto $dto): void
    {
        $package_id = $dto->package_id;
        $is_package = $this->single_article_read_repository->isPackage($package_id);
        if (!$is_package) {
            throw new \Exception("Article with id $package_id is not a package.");
        }

        if ($package_id === $dto->article_id) {
            throw new \Exception('An article cannot be part of its own package.');
        }

        $is_destock = $this->single_article_read_repository->isDestock($dto->article_id);
        if ($is_destock) {
            throw new \Exception('Cannot add a destock to a package.');
        }

        $is_package = $this->single_article_read_repository->isPackage($dto->article_id);
        $new_packaged_articles = $is_package
            ? $this->packaged_article_repository->findAllByPackageId($dto->article_id)
            : [$dto];

        $current_packaged_articles = $this->packaged_article_repository->findAllByPackageId($package_id);
        if (count($current_packaged_articles) + count($new_packaged_articles) > 8) {
            $validation_error = [
                'validation_errors' => [
                    'packaged_article' => sprintf(
                        '[key:too_many_packaged_articles] value "%d" : A package cannot have more than 8 articles',
                        $dto->article_id
                    ),
                ],
            ];

            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), $validation_error);
        }

        foreach ($new_packaged_articles as $packaged_article) {
            $exists = array_filter(
                $current_packaged_articles,
                fn ($a): bool => $a->article_id === $packaged_article->article_id
            );
            if ([] !== $exists) {
                $validation_error = [
                    'validation_errors' => [
                        'packaged_article' => sprintf(
                            '[key:packaged_article_already_in_package] value "%d" : Article(s) with id(s) %s already exists in package',
                            $dto->article_id,
                            implode(', ', array_map(fn ($a): int => $a->article_id, $exists))
                        ),
                    ],
                ];

                throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), $validation_error);
            }
        }
    }

    /** @throws NotFoundException */
    public function checkIfCanEditPackagedArticle(PackagedArticleUpdateContextDto $dto): void
    {
        $packaged_article = $this->packaged_article_repository->findById($dto->packaged_article_id);
        if (!$packaged_article instanceof PackagedArticleEntity) {
            throw new NotFoundException('Packaged article not found.');
        }

        if ($packaged_article->package_id !== $dto->package_id) {
            throw new NotFoundException('Package not found or packaged article not found in package.');
        }
    }

    /** @throws InternalErrorException */
    public function validateDto($dto): void
    {
        $errors = $this->validator->validate($dto);
        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }
    }
}
