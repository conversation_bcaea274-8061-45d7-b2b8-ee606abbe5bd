<?php

namespace SonVideo\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticlePlannedPriceUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\TriggeredPriceUpdateContextDto;
use SonVideo\Erp\Article\Entity\ArticlePlannedPriceEntity;
use SonVideo\Erp\Article\Mysql\Repository\ArticlePlannedPriceRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyProductRepository;
use SonVideo\Erp\Referential\SalesChannel;
use SonVideo\Erp\Task\Entity\TaskEntity;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class ArticlePlannedPriceApplier implements LegacyPdoAwareInterface, LoggerAwareInterface
{
    use LegacyPdoAwareTrait;
    use LoggerAwareTrait;

    private ArticlePlannedPriceRepository $article_planned_price_repository;

    private SerializerInterface $serializer;

    private QueryBuilder $query_builder;

    private TaskRepository $task_repository;

    private AccountQueryRepository $account_repository;
    private ArticleSalesChannelManager $article_sales_channel_manager;
    private PricingStrategyProductRepository $pricing_strategy_product_repository;
    private ArticleRepository $article_repository;

    public function __construct(
        ArticlePlannedPriceRepository $article_planned_price_repository,
        SerializerInterface $serializer,
        QueryBuilder $query_builder,
        TaskRepository $task_repository,
        AccountQueryRepository $account_repository,
        ArticleSalesChannelManager $article_sales_channel_manager,
        PricingStrategyProductRepository $pricing_strategy_product_repository,
        ArticleRepository $article_repository
    ) {
        $this->article_planned_price_repository = $article_planned_price_repository;
        $this->serializer = $serializer;
        $this->query_builder = $query_builder;
        $this->task_repository = $task_repository;
        $this->account_repository = $account_repository;
        $this->article_sales_channel_manager = $article_sales_channel_manager;
        $this->pricing_strategy_product_repository = $pricing_strategy_product_repository;
        $this->article_repository = $article_repository;
    }

    public function updateArticlePrices(): int
    {
        $now = date('Y-m-d H:i:s');

        $this->query_builder->setWhere(['ends_at' => ['_lt' => $now]]);

        /** @var ArticlePlannedPriceEntity[] $article_planned_prices_to_deactivate */
        $article_planned_prices_to_deactivate = $this->serializer->denormalize(
            $this->article_planned_price_repository->findAllPaginated($this->query_builder)->getResults(),
            ArticlePlannedPriceEntity::class . '[]'
        );

        $this->processPrices($article_planned_prices_to_deactivate, false);

        $this->query_builder->setWhere([
            'starts_at' => ['_lte' => $now],
            'ends_at' => ['_gt' => $now],
            'applied_at' => ['_null' => true],
        ]);

        /** @var ArticlePlannedPriceEntity[] $article_planned_prices_to_active */
        $article_planned_prices_to_active = $this->serializer->denormalize(
            $this->article_planned_price_repository->findAllPaginated($this->query_builder)->getResults(),
            ArticlePlannedPriceEntity::class . '[]'
        );

        $this->processPrices($article_planned_prices_to_active);

        return count($article_planned_prices_to_active) + count($article_planned_prices_to_deactivate);
    }

    /** @param ArticlePlannedPriceEntity[] $article_planned_prices */
    private function processPrices(array $article_planned_prices, bool $to_activate = true): void
    {
        foreach ($article_planned_prices as $article_planned_price) {
            $user = $this->account_repository->findOneById($article_planned_price->created_by['id']);

            foreach ($article_planned_price->sales_channel_ids as $sales_channel_id) {
                try {
                    $price = $to_activate
                        ? $article_planned_price->selling_price
                        : $article_planned_price->exit_selling_price;

                    $is_on_sale = (bool) $this->article_repository->getOneById($article_planned_price->article_id, [
                        'is_on_sale',
                    ])['is_on_sale'];

                    if ($is_on_sale && SalesChannel::SON_VIDEO === $sales_channel_id) {
                        continue;
                    }
                    $is_article_sales_channel_under_strategy = $this->pricing_strategy_product_repository->isActivatedForSalesChannel(
                        $article_planned_price->article_id,
                        $sales_channel_id
                    );

                    if ($is_article_sales_channel_under_strategy) {
                        continue;
                    }

                    $price_update_context = new TriggeredPriceUpdateContextDto();
                    $price_update_context->planned_price = $article_planned_price;

                    $this->article_sales_channel_manager->updateSellingPriceWithTaxes(
                        $article_planned_price->article_id,
                        $sales_channel_id,
                        $price,
                        $price_update_context,
                        $user
                    );
                } catch (\Exception|ExceptionInterface $exception) {
                    $this->logger->error($exception->getMessage(), ['exception' => $exception]);
                    $description = sprintf(
                        "La mise à jour du prix de vente de l'article %d au prix de %s€ a échoué",
                        $article_planned_price->article_id,
                        $price
                    );

                    $this->task_repository->createProductTask(
                        TaskEntity::TYPE_ERROR_UPDATE_PRICE,
                        $article_planned_price->article_id,
                        $description,
                        $article_planned_price->created_by['id']
                    );
                }
            }

            /** @var ArticlePlannedPriceUpdateContextDto $dto */
            $dto = $this->serializer->denormalize(
                array_merge($this->serializer->normalize($article_planned_price), [
                    'applied_at' => date('Y-m-d H:i:s'),
                ]),
                ArticlePlannedPriceUpdateContextDto::class
            );

            $this->article_planned_price_repository->update($dto);

            if (!$to_activate) {
                $this->article_planned_price_repository->delete($article_planned_price->article_planned_price_id);
            }
        }
    }
}
