<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Article\Dto\CreationContext\ArticleWeightedCostAdjustmentCreationContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleWeightedCostAdjustmentUpdateContextDto;

class ArticleWeightedCostAdjustmentRepository extends AbstractLegacyRepository
{
    public function create(ArticleWeightedCostAdjustmentCreationContextDto $weighted_cost_adjustment): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.BO_STK_weighted_cost_adjustment (article_id, type, amount, meta, created_by)
        VALUES
            (:article_id, :type, :amount, :meta, :created_by)
        ;
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $weighted_cost_adjustment->article_id,
            'type' => $weighted_cost_adjustment->type,
            'amount' => $weighted_cost_adjustment->amount,
            'meta' => json_encode($weighted_cost_adjustment->meta, JSON_THROW_ON_ERROR | JSON_FORCE_OBJECT),
            'created_by' => $weighted_cost_adjustment->created_by,
        ]);

        return $this->legacy_pdo->lastInsertId();
    }

    /** @return false|array */
    public function findOneById(int $id)
    {
        $sql = <<<SQL
        SELECT id, type, amount, created_at, updated_at, created_by, meta
        FROM backOffice.BO_STK_weighted_cost_adjustment
        WHERE id = :id;
        SQL;

        return $this->legacy_pdo->fetchOne($sql, ['id' => $id]);
    }

    public function update(ArticleWeightedCostAdjustmentUpdateContextDto $weighted_cost_adjustment): int
    {
        $sql = <<<SQL
        UPDATE backOffice.BO_STK_weighted_cost_adjustment
        SET meta = :meta
        WHERE id = :id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'meta' => json_encode($weighted_cost_adjustment->meta, JSON_THROW_ON_ERROR | JSON_FORCE_OBJECT),
            'id' => $weighted_cost_adjustment->id,
        ]);
    }

    /** @throws SqlErrorMessageException */
    public function exists(int $article_id, int $weighted_cost_adjustment_id): bool
    {
        $sql = <<<SQL
        SELECT EXISTS(
            SELECT 1 FROM backOffice.BO_STK_weighted_cost_adjustment
            WHERE article_id = :article_id
            AND id = :weighted_cost_adjustment_id
        )
        SQL;

        return (bool) $this->legacy_pdo->fetchValue($sql, [
            'article_id' => $article_id,
            'weighted_cost_adjustment_id' => $weighted_cost_adjustment_id,
        ]);
    }

    public function fetchDestockToAdjust(): array
    {
        $sql = <<<SQL
        SELECT
          a.id_produit,
          p.reference AS sku,
          max(wca.created_at) AS last_adjusted_at,
          a.prix_vente AS selling_price,
          a.prix_achat_pondere AS weighted_cost
          FROM
            backOffice.article a
              INNER JOIN backOffice.article_destock ad ON a.id_produit = ad.article_id
              INNER JOIN backOffice.BO_STK_produit_depot pd ON a.id_produit = pd.id_produit
              INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
              LEFT JOIN backOffice.BO_STK_weighted_cost_adjustment wca ON a.id_produit = wca.article_id
          WHERE
            pd.id_depot = 21
            AND pd.quantite_stock > 0
            AND a.date_creation < now() - INTERVAL 3 MONTH
          GROUP BY a.id_produit
          HAVING
            last_adjusted_at IS NULL
            OR last_adjusted_at < now() - INTERVAL 3 MONTH
        SQL;

        return $this->legacy_pdo->fetchAssoc($sql) ?: [];
    }
}
