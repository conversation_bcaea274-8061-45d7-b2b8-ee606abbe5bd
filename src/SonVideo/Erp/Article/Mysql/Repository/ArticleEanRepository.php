<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

final class ArticleEanRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'id' => 'ae.id',
        'ean' => 'ae.ean',
        'article_id' => 'ae.BO_CTG_PDT_ART_article_id',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                  ae.id as id,
                  ae.ean as ean,
                  ae.BO_CTG_PDT_ART_article_id as article_id
              FROM backOffice.BO_CTG_PDT_ART_ean ae
              WHERE {where}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function add(int $article_id, string $ean): int
    {
        $sql = <<<'SQL'
            INSERT backOffice.BO_CTG_PDT_ART_ean (BO_CTG_PDT_ART_article_id, ean)
            VALUES (:article_id, :ean)
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, ['article_id' => $article_id, 'ean' => $ean]);
    }

    public function delete(int $article_id, string $ean): int
    {
        $sql = <<<'SQL'
            DELETE FROM backOffice.BO_CTG_PDT_ART_ean
            WHERE BO_CTG_PDT_ART_article_id = :article_id
                  AND ean = :ean
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, ['article_id' => $article_id, 'ean' => $ean]);
    }
}
