<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use SonVideo\Erp\Article\Entity\ArticleStockEntity;
use SonVideo\Erp\Referential\Product;

class ArticleStockRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    /** @return array<ArticleStockEntity> */
    public function findAll(string $id_or_sku): array
    {
        $condition = ctype_digit($id_or_sku) ? 'p.id_produit' : 'p.reference';

        // JSON_ARRAYAGG does not support DISTINCT, therefore we use GROUP_CONCAT
        $sql = <<<SQL
        SELECT
          tmp.warehouse_id,
          tmp.warehouse_name,
          tmp.shipment_method_id,
          tmp.quantity,
          IF(so.supplier_order_id IS NOT NULL,CAST(CONCAT('[', GROUP_CONCAT(DISTINCT
            JSON_OBJECT(
              'warehouse_id', so.warehouse_id,
              'supplier_id', so.supplier_id,
              'supplier_order_id', so.supplier_order_id,
              'created_at', so.created_at,
              'order_type', so.order_type,
              'order_status', so.order_status,
              'quantity_ordered', so.quantity_ordered,
              'quantity_delivered', so.quantity_delivered,
              'expected_delivery_date', so.expected_delivery_date,
              'deliveries', so.deliveries
              )
            ), ']') AS JSON), JSON_ARRAY()) AS supplier_orders,
          IF(outgoing_trf.id_transfer IS NOT NULL, CAST(CONCAT('[', GROUP_CONCAT(DISTINCT
            JSON_OBJECT(
              'transfer_id', outgoing_trf.id_transfer,
              'from_id', outgoing_trf.from_id,
              'to_id', outgoing_trf.to_id,
              'customer_order_id', outgoing_trf.customer_order_id,
              'created_at', outgoing_trf.created_at,
              'quantity', outgoing_trf.quantity,
              'delivery_note_id', outgoing_trf.delivery_note_id
              )
            ), ']') AS JSON), JSON_ARRAY()) AS outgoing_transfers,
          IF(incoming_trf.id_transfer IS NOT NULL, CAST(CONCAT('[', GROUP_CONCAT(DISTINCT
            JSON_OBJECT(
              'transfer_id', incoming_trf.id_transfer,
              'from_id', incoming_trf.from_id,
              'to_id', incoming_trf.to_id,
              'customer_order_id', incoming_trf.customer_order_id,
              'created_at', incoming_trf.created_at,
              'quantity', incoming_trf.quantity,
              'delivery_note_id', incoming_trf.delivery_note_id
              )
            ), ']') AS JSON), JSON_ARRAY()) AS incoming_transfers,
          IF(related_customer_orders.customer_order_id IS NOT NULL,CAST(CONCAT('[', GROUP_CONCAT(DISTINCT
                related_customer_orders.customer_order_id
            ), ']') AS JSON), JSON_ARRAY()) AS related_customer_order_ids
          FROM
            -- Main query
            (
              SELECT
                spd.id_depot AS warehouse_id,
                bsd.nom_depot AS warehouse_name,
                bsd.id_transporteur_emport AS shipment_method_id,
                bsd.expedition_client_possible AS can_ship_to_customer,
                bsd.ordre AS display_order,
                JSON_OBJECT(
                  'in_stock', spd.quantite_stock,
                  'safety_stock_threshold', spd.stock_securite,
                  'awaiting_preparation', backOffice.PDT_ART_qte_en_attente_depot(spd.id_produit, bsd.id),
                  'is_prepared', backOffice.PDT_ART_qte_au_depart_depot(spd.id_produit, bsd.id)
                  ) AS quantity
                FROM
                  backOffice.BO_STK_produit_depot spd
                    INNER JOIN backOffice.produit p ON spd.id_produit = p.id_produit
                    INNER JOIN backOffice.BO_STK_depot bsd ON spd.id_depot = bsd.id
                WHERE
                    {condition} = :id_or_sku
                  AND bsd.is_active_bo = 1
                GROUP BY bsd.id
              ) tmp
              -- Aggregate: ongoing supplier orders
              LEFT JOIN (
              SELECT
                spd.id_depot AS warehouse_id,
                cf.id_fournisseur AS supplier_id,
                cf.id_commande_fournisseur AS supplier_order_id,
                cf.date_creation AS created_at,
                cf.type AS order_type,
                cf.status AS order_status,
                pcf.quantite_commandee AS quantity_ordered,
                pcf.quantite_livree AS quantity_delivered,
                pcf.date_livraison_prevue AS expected_delivery_date,
                IF(lpcf.id IS NOT NULL, JSON_ARRAYAGG(JSON_OBJECT(
                  'expected_delivery_id', lpcf.id,
                  'supplier_order_product_id', lpcf.id_produit_commande_fournisseur,
                  'expected_delivery_date', lpcf.date_livraison_prevue,
                  'expected_quantity', lpcf.quantite_livraison_prevue
                )), JSON_ARRAY()) AS deliveries
                FROM
                  backOffice.BO_STK_produit_depot spd
                    INNER JOIN backOffice.produit p ON spd.id_produit = p.id_produit
                    INNER JOIN backOffice.commande_fournisseur cf ON spd.id_depot = cf.id_depot
                    LEFT JOIN backOffice.produit_commande_fournisseur pcf
                    ON cf.id_commande_fournisseur = pcf.id_commande_fournisseur
                      AND spd.id_produit = pcf.id_produit
                    LEFT JOIN backOffice.livraison_produit_commande_fournisseur lpcf ON pcf.id = lpcf.id_produit_commande_fournisseur
                WHERE
                    {condition} = :id_or_sku
                  AND cf.status IN ('en preparation', 'en cours')
                  AND pcf.id_produit IS NOT NULL
                  AND pcf.quantite_commandee - pcf.quantite_livree > 0
                GROUP BY cf.id_commande_fournisseur
              ) AS so ON so.warehouse_id = tmp.warehouse_id
              -- Aggregate: outgoing transfers
              LEFT JOIN (
              SELECT
                bst_from.id AS id_transfer,
                bst_from.id_depot_depart AS from_id,
                bst_from.id_depot_arrivee AS to_id,
                bst_from.id_commande AS customer_order_id,
                bst_from.date_creation AS created_at,
                bspt_from.quantite AS quantity,
                bspt_from.id_bon_livraison AS delivery_note_id
                FROM
                  backOffice.BO_STK_produit_depot spd
                    INNER JOIN backOffice.produit p ON spd.id_produit = p.id_produit
                    INNER JOIN backOffice.BO_STK_depot bsd ON spd.id_depot = bsd.id
                    LEFT JOIN backOffice.BO_STK_produit_transfert bspt_from ON bspt_from.id_produit = spd.id_produit
                    LEFT JOIN backOffice.BO_STK_transfert bst_from
                    ON bspt_from.id_transfert = bst_from.id AND bsd.id = bst_from.id_depot_depart AND
                       bst_from.statut IN ('au depart')
                WHERE
                    {condition} = :id_or_sku
                  AND bst_from.id IS NOT NULL
                  AND bst_from.id_pdt_transporteur IS NOT NULL
              ) AS outgoing_trf ON tmp.warehouse_id = outgoing_trf.from_id
              -- Aggregate: incoming transfers
              LEFT JOIN (
              SELECT
                bst_from.id AS id_transfer,
                bst_from.id_depot_depart AS from_id,
                bst_from.id_depot_arrivee AS to_id,
                bst_from.id_commande AS customer_order_id,
                bst_from.date_creation AS created_at,
                bspt_from.quantite - bspt_from.quantite_livree AS quantity,
                bspt_from.id_bon_livraison AS delivery_note_id
                FROM
                  backOffice.BO_STK_produit_depot spd
                    INNER JOIN backOffice.produit p ON spd.id_produit = p.id_produit
                    INNER JOIN backOffice.BO_STK_depot bsd ON spd.id_depot = bsd.id
                    LEFT JOIN backOffice.BO_STK_produit_transfert bspt_from ON bspt_from.id_produit = spd.id_produit
                    LEFT JOIN backOffice.BO_STK_transfert bst_from
                    ON bspt_from.id_transfert = bst_from.id AND bsd.id = bst_from.id_depot_depart AND
                       bst_from.statut IN ('expedie')
                WHERE
                    {condition} = :id_or_sku
                  AND bst_from.id IS NOT NULL
                  AND bst_from.id_pdt_transporteur IS NOT NULL
                  AND bspt_from.quantite - bspt_from.quantite_livree > 0
              ) AS incoming_trf ON tmp.warehouse_id = incoming_trf.to_id
              -- Aggregate: related orders
              LEFT JOIN (
              SELECT c.id_commande AS customer_order_id,
                    IF(c.depot_emport IS NULL,
                        (SELECT JSON_ARRAYAGG(id) FROM backOffice.BO_STK_depot WHERE expedition_client_possible = 1),
                        JSON_ARRAY(c.depot_emport)
                    ) AS related_warehouses
              FROM backOffice.commande c
                INNER JOIN backOffice.produit_commande pc ON pc.id_commande = c.id_commande
                INNER JOIN backOffice.produit p ON pc.id_produit = p.id_produit
                LEFT JOIN backOffice.bon_livraison bl ON pc.id_bon_livraison = bl.id_bon_livraison
              WHERE
                 {condition} = :id_or_sku
                  AND (bl.id_bon_livraison IS NULL OR bl.status != 'expedie')
                  AND (
                      (pc.reservation != 'Y' OR pc.reservation IS NULL)
                        AND c.en_attente_de_livraison = 1
                      OR pc.reservation = 'Y'
                        AND c.flux = 'traitement'
                      )
              GROUP BY c.id_commande
              ) AS related_customer_orders ON JSON_CONTAINS(related_customer_orders.related_warehouses, CAST(tmp.warehouse_id AS JSON), '$')
          GROUP BY tmp.warehouse_id
          ORDER BY tmp.display_order ASC,
            tmp.warehouse_name ASC
        SQL;

        $this->legacy_pdo->fetchAffected('SET SESSION group_concat_max_len = 1000000');

        return $this->legacy_pdo->fetchAllEntities(
            strtr($sql, ['{condition}' => $condition]),
            ['id_or_sku' => $id_or_sku],
            ArticleStockEntity::class
        );
    }

    public function getOldSafetyStockThreshold(int $article_id, int $warehouse_id): int
    {
        $sql = <<<SQL
            SELECT stock_securite
            FROM backOffice.BO_STK_produit_depot
            WHERE id_produit = :article_id
                AND id_depot = :warehouse_id
        SQL;

        $result = $this->legacy_pdo->fetchValue($sql, ['article_id' => $article_id, 'warehouse_id' => $warehouse_id]);
        if (null === $result) {
            throw new NotFoundException(sprintf('No security stock found with %d.', $article_id));
        }

        return (int) $result;
    }

    public function updateSafetyStockThreshold(int $safety_stock_threshold, int $article_id, int $warehouse_id): int
    {
        $sql = <<<SQL
            UPDATE backOffice.BO_STK_produit_depot
            SET stock_securite = :safety_stock_threshold
            WHERE id_produit = :article_id
                AND id_depot = :warehouse_id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'safety_stock_threshold' => $safety_stock_threshold,
            'article_id' => $article_id,
            'warehouse_id' => $warehouse_id,
        ]);
    }

    public function getPossiblyStockedYapus(int $customer_order_id): array
    {
        $sql = <<<SQL
            SELECT
                p.reference,
                COALESCE(pd.quantite_stock, 0) stock,
                d2.nom_depot
            FROM
                backOffice.commande c
                    INNER JOIN backOffice.produit_commande pc ON pc.id_commande = c.id_commande
                        AND pc.id_produit NOT IN (:shipment_product_id, :catalog_product_id, :magazine_product_id)
                    INNER JOIN backOffice.article a ON pc.id_produit = a.id_produit
                    INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
                    LEFT JOIN backOffice.BO_STK_depot d ON c.depot_emport = d.id
                    LEFT JOIN backOffice.BO_STK_produit_depot pd ON a.id_produit = pd.id_produit AND pd.quantite_stock > 0
                    LEFT JOIN backOffice.BO_STK_depot d2 ON pd.id_depot = d2.id
            WHERE
                a.status = 'yapu'
              AND pc.id_bon_livraison IS NULL
              AND pc.quantite > 0
              AND IF(
                      d.id IS NOT NULL,
                      (backOffice.PDT_ART_qte_dispo_resa(a.id_produit, c.id_commande, d.id) + backOffice.PDT_ART_qte_dispo_resa(a.id_produit, c.id_commande, 21) + backOffice.PDT_ART_qte_dispo_resa(a.id_produit, c.id_commande, 3)),
                      (backOffice.PDT_ART_qte_dispo_resa(a.id_produit, c.id_commande, 21) + backOffice.PDT_ART_qte_dispo_resa(a.id_produit, c.id_commande, 3))
                  ) < pc.quantite
              AND c.id_commande = :customer_order_id
            HAVING stock > 0;
        SQL;

        return $this->legacy_pdo->fetchAll($sql, [
            'customer_order_id' => $customer_order_id,
            'shipment_product_id' => Product::SHIPMENT_PRODUCT_ID,
            'catalog_product_id' => Product::CATALOG_PRODUCT_ID,
            'magazine_product_id' => Product::MAGAZINE_PRODUCT_ID,
        ]);
    }
}
