<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Article\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

final class ArticleStockEntity extends AbstractEntity
{
    public int $warehouse_id;

    public string $warehouse_name;

    public ?int $shipment_method_id = null;

    /** @var array|JsonType */
    public $quantity;

    /** @var array|JsonType */
    public array $supplier_orders;

    /** @var array|JsonType */
    public array $outgoing_transfers;

    /** @var array|JsonType */
    public array $incoming_transfers;

    /** @var array|JsonType */
    public array $related_customer_order_ids;
}
