<?php

namespace SonVideo\Erp\Article\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

final class ArticlePlannedPriceEntity extends AbstractEntity
{
    public int $article_planned_price_id;

    public int $article_id;

    public float $selling_price;
    public float $exit_selling_price;

    public \DateTimeInterface $starts_at;
    public \DateTimeInterface $ends_at;
    public ?\DateTimeInterface $applied_at = null;

    /** @var array|JsonType */
    public array $created_by;

    /** @var array|JsonType */
    public array $sales_channel_ids = [];
}
