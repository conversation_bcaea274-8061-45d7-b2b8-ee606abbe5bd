<?php

namespace SonVideo\Erp\Article\Dto;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class SalesChannelComputedPriceDto
{
    /** @Assert\PositiveOrZero() */
    public int $sales_channel_id;

    /** @Assert\Positive() */
    public float $margin = 0.1;

    /** @Assert\PositiveOrZero() */
    public float $margin_rate = 0.0;

    /** @Assert\PositiveOrZero() */
    public float $selling_price_tax_excluded = 0.0;

    /** @Assert\PositiveOrZero() */
    public float $selling_price_tax_included = 0.0;

    /** @Assert\PositiveOrZero() */
    public float $sales_channel_commission = 0.0;

    /** @Assert\PositiveOrZero() */
    public float $markup_rate = 0.0;

    /**
     * @OA\Property(type="array",
     *      @OA\Items(type="object",
     *          @OA\Property(property="code", type="string"),
     *          @OA\Property(property="message", type="string")
     *      )
     * ))
     *
     * @var ?array<array{code: string, message: string}>
     */
    public ?array $errors = null;
}
