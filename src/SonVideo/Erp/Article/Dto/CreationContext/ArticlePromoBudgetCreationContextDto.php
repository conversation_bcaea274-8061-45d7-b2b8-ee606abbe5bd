<?php

namespace SonVideo\Erp\Article\Dto\CreationContext;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticlePromoBudgetCreationContextDto
{
    public ?int $id = null;

    /** @OA\Property(description="Id article auquel appliquer la promotion") */
    public int $article_id;

    /**
     * @OA\Property(description="Montant de la promotion")
     * @Assert\Positive(message="[key:amount_must_be_positive] amount must be positive : {{ value }}")
     * @Assert\NotNull(message="[key:amount_must_be_positive] amount must be positive : {{ value }}")
     */
    public ?float $amount = null;

    /**
     * @OA\Property(description="Date à partir de laquelle commence la promotion")
     *
     * @Assert\DateTime()
     * @Assert\LessThan(propertyPath="end_at", message="[key:starts_at_must_be_before_ends_at] start date must be before end date")
     */
    public \DateTimeInterface $start_at;

    /**
     * @OA\Property(description="Date à jusqu'à laquelle dure la pormotion")
     *
     * @Assert\DateTime()
     * @Assert\GreaterThan(propertyPath="start_at", message="[key:starts_at_must_be_before_ends_at] start date must be before end date")
     */
    public \DateTimeInterface $end_at;
}
