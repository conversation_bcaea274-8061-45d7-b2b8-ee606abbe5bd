<?php

namespace SonVideo\Erp\Supplier\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Supplier\Entity\SupplierPaymentInformationEntity;
use SonVideo\Erp\Supplier\Mysql\Repository\SupplierRepository;
use SonVideo\Erp\SupplierPayment\Mysql\Repository\SupplierPaymentRepository;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SupplierManager implements LegacyPdoAwareInterface, SerializerAwareInterface
{
    use SerializerAwareTrait;
    use LegacyPdoAwareTrait;

    private SupplierRepository $supplier_repository;
    private SupplierPaymentRepository $supplier_payment_repository;
    private ValidatorInterface $validator;
    private QueryBuilder $query_builder;

    public function __construct(
        SupplierRepository $supplier_repository,
        SupplierPaymentRepository $supplier_payment_repository,
        ValidatorInterface $validator,
        QueryBuilder $query_builder
    ) {
        $this->supplier_repository = $supplier_repository;
        $this->supplier_payment_repository = $supplier_payment_repository;
        $this->validator = $validator;
        $this->query_builder = $query_builder;
    }

    /**
     * @throws InternalErrorException
     * @throws NotFoundException
     * @throws ExceptionInterface
     */
    public function update(SupplierPaymentInformationEntity $supplier): int
    {
        $this->checkIfSupplierExist($supplier->supplier_id);

        $dto = $this->serializer->denormalize(
            [
                'supplier_id' => $supplier->supplier_id,
                'supplier_payment_id' => $supplier->supplier_payment_id,
                'discount_rate' => $supplier->discount_rate,
                'franco' => $supplier->franco,
                'delivery_cost' => $supplier->delivery_cost,
                'discount_payment_deadline' => $supplier->discount_payment_deadline,
                'payment_deadline_id' => $supplier->payment_deadline_id,
                'comment' => $supplier->comment,
            ],
            SupplierPaymentInformationEntity::class
        );
        $errors = $this->validator->validate($dto);

        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }

        return $this->supplier_repository->update($dto);
    }

    private function checkIfSupplierExist(int $supplier_id): void
    {
        $this->query_builder->setWhere(['supplier_id' => ['_eq' => $supplier_id]], SupplierRepository::COLUMNS_MAPPING);

        $results = $this->supplier_repository->findAllPaginated($this->query_builder)->getResults();

        if ([] === $results) {
            throw new NotFoundException(sprintf('Supplier does not exist with id "%s".', $supplier_id));
        }
    }
}
