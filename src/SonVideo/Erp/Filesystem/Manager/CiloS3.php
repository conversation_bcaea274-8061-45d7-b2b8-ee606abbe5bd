<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Filesystem\Manager;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use SonVideo\Erp\Filesystem\Contract\CreateOrOverwriteTrait;

/**
 * Class CiloS3.
 */
class CiloS3
{
    use CreateOrOverwriteTrait;

    public const FILESYSTEM = 'cilo_s3_filesystem';
    public const ORDERS_INBOX = '/orders/inbox';
    public const ORDERS_SUCCESS = '/orders/success';
    public const ORDERS_FAILURE = '/orders/failure';
    public const CATALOG_DIRECTORY = '/catalog';

    /** @var FilesystemInterface */
    protected $filesystem;

    /** CiloS3 constructor. */
    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(self::FILESYSTEM);
    }

    /** getFilesystem */
    public function getFilesystem(): FilesystemInterface
    {
        return $this->filesystem;
    }

    /**
     * backupOrderFile.
     *
     * @param resource $stream
     *
     * @return bool True on success, false on failure
     *
     * @throws FileExistsException
     */
    public function backupOrderFile(string $filename, $stream): bool
    {
        return $this->filesystem->writeStream(sprintf('%s/%s', self::ORDERS_INBOX, $filename), $stream);
    }

    /**
     * Move the order file $filename from inbox to succes directory.
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function moveOrderToSuccessDir(string $filename): bool
    {
        return $this->filesystem->rename(
            sprintf('%s/%s', self::ORDERS_INBOX, $filename),
            sprintf('%s/%s', self::ORDERS_SUCCESS, $filename)
        );
    }

    /**
     * Move the order file $filename from inbox to failure directory.
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function moveOrderToFailureDir(string $filename): bool
    {
        return $this->filesystem->rename(
            sprintf('%s/%s', self::ORDERS_INBOX, $filename),
            sprintf('%s/%s', self::ORDERS_FAILURE, $filename)
        );
    }

    /**
     * readOrderToProcess.
     *
     * @throws FileNotFoundException
     */
    public function readOrderToProcess(string $filename): string
    {
        return $this->filesystem->read(sprintf('%s/%s', self::ORDERS_INBOX, $filename));
    }

    /**
     * saveCatalog.
     *
     * @return bool
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function saveCatalog(string $filename, string $xml_content)
    {
        $file_path = sprintf('%s/%s', static::CATALOG_DIRECTORY, $filename);

        if ($this->filesystem->has($file_path)) {
            $this->filesystem->delete($file_path);
        }

        return $this->filesystem->write($file_path, $xml_content);
    }
}
