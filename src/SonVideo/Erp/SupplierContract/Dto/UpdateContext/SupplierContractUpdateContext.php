<?php

namespace SonVideo\Erp\SupplierContract\Dto\UpdateContext;

use OpenApi\Annotations as OA;
use SonVideo\Erp\Supplier\Entity\SupplierPaymentInformationEntity;
use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Type;

class SupplierContractUpdateContext
{
    /**
     * @OA\Property(description="Id du contrat fournisseur")
     * @Assert\NotBlank()
     *
     * @var int
     */
    public $supplier_contract_id;

    /**
     * @OA\Property(description="Id du fournisseur")
     * @Assert\NotBlank()
     *
     * @var int
     */
    public $supplier_id;

    /**
     * @OA\Property(description="Id de la marque")
     *
     * @var int|null
     */
    public $brand_id;

    /**
     * @OA\Property(description="Informations de paiement du fournisseur")
     * @Assert\NotBlank()
     *
     * @var SupplierPaymentInformationEntity
     */
    public $payment;

    /**
     * @OA\Property(description="Année du contrat")
     * @Assert\NotBlank()
     *
     * @var int
     */
    public $year;

    /**
     * @OA\Property(description="Description du contrat avec la marque")
     *
     * @var string|null
     */
    public $discount_description;

    /**
     * @OA\Property(description="Plan d'accompagnement marketing")
     * @Assert\NotBlank()
     * @SerializedName("pam")
     * @Type("array")
     *
     * @var array
     */
    public $pam;

    /**
     * @OA\Property(description="Remise de fin d'année")
     * @Assert\NotBlank()
     * @SerializedName("rfa")
     * @Type("array")
     *
     * @var array
     */
    public $rfa;

    /**
     * @OA\Property(description="Englobe la remise inconditionnelle")
     *
     * @SerializedName("additional_rewards")
     * @Type("array")
     *
     * @var array
     */
    public $additional_rewards;

    /**
     * @OA\Property(description="Remise inconditionnelle")
     *
     * @Assert\PositiveOrZero()
     * @Assert\Range(min=0, max=100)
     */
    public float $unconditional_discount;
}
