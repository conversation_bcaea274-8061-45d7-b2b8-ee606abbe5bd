<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrderProduct\Mysql\Repository;

use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderProductCreationContextEntity;
use SonVideo\Erp\Referential\Product;

class CustomerOrderProductRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'customer_order_product_id' => 'pc.id',
        'customer_order_id' => 'pc.id_commande',
        'customer_id' => 'c.id_prospect',
        'customer_order_created_at' => 'c.date_creation',
        'customer_order_closed_at' => 'c.cloture_date',
        'article_id' => 'pc.id_produit',
        'sku' => 'p.reference',
        'article_name' => 'a.modele',
        'brand_name' => 'm.marque',
        'tax_rate' => 'pc.tva',
        'quantity' => 'pc.quantite',
        'selling_price' => 'pc.prix_vente',
        'buy_price' => 'pc.prix_achat',
        'description' => 'pc.description',
        'ecotax_price' => 'pc.prix_ecotaxe',
        'extension_warranty_duration' => 'pc.duree_garantie_ext',
        'extension_warranty_price' => 'pc.prix_garantie_ext',
        'extension_warranty_tax' => 'pc.tva_garantie_ext',
        'extension_warranty_commission' => 'pc.commission_garantie_ext',
        'extension_warranty_commission_tax' => 'pc.tva_commission_garantie_ext',
        'extension_warranty_seller' => 'pc.vendeur_garantie_ext',
        'damage_and_theft_warranty_duration' => 'pc.duree_garantie_vc',
        'damage_and_theft_warranty_price' => 'pc.prix_garantie_vc',
        'damage_and_theft_warranty_commission' => 'pc.commission_garantie_vc',
        'damage_and_theft_warranty_commisision_tax' => 'pc.tva_commission_garantie_vc',
        'damage_and_theft_warranty_seller' => 'pc.vendeur_garantie_vc',
        'discount_type' => 'pc.remise_type',
        'discount_amount' => 'pc.remise_montant',
        'discount_description' => 'pc.remise_description',
        'delivery_note_id' => 'pc.id_bon_livraison',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                pc.id                                                               AS customer_order_product_id,
                pc.id_commande                                                      AS customer_order_id,
                c.id_prospect                                                       AS customer_id,
                c.date_creation                                                     AS customer_order_created_at,
                c.cloture_date                                                      AS customer_order_closed_at,
                pc.id_produit                                                       AS article_id,
                p.reference                                                         AS sku,
                a.modele                                                            AS article_name,
                m.marque                                                            AS brand_name,
                COALESCE(bc.media_300_square_uri, bc.media_largest_uri)             AS article_image,
                pc.tva                                                              AS tax_rate,
                pc.quantite                                                         AS quantity,
                pc.prix_vente                                                       AS selling_price,
                pc.prix_achat                                                       AS buy_price,
                pc.description                                                      AS description,
                pc.prix_ecotaxe                                                     AS ecotax_price,
                pc.prix_sorecop                                                     AS sorecop_price,
                pc.duree_garantie_ext                                               AS extension_warranty_duration,
                pc.prix_garantie_ext                                                AS extension_warranty_price,
                pc.tva_garantie_ext                                                 AS extension_warranty_tax,
                pc.commission_garantie_ext                                          AS extension_warranty_commission,
                pc.tva_commission_garantie_ext                                      AS extension_warranty_commission_tax,
                CASE WHEN pc.vendeur_garantie_ext IS NOT NULL
                       THEN backOffice.GET_COMPUTED_USER_NAME_BY_USERNAME(pc.vendeur_garantie_ext)
                  END                                                               AS extension_warranty_seller,
                pc.duree_garantie_vc                                                AS damage_and_theft_warranty_duration,
                pc.prix_garantie_vc                                                 AS damage_and_theft_warranty_price,
                pc.commission_garantie_vc                                           AS damage_and_theft_warranty_commission,
                pc.tva_commission_garantie_vc                                       AS damage_and_theft_warranty_commisision_tax,
                CASE WHEN pc.vendeur_garantie_vc IS NOT NULL
                       THEN backOffice.GET_COMPUTED_USER_NAME_BY_USERNAME(pc.vendeur_garantie_vc)
                  END                                                               AS damage_and_theft_warranty_seller,
                pc.remise_type                                                      AS discount_type,
                pc.remise_montant                                                   AS discount_amount,
                pc.remise_description                                               AS discount_description,
                pc.id_bon_livraison                                                 AS delivery_note_id
                FROM
                  backOffice.produit_commande pc
                    INNER JOIN backOffice.commande c ON pc.id_commande = c.id_commande
                    INNER JOIN backOffice.article a ON pc.id_produit = a.id_produit
                    INNER JOIN backOffice.produit p ON p.id_produit = a.id_produit
                    LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
                    LEFT JOIN backOffice.batch_catalog bc ON a.id_produit = bc.article_id
                WHERE {conditions}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /** Retrieve bought products */
    public function findBoughtProductsPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                c.id_prospect                                                       AS customer_id,
                MAX(c.date_creation)                                                AS customer_order_created_at,
                pc.id_produit                                                       AS article_id,
                p.reference                                                         AS sku,
                a.modele                                                            AS article_name,
                m.marque                                                            AS brand_name,
                pc.description                                                      AS description,
                COALESCE(bc.media_300_square_uri, bc.media_largest_uri)             AS article_image,
                SUM(pc.quantite)                                                    AS quantity,
                AVG(pc.prix_vente)                                                  AS selling_price,
                AVG(COALESCE(pc.prix_achat, 0))                                     AS buy_price,
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'customer_order_id', c.id_commande,
                        'has_ongoing_premium_warranty', CAST(IF(backOffice.PDT_has_ongoing_premium_warranty(c.id_commande, pc.id_produit), 'true', 'false') AS JSON)
                    )
                ) AS customer_orders
                FROM
                  backOffice.produit_commande pc
                    INNER JOIN backOffice.commande c ON pc.id_commande = c.id_commande
                    INNER JOIN backOffice.article a ON pc.id_produit = a.id_produit
                    INNER JOIN backOffice.produit p ON p.id_produit = a.id_produit
                    LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
                    LEFT JOIN backOffice.batch_catalog bc ON a.id_produit = bc.article_id
                WHERE pc.id_bon_livraison IS NOT NULL
                  AND p.type IN ('article', 'compose')
                  AND ({conditions})
                GROUP BY pc.id_produit
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function fetchAvailability(array $customer_order_product_ids): array
    {
        $sql = <<<SQL
        SELECT
            tmp.id_commande AS customer_order_id,
            tmp.id_produit AS product_id,
            CMD_PDT_get_availability_date(tmp.id_commande, tmp.id_produit) AS availability_date,
            tmp.store_pickup_id AS store_pickup_id,
            tmp.customer_order_product_id AS customer_order_product_id,
            tmp.pc_quantity AS customer_order_product_quantity,
            tmp.sent_quantity AS sent_quantity,
            (tmp.store_quantity - tmp.store_quantity_reserved) AS available_quantity_in_store,
            (tmp.champigny_stock + tmp.havre_stock) - tmp.reserved_qty_in_warehouses AS warehouses_stock,
            IF (tmp.customer_order_flux = 'cloture' OR (tmp.customer_order_flux = 'traitement' AND tmp.is_customer_order_in_progress = 1), 'valid', 'invalid') AS customer_order_status
        FROM
             (
               SELECT c.id_commande AS id_commande,
                      pc.id_produit AS id_produit,
                      c.depot_emport AS store_pickup_id,
                      pc.id AS customer_order_product_id,
                      pc.quantite AS pc_quantity,
                      COALESCE((
                          SELECT pbl.quantite
                          FROM bon_livraison bl
                            INNER JOIN produit_bon_livraison pbl ON bl.id_bon_livraison = pbl.id_bon_livraison
                          WHERE bl.id_commande = c.id_commande
                            AND pbl.id_produit = pc.id_produit
                            AND bl.status = 'expedie'
                            AND pc.id_bon_livraison = pbl.id_bon_livraison
                          GROUP BY pbl.id_produit
                      ), 0) AS sent_quantity,
                      IF(c.depot_emport IS NOT NULL,
                          PDT_ART_qte_stock_depot(pc.id_produit, c.depot_emport),
                          0) AS store_quantity,
                      IF(c.depot_emport IS NOT NULL,
                          PDT_ART_qte_reservee_depot(pc.id_produit, c.id_commande, c.depot_emport),
                          0) AS store_quantity_reserved,
                      COALESCE(PDT_ART_qte_stock_depot(pc.id_produit, 21), 0) AS champigny_stock,
                      COALESCE(PDT_ART_qte_stock_depot(pc.id_produit, 3), 0) AS havre_stock,
                      c.flux AS customer_order_flux,
                      c.en_attente_de_livraison AS is_customer_order_in_progress,
                      PDT_ART_qte_reservee_depot(pc.id_produit, c.id_commande, 21) AS reserved_qty_in_warehouses
                FROM produit_commande pc
                   INNER JOIN commande c ON pc.id_commande = c.id_commande
                WHERE pc.id IN (:customer_order_product_ids)
             ) tmp
        ;
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['customer_order_product_ids' => $customer_order_product_ids]);
    }

    public function fetchAvailableSupplierOrdersWithUnknownExpectedDeliveryDate(
        array $customer_order_product_ids
    ): array {
        $sql = <<<SQL
        SELECT
          pcf.id_commande_fournisseur AS supplier_order_id,
          cf.status AS supplier_order_status,
          lpcf.date_livraison_prevue AS delivery_expected_date,
          pc.id AS customer_order_product_id
        FROM produit_commande pc
          LEFT JOIN produit_commande_fournisseur pcf ON pc.id_produit = pcf.id_produit AND pcf.quantite_livree < pcf.quantite_commandee
          INNER JOIN commande_fournisseur cf ON pcf.id_commande_fournisseur = cf.id_commande_fournisseur AND cf.status IN ('en preparation', 'en cours')
          LEFT JOIN livraison_produit_commande_fournisseur lpcf ON pcf.id = lpcf.id_produit_commande_fournisseur AND lpcf.date_livraison_prevue = '2011-01-01'
        WHERE pc.id IN (:customer_order_product_ids)
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['customer_order_product_ids' => $customer_order_product_ids]);
    }

    public function fetchAvailableTransfers(array $customer_order_product_ids): array
    {
        $sql = <<<SQL
        SELECT
          t.id AS transfer_id,
          t.statut AS status,
          t.id_commande AS customer_order_id,
          pc.id_produit AS product_id,
          pc.id AS customer_order_product_id
        FROM produit_commande pc
            INNER JOIN BO_STK_transfert t ON pc.id_commande = t.id_commande AND t.statut IN ('expedie', 'au depart')
            INNER JOIN BO_STK_produit_transfert pt ON t.id = pt.id_transfert AND pt.id_produit = pc.id_produit
        WHERE pc.id IN (:customer_order_product_ids)
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['customer_order_product_ids' => $customer_order_product_ids]);
    }

    /**
     * @return mixed|null
     *
     * @throws SqlErrorMessageException
     */
    public function create(CustomerOrderProductCreationContextEntity $customer_order_product): ?string
    {
        $customer_order_product_array = $customer_order_product->toArray();

        if (Product::TYPE_PACKAGE === $customer_order_product_array['type']) {
            $sql = 'CALL backOffice.add_bundle_to_order(
                :customer_order_id,
                :product_id,
                :quantity,
                :selling_price_tax_included,
                :discount_amount,
                :group_type,
                :group_description
            )';
        } else {
            $sql = 'CALL backOffice.add_product_to_order(
                :customer_order_id,
                :product_id,
                :quantity,
                :selling_price_tax_included,
                :vat,
                COALESCE(backOffice.PDT_ART_px_achat(:product_id), 0),
                :description,
                :discount_type,
                :discount_amount,
                :discount_description,
                :ecotax_price,
                :sorecop_price,
                :group_type,
                :group_description,
                :warranty_duration_ext,
                :warranty_unit_selling_price_ext,
                :warranty_duration_tb,
                :warranty_unit_selling_price_tb,
                @result
            )';
        }

        return $this->legacy_pdo->fetchValueFromOutputParameter($sql, $customer_order_product_array);
    }

    public function createStatInitial(CustomerOrderProductCreationContextEntity $customer_order_product): int
    {
        $sql = <<<SQL
        INSERT INTO data_warehouse.customer_order_product_initial
          (customer_order_id, product_id, quantity, selling_price_tax_included, vat, purchase_price, discount_amount, ecotax_price, sorecop_price, warranties_price, promo_code, available_quantity, estimated_supplier_order_delivery_date)
        VALUES (
            :customer_order_id,
            :product_id,
            :quantity,
            :selling_price_tax_included,
            :vat,
            COALESCE(backOffice.PDT_ART_px_achat(:product_id), 0),
            :discount_amount,
            :ecotax_price,
            :sorecop_price,
            COALESCE(:warranty_unit_selling_price_ext, 0) + COALESCE(:warranty_unit_selling_price_tb, 0),
            :promo_code,
            COALESCE(backOffice.PDT_ART_V_qte_dispo_resa(:product_id, NULL), 0) - backOffice.PDT_ART_qte_en_attente(:product_id),
            backOffice.CMD_PDT_get_availability_date(customer_order_id, product_id)
        )
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, $customer_order_product->toArray());
    }

    public function fetchShippingProductFromOrder(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT
            pc.id                                                               AS customer_order_product_id,
            pc.id_commande                                                      AS customer_order_id,
            pc.id_produit                                                       AS article_id,
            pc.tva                                                              AS tax_rate,
            pc.quantite                                                         AS quantity,
            pc.prix_vente                                                       AS selling_price,
            pc.prix_achat                                                       AS buy_price,
            pc.description                                                      AS description
            FROM
                backOffice.produit_commande pc
            WHERE
                id_produit = :product_id
            AND id_commande = :customer_order_id
        SQL;

        return $this->legacy_pdo->fetchOne($sql, [
            'product_id' => Product::SHIPMENT_PRODUCT_ID,
            'customer_order_id' => $customer_order_id,
        ]);
    }

    /** @throws \Exception */
    public function updateEffectiveSupplierOrderDeliveryDate(array $customer_order_product_lines): int
    {
        $sql = <<<SQL
            UPDATE backOffice.produit_commande
            SET effective_supplier_order_delivery_date = now()
            WHERE id IN (:supplier_order_product_ids)
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'supplier_order_product_ids' => $customer_order_product_lines,
        ]);
    }

    public function findAllAwaitingSupplierOrderDeliveryByProductId(int $product_id): array
    {
        $sql = <<<SQL
        SELECT
            pc.id_commande AS customer_order_id,
            pc.id AS customer_order_product_id,
            pc.id_produit AS product_id,
            pc.quantite AS quantity,
            c.date_creation AS creation_date,
            pc.effective_supplier_order_delivery_date AS effective_supplier_order_delivery_date,
            backOffice.CMD_is_paid(c.flux, c.V_statut_traitement) AS is_paid
        FROM produit_commande pc
        INNER JOIN commande c ON c.id_commande = pc.id_commande
        WHERE pc.id_produit=:product_id
        AND pc.effective_supplier_order_delivery_date IS NULL
        ORDER BY is_paid DESC, creation_date DESC;
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['product_id' => $product_id]);
    }
}
