<?php

namespace SonVideo\Erp\Payment\Manager;

use App\Adapter\Serializer\SerializerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderPaymentCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderProductCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CustomerOrderBasicInfo;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\Payment\Client\PaymentV2ClientInterface;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationPayload;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationRequest;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\PaymentApiV2;
use SonVideo\Erp\Referential\PaymentWorkflow;
use SonVideo\Erp\System\Manager\FeatureStatus;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class PaymentV2State implements PaymentV2StateInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    private ShipmentMethodReadRepository $shipment_method_read_repository;

    private PaymentV2ClientInterface $client;

    private SerializerInterface $serializer;

    private FeatureStatus $feature_status;

    public function __construct(
        ShipmentMethodReadRepository $shipment_method_read_repository,
        PaymentV2ClientInterface $client,
        SerializerInterface $serializer,
        FeatureStatus $feature_status
    ) {
        $this->shipment_method_read_repository = $shipment_method_read_repository;
        $this->client = $client;
        $this->serializer = $serializer;
        $this->feature_status = $feature_status;
    }

    public function isValidWithProvidedOrderContext(
        CustomerOrderCreationContextDto $context_entity
    ): CustomerOrderCreationContextDto {
        // Bail out if the origin does not support Payment V2
        if (CustomerOrderOrigin::SITE !== $context_entity->origin) {
            return $context_entity;
        }

        if (!$this->hasHandledPaymentMethods($context_entity->payments, $context_entity->customer_id)) {
            return $context_entity;
        }

        if (null === $context_entity->return_url) {
            throw new \InvalidArgumentException('This origin uses Payment V2, the return URL (context->return_url) should not be null');
        }

        $shipment_method = $this->shipment_method_read_repository->findOneById(
            $context_entity->shipment_method->shipment_method_id,
            'TRUE'
        );

        // Re-format context to be usable on Payment V2
        $context_for_payment_v2 = [
            'origin' => $context_entity->origin,
            'ip_address' => $context_entity->ip_address,
            'estimated_delivery_date' => $context_entity->estimated_delivery_date instanceof \DateTimeInterface
                    ? $context_entity->estimated_delivery_date->format('Y-m-d')
                    : null,
            'total_price' => $context_entity->total_price,
            'customer' => [
                'customer_id' => $context_entity->customer_id,
            ],
            'billing_address' => $context_entity->billing_address,
            'shipping_address' => $context_entity->shipping_address,
            'shipment_methods' => [
                [
                    'shipment_method_id' => $context_entity->shipment_method->shipment_method_id,
                    'carrier_code' => $shipment_method->carrier->code,
                    'price' => $context_entity->shipment_method->cost,
                    'is_express' => $shipment_method->carrier->is_express,
                    'is_store' => null !== $shipment_method->store_pickup_id,
                    'is_relay' => null !== $context_entity->shipment_method->relay_id,
                ],
            ],
            'payments' => $this->serializer->normalize($this->addIdentifierToNewPayments($context_entity->payments)),
            'products' => array_map(
                static fn (CustomerOrderProductCreationContextDto $product): array => [
                    'sku' => $product->sku,
                    'total_price' => $product->computeTotalPrice(),
                    'quantity' => $product->quantity,
                ],
                $context_entity->products
            ),
        ];

        // Validate the context
        $result = $this->client->post(PaymentApiV2::VERIFY_URL, $context_for_payment_v2);
        $context_entity->payments = $this->handleResponse(
            $result,
            $context_entity->payments,
            $context_entity->return_url
        );

        return $context_entity;
    }

    public function isValidWithExistingCustomerOrder(
        CustomerOrderPaymentCreationRequestDto $request_context,
        CustomerOrderBasicInfo $customer_order_basic_info
    ): CustomerOrderPaymentCreationRequestDto {
        // Bail out if the origin does not support Payment V2
        if (CustomerOrderOrigin::SITE !== $customer_order_basic_info->origin) {
            return $request_context;
        }

        if (!$this->hasHandledPaymentMethods($request_context->payments, $customer_order_basic_info->customer_id)) {
            return $request_context;
        }

        if (null === $request_context->return_url) {
            throw new \InvalidArgumentException('This origin uses Payment V2, the return URL (context->return_url) should not be null');
        }

        // Validate the context
        $result = $this->client->post(
            strtr(PaymentApiV2::VERIFY_URL_FOR_CUSTOMER_ORDER, [
                '{customer_order_id}' => $request_context->customer_order_id,
            ]),
            [
                'payments' => $this->serializer->normalize(
                    $this->addIdentifierToNewPayments($request_context->payments)
                ),
            ]
        );

        $request_context->payments = $this->handleResponse(
            $result,
            $request_context->payments,
            $request_context->return_url
        );

        return $request_context;
    }

    /**
     * @param CustomerOrderPaymentCreationContextDto[] $payments
     *
     * @return PaymentV2CreationRequest[]
     */
    private function addIdentifierToNewPayments(array $payments): array
    {
        foreach ($payments as $index => $payment) {
            $payment->external_reference = sprintf('NEW_%s', $index);
        }

        return array_map(
            static fn (
                CustomerOrderPaymentCreationContextDto $payment
            ): PaymentV2CreationRequest => PaymentV2CreationRequest::fromCreationContextDto($payment),
            $payments
        );
    }

    /**
     * @param CustomerOrderPaymentCreationContextDto[] $payments
     *
     * @return CustomerOrderPaymentCreationContextDto[]
     *
     * @throws ExceptionInterface
     */
    private function handleResponse(array $result, array $payments, string $return_url): array
    {
        // Update payments for the customer order context
        /** @var PaymentV2CreationPayload[] $verified_payments */
        $verified_payments = $this->serializer->denormalize($result, PaymentV2CreationPayload::class . '[]');
        $verified_payments_with_external_reference = [];

        foreach ($verified_payments as $verified_payment) {
            if (null === $verified_payment->external_reference) {
                continue;
            }

            $verified_payments_with_external_reference[$verified_payment->external_reference] = $verified_payment;
        }

        $this->logger->debug('[Payment V2] Verified payments with external reference', [
            'value' => json_encode($verified_payments_with_external_reference, JSON_PARTIAL_OUTPUT_ON_ERROR),
        ]);

        foreach ($payments as $payment) {
            if (!isset($verified_payments_with_external_reference[$payment->external_reference])) {
                $this->logger->debug('[Payment V2] Context payment not found', [
                    'value' => json_encode($payment, JSON_PARTIAL_OUTPUT_ON_ERROR),
                ]);

                throw new \UnexpectedValueException('Incoherent payment mapping');
            }

            $verified_payment = $verified_payments_with_external_reference[$payment->external_reference];

            if (PaymentWorkflow::V2 !== $verified_payment->workflow) {
                continue;
            }

            $payment->workflow = $verified_payment->workflow;
            $verified_payment->return_url = str_replace('{code}', $verified_payment->code, $return_url);

            $payment->setPaymentV2VerifiedPayment($verified_payment);
        }

        return $payments;
    }

    private function hasHandledPaymentMethods(array $payments, int $customer_id): bool
    {
        return array_reduce(
            $payments,
            fn ($carry, $payment): bool => $carry || $this->canHandle(trim($payment->payment_mean), $customer_id),
            false
        );
    }

    public function canHandle(string $payment_method_code, int $customer_id): bool
    {
        if (PaymentMean::PRESTO === $payment_method_code) {
            return true;
        }

        if (
            PaymentMean::WORLDLINE_FLOA === $payment_method_code &&
            $this->feature_status->isActiveForCustomer('payment_v2:floa', $customer_id)
        ) {
            return true;
        }

        if (
            PaymentMean::WORLDLINE_BANCONTACT === $payment_method_code &&
            $this->feature_status->isActiveForCustomer('payment_v2:bancontact', $customer_id)
        ) {
            return true;
        }

        if (
            in_array(
                $payment_method_code,
                [
                    PaymentMean::WORLDLINE_AMERICAN_EXPRESS,
                    PaymentMean::WORLDLINE_CREDIT_CARD,
                    PaymentMean::WORLDLINE_GROUPED_CREDIT_CARD,
                ],
                true
            )
        ) {
            return true;
        }

        return $this->feature_status->isActiveForCustomer('payment_v2', $customer_id) &&
            in_array($payment_method_code, PaymentApiV2::HANDLED_PAYMENT_CODES, true);
    }
}
