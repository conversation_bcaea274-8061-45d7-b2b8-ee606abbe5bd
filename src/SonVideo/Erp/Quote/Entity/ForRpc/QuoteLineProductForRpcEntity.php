<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Entity\ForRpc;

use App\Entity\AbstractEntity;
use SonVideo\Erp\Quote\Entity\ProductInfoSelectedWarrantyEntity;

class QuoteLineProductForRpcEntity extends AbstractEntity
{
    public ProductInfoForRpcEntity $product;

    public int $quantity;

    public float $selling_price_tax_excluded;

    public float $selling_price_tax_included;

    public float $unit_discount_amount_abs_tax_included;

    public float $total_discount_amount;

    public float $total_price;

    /** @var ProductInfoSelectedWarrantyEntity[] */
    public array $selected_warranties = [];
}
