<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Entity\ForRpc;

use App\Entity\AbstractEntity;

class QuotePricesForRpc extends AbstractEntity
{
    public float $total_discount_tax_excluded;

    public float $total_discount_tax_included;

    public float $total_price_tax_excluded;

    public float $total_vat;

    public float $total_price_tax_included;

    public float $computed_vat_rate;
}
