<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Mysql\Repository;

use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Quote\Entity\ProductInfoEntity;
use SonVideo\Erp\Quote\Entity\QuoteLineProductEntity;

class QuoteLineProductRepository extends AbstractLegacyRepository
{
    use MapToEntityTrait;

    private const COLUMNS_AUTHORIZED_FOR_UPDATE = ['quantity', 'unit_discount_amount', 'selected_warranties'];

    /** @throws \JsonException */
    public function add(
        int $quote_id,
        ProductInfoEntity $product,
        int $quantity = 1,
        float $unit_discount_amount = 0,
        array $selected_warranties = []
    ): int {
        $sql = <<<SQL
        INSERT INTO backOffice.quote_line (quote_id, display_order)
        SELECT :quote_id, COALESCE(MAX(display_order) + 1, 1)
          FROM backOffice.quote_line
          WHERE quote_id = :quote_id
        SQL;

        $this->legacy_pdo->fetchAffected($sql, ['quote_id' => $quote_id]);
        $quote_line_id = $this->legacy_pdo->lastInsertId();

        $sql = <<<SQL
          INSERT INTO backOffice.quote_line_product(quote_line_id, product_id, quantity, unit_discount_amount, product, selected_warranties)
            VALUES
              (:quote_line_id, :product_id, :quantity, :unit_discount_amount, :product, :selected_warranties);
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'quote_line_id' => $quote_line_id,
            'product_id' => $product->product_id,
            'quantity' => $quantity,
            'unit_discount_amount' => $unit_discount_amount,
            'product' => json_encode($product, JSON_THROW_ON_ERROR),
            'selected_warranties' => json_encode($selected_warranties, JSON_THROW_ON_ERROR),
        ]);

        return $this->legacy_pdo->lastInsertId();
    }

    /**
     * update.
     *
     * @throws \JsonException
     */
    public function update(int $quote_line_product_id, array $data): int
    {
        if ([] === $data) {
            throw new \UnexpectedValueException('No data supplied for update');
        }

        $not_authorized_columns = array_keys(array_diff_key($data, array_flip(self::COLUMNS_AUTHORIZED_FOR_UPDATE)));

        if ([] !== $not_authorized_columns) {
            throw new \UnexpectedValueException(sprintf('Invalid column(s): [%s]. Authorized columns are: [%s]', implode(', ', $not_authorized_columns), implode(', ', self::COLUMNS_AUTHORIZED_FOR_UPDATE)));
        }

        if (array_key_exists('selected_warranties', $data)) {
            // Format for insertion with PDO
            $data['selected_warranties'] = json_encode($data['selected_warranties'], JSON_THROW_ON_ERROR);
        }

        $statement = array_map(fn ($value): string => sprintf('%s = :%s', $value, $value), array_keys($data));

        $sql = strtr(
            'UPDATE backOffice.quote_line_product SET {stmt} WHERE quote_line_product_id = :quote_line_product_id',
            [
                '{stmt}' => "\n" . implode(", \n", $statement) . "\n",
            ]
        );

        return $this->legacy_pdo->fetchAffected(
            $sql,
            array_merge($data, ['quote_line_product_id' => $quote_line_product_id])
        );
    }

    public function findById(int $quote_line_product_id): ?QuoteLineProductEntity
    {
        $sql = <<<SQL
        SELECT
          qlp.quote_line_product_id,
          qlp.quote_line_id,
          qlp.product_id,
          p.reference AS sku,
          qlp.quantity,
          qlp.unit_discount_amount,
          qlp.product,
          qlp.selected_warranties,
          ql.quote_id
          FROM backOffice.quote_line_product qlp
          INNER JOIN backOffice.quote_line ql ON qlp.quote_line_id = ql.quote_line_id
          INNER JOIN backOffice.produit p ON p.id_produit = qlp.product_id
        WHERE quote_line_product_id = :quote_line_product_id
        SQL;

        $quote_line_data = $this->legacy_pdo->fetchOne($sql, ['quote_line_product_id' => $quote_line_product_id]);

        return $quote_line_data ? $this->data_loader->hydrate($quote_line_data, QuoteLineProductEntity::class) : null;
    }

    public function findAllByQuoteId(int $quote_id): array
    {
        $sql = <<<SQL
        SELECT
          qlp.quote_line_product_id,
          qlp.quote_line_id,
          qlp.product_id,
          p.reference AS sku,
          qlp.quantity,
          qlp.unit_discount_amount,
          qlp.product,
          qlp.selected_warranties,
          ql.quote_id
          FROM backOffice.quote_line_product qlp
          INNER JOIN backOffice.quote_line ql ON qlp.quote_line_id = ql.quote_line_id
          INNER JOIN backOffice.produit p ON p.id_produit = qlp.product_id
        WHERE ql.quote_id = :quote_id
        ORDER BY ql.display_order
        SQL;

        $quote_line_data = $this->legacy_pdo->fetchAssoc($sql, ['quote_id' => $quote_id]);

        return $this->mapToEntitiesData($quote_line_data, QuoteLineProductEntity::class);
    }

    /** @throws NotFoundException */
    public function fetchProductInfoBySku(string $sku): object
    {
        $sql = <<<SQL
        SELECT
          JSON_OBJECT(
            'product_id', p.id_produit,
            'sku', p.reference,
            'description', if(a.stock_a_id_produit, CONCAT(a.modele, ' ',backOffice.PDT_ART_destock_format_basket_description(ad.state, ad.public_description)),  a.description_panier),
            'short_description', CONCAT(
              if(a.description_courte = '', CONCAT(m.marque, ' ', a.modele), a.description_courte),
              if(a.stock_a_id_produit, ' - seconde vie', '')
            ),
            'image', backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)),
            'weight', a.poids,
            'outsize', s.hors_gabarit = 1 /* cast to bool */,
            'selling_price_tax_included', a.prix_vente,
            'ecotax_price', a.prix_ecotaxe,
            'sorecop_price', a.prix_sorecop,
            'vat', p.tva,
            'purchase_price', COALESCE(backOffice.PDT_ART_px_achat(p.id_produit), 0),
            'promo_budget', IF(
                NOW() BETWEEN apb.start_at AND apb.end_at,
                JSON_OBJECT(
                    'amount', apb.amount,
                    'end_at', apb.end_at
                ),
                NULL
            ),
            'type', CASE
              WHEN a.compose = 1
                THEN 'compose'
              WHEN p.reference LIKE 'DESTOCK%'
                THEN 'destock'
              ELSE 'article' END,
            'eligible_warranties', backOffice.PDT_get_eligible_warranties(p.id_produit)
            )                                AS product,
          1                                  AS quantity,
          0                                  AS unit_discount_amount,
          a.prix_vente_generalement_constate AS selling_price_generally_observed,
          a.status                           AS status
          FROM
            backOffice.produit p
            INNER JOIN backOffice.CTG_TXN_souscategorie s ON p.id_souscategorie = s.id
            LEFT JOIN backOffice.article a ON p.id_produit = a.id_produit
            LEFT JOIN backOffice.batch_catalog bc ON p.id_produit = bc.article_id
            LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
            LEFT JOIN backOffice.article_promo_budget apb ON p.id_produit = apb.article_id
            LEFT JOIN backOffice.article_destock ad ON p.id_produit = ad.article_id
          WHERE
                p.reference = :sku;
        SQL;

        $product_data = $this->legacy_pdo->fetchOne($sql, ['sku' => $sku]);

        if (false === $product_data) {
            throw new NotFoundException(sprintf('Product not found with sku "%s"', $sku));
        }
        $product = (object) $product_data;

        $product->product = json_decode($product->product, null, 512, JSON_THROW_ON_ERROR);

        // Re-map for entity hydration
        $product->product->eligible_warranties = array_map(function ($warranty) {
            $warranty->unit_selling_price_tax_included = (float) $warranty->unit_selling_price_tax_included;

            return $warranty;
        }, $product->product->eligible_warranties);

        return $product;
    }

    /** @throws NotFoundException */
    public function fetchProductsInfoByPackageId(int $package_id): array
    {
        $sql = <<<'SQL'
        SELECT
          JSON_OBJECT(
            'product_id', p.id_produit,
            'sku', p.reference,
            'description', a.description_panier,
            'short_description', CONCAT(a.description_courte, '\nIssue du pack: ', a2.modele),
            'image', backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)),
            'weight', a2.poids,
            'outsize', s.hors_gabarit = 1 /* cast to bool */,
            'selling_price_tax_included', a.prix_vente,
            'ecotax_price', a.prix_ecotaxe,
            'sorecop_price', a.prix_sorecop,
            'vat', p.tva,
            'purchase_price', COALESCE(backOffice.PDT_ART_px_achat(p.id_produit), 0),
            'type', CASE
              WHEN a.compose = 1
                THEN 'compose'
              WHEN p.reference LIKE 'DESTOCK%'
                THEN 'destock'
              ELSE 'article' END,
            'eligible_warranties', backOffice.PDT_get_eligible_warranties(p.id_produit)
          ) AS product,
          pc.quantite AS quantity,
          0 AS unit_discount_amount,
          a.prix_vente_generalement_constate AS selling_price_generally_observed,
          a.status AS status
          FROM
            backOffice.produit_compose pc
              LEFT JOIN backOffice.produit p ON pc.id_produit = p.id_produit
              LEFT JOIN backOffice.CTG_TXN_souscategorie s ON p.id_souscategorie = s.id
              LEFT JOIN backOffice.article a ON p.id_produit = a.id_produit
              LEFT JOIN backOffice.article a2 ON pc.id_compose = a2.id_produit
              LEFT JOIN backOffice.batch_catalog bc ON p.id_produit = bc.article_id
          WHERE
            pc.id_compose = :package_id
          ORDER BY p.id_produit
        SQL;

        $products = $this->legacy_pdo->fetchAssoc($sql, ['package_id' => $package_id]);

        if ([] === $products) {
            throw new NotFoundException(sprintf('Products not found with package id "%s"', $package_id));
        }

        return array_map(static function (array $line) {
            $line['product'] = json_decode($line['product'], false, 512, JSON_THROW_ON_ERROR);

            return $line;
        }, $products);
    }
}
