<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Contract;

use App\Exception\InternalErrorException;
use SonVideo\Erp\Quote\Entity\ProductLineInfoEntity;
use SonVideo\Erp\Quote\Manager\Decorator\QuoteLineProductDecorator;
use SonVideo\Erp\Referential\InternalError;

trait QuoteLineProductPricesTrait
{
    /**
     * @return ProductLineInfoEntity[]
     *
     * @throws InternalErrorException
     */
    protected function allocateAProratedDiscount(
        array $products_line_info,
        float $total_discount_amount,
        bool $has_discount_admin_permission = false
    ): array {
        if ($total_discount_amount > 0) {
            throw new InternalErrorException(InternalError::QUOTE_DISCOUNT_AMOUNT_MUST_BE_NEGATIVE, new \InvalidArgumentException(sprintf('Unit discount amount must be negative, "%s" given', $total_discount_amount)));
        }

        // extract all margins and sum them
        $total_margin = array_sum(
            array_map(static fn (ProductLineInfoEntity $entry): float => $entry->margin, $products_line_info)
        );

        /** @var $product_line_info ProductLineInfoEntity */
        foreach ($products_line_info as $product_line_info) {
            // Set new discount amount
            $product_line_info->unit_discount_amount =
                0.0 === (float) $product_line_info->margin
                    ? 0
                    : round(
                        (($product_line_info->margin / $total_margin) * $total_discount_amount) /
                            $product_line_info->quantity,
                        2
                    );

            // Recompute prices
            $product_line_info = QuoteLineProductDecorator::computeProductPrices($product_line_info);

            $this->validatePrices($product_line_info, $has_discount_admin_permission);
        }

        return $products_line_info;
    }

    /** @throws InternalErrorException */
    protected function validatePrices(
        ProductLineInfoEntity $product_line_info,
        bool $has_discount_admin_permission = false
    ): void {
        if (null === $product_line_info->selling_price_tax_excluded) {
            $product_line_info = QuoteLineProductDecorator::computeProductPrices($product_line_info);
        }

        if ($product_line_info->unit_discount_amount > 0) {
            throw new InternalErrorException(InternalError::QUOTE_DISCOUNT_AMOUNT_MUST_BE_NEGATIVE, new \InvalidArgumentException(sprintf('Unit discount amount must be negative, "%s" given', $product_line_info->unit_discount_amount)), ['sku' => $product_line_info->product->sku, 'unit_discount_amount' => $product_line_info->unit_discount_amount]);
        }

        if ($product_line_info->selling_price_with_discount < 0) {
            throw new InternalErrorException(InternalError::QUOTE_SELLING_PRICE_MUST_BE_POSITIVE, new \InvalidArgumentException(vsprintf('Selling price must be positive, "%s" given', [$product_line_info->selling_price_with_discount])), ['sku' => $product_line_info->product->sku, 'selling_price_with_discount' => $product_line_info->selling_price_with_discount]);
        }

        if (0.0 === $product_line_info->unit_discount_amount) {
            return;
        }

        // compute margin rate
        // Beware! In english, 'taux de marque' = 'margin rate' and 'taux de marge' = 'markup rate'
        $min_margin_rate = $has_discount_admin_permission ? 0 : 0.05;

        if ($product_line_info->margin_rate_with_discount < $min_margin_rate) {
            throw new InternalErrorException(InternalError::QUOTE_INVALID_MARGIN_RATE, new \LogicException(sprintf('Margin rate (%s) can\'t be less than "%s"', $product_line_info->margin_rate_with_discount, $min_margin_rate)), ['sku' => $product_line_info->product->sku, 'margin_rate_with_discount' => $product_line_info->margin_rate_with_discount, 'min_margin_rate' => $min_margin_rate]);
        }
    }
}
