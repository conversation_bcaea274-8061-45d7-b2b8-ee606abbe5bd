<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Manager;

use App\Contract\DataLoaderAwareInterface;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\Quote\QuoteEmailDispatcher;
use SonVideo\Erp\Quote\Contract\QuoteHelperTrait;
use SonVideo\Erp\Quote\Contract\QuoteLineProductCreateTrait;
use SonVideo\Erp\Quote\Contract\QuoteRepositoryAwareInterface;
use SonVideo\Erp\Quote\Contract\QuoteRepositoryAwareTrait;
use SonVideo\Erp\Quote\Contract\QuoteRetrieverTrait;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Entity\QuoteLineEntity;
use SonVideo\Erp\Quote\Manager\Decorator\QuoteDecorator;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\Erp\User\Entity\UserEntity;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use SonVideo\Synapps\Client\RpcClientAwareInterface;
use SonVideo\Synapps\Client\RpcClientAwareTrait;

final class QuoteEmailSender implements QuoteRepositoryAwareInterface, DataLoaderAwareInterface, RpcClientAwareInterface, LoggerAwareInterface
{
    use QuoteRepositoryAwareTrait;
    use QuoteRetrieverTrait;
    use QuoteLineProductCreateTrait;
    use QuoteHelperTrait;
    use RpcClientAwareTrait;
    use LoggerAwareTrait;

    private const SYNAPPS_SUBJECT = 'quote.sent.erp_server';
    private const FOCMS_PAY_QUOTE_ENDPOINT = '/mon-compte/payer-mon-devis/';
    private const FOCMS_ADD_QUOTE_ENDPOINT = '/mon-panier/ajouter-quote/';
    public const RPC_METHOD_GET_TOKEN = 'token:generate_for_quotation';

    private SystemEventLogger $system_event_logger;

    private QuoteEmailDispatcher $quote_email_dispatcher;

    private QuoteDecorator $quote_decorator;

    private AccountQueryRepository $account_repository;

    private SynappsNotifier $synapps_notifier;

    private string $server_env;
    private string $fo_cms_url;

    public function __construct(
        SystemEventLogger $system_event_logger,
        QuoteEmailDispatcher $quote_email_dispatcher,
        QuoteDecorator $quote_decorator,
        AccountQueryRepository $account_repository,
        SynappsNotifier $synapps_notifier,
        string $server_env,
        string $fo_cms_url
    ) {
        $this->system_event_logger = $system_event_logger;
        $this->quote_email_dispatcher = $quote_email_dispatcher;
        $this->quote_decorator = $quote_decorator;
        $this->account_repository = $account_repository;
        $this->synapps_notifier = $synapps_notifier;
        $this->server_env = $server_env;
        $this->fo_cms_url = $fo_cms_url;
    }

    /**
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws EmailRequestPayloadException
     */
    public function send(int $quote_id, UserEntity $user): void
    {
        if (!$this->quote_repository->exists($quote_id)) {
            throw new NotFoundException(sprintf('Quote not found with id "%s".', $quote_id));
        }

        $quote = $this->retrieveFullyHydratedQuote($quote_id, $this->quote_repository);

        if (!$this->canSend($quote)) {
            throw new \LogicException('Quote cannot be sent.');
        }

        $quote->sent_at = new \DateTime();
        $updates_values = ['sent_at' => $quote->sent_at->format('Y-m-d H:i:s')];

        if (!$quote->expired_at instanceof \DateTimeInterface) {
            $quote->expired_at = (new \DateTime('+' . $quote->valid_until . ' days'))->setTime(23, 59, 59);
            $updates_values['expired_at'] = $quote->expired_at->format('Y-m-d H:i:s');
        }

        $this->quote_repository->update($quote_id, $updates_values, $user->id_utilisateur);

        $loggable = $this->quote_email_dispatcher->dispatch(
            $this->format($this->quote_decorator->decorate($quote), $user)
        );

        $this->system_event_logger->log($loggable);

        $this->synapps_notifier->notify(self::SYNAPPS_SUBJECT, ['quote_id' => $quote_id]);
    }

    /**
     * dataFormatter.
     *
     * @return array{from: array{name: string, email: string, reply_to: string}, cc: string, to: mixed, context: mixed[], _rel: array{customer: mixed, quote: mixed}, _sent_by: mixed}
     *
     * @throws \Exception
     */
    private function format(array $decorated_quote, UserEntity $user): array
    {
        // We can't send primary key at null to mailjet
        $decorated_quote['customer_order_aggregates'] ??= [];
        $decorated_quote['intra_community_vat'] ??= 0;
        $decorated_quote['message'] = nl2br($decorated_quote['message']);

        // Get and pass autologin token
        $rpc_result = $this->rpc_client->call('bo-cms', self::RPC_METHOD_GET_TOKEN, [
            $decorated_quote['customer_email'],
            $user->utilisateur,
        ]);

        if (isset($rpc_result['error'])) {
            throw new \UnexpectedValueException('Failed to retrieve an autologin CMS Token for quote');
        }

        foreach ($decorated_quote['quote_line_aggregates'] as $key => $value) {
            $this->logger->debug('VALUE', $value);

            if (QuoteLineEntity::TYPE_PRODUCT !== $value['type']) {
                continue;
            }

            if (str_contains($value['data']['product']['image'], '_300_square')) {
                continue;
            }

            $decorated_quote['quote_line_aggregates'][$key]['data']['product']['image'] =
                '/images/ui/uiV3/graphics/no-img-300.png';
        }

        $token = $rpc_result['result']['token']['token'];

        $link_to_pay_quote_endpoint =
            QuoteEntity::TYPE_QUOTATION === $decorated_quote['type']
                ? self::FOCMS_PAY_QUOTE_ENDPOINT
                : self::FOCMS_ADD_QUOTE_ENDPOINT;
        $decorated_quote['link_to_pay_quote'] =
            $this->fo_cms_url . $link_to_pay_quote_endpoint . $decorated_quote['quote_id'] . '?autologin=' . $token;

        $quote_creator = $this->account_repository->findOneById($decorated_quote['created_by']);

        // mailjet doesn't accept null values on root level
        foreach ($decorated_quote as $key => $value) {
            if (null === $value) {
                $decorated_quote[$key] = '';
            }
        }

        return [
            'from' => [
                'name' => 'Son-Vidéo',
                'email' => '<EMAIL>',
                'reply_to' => $quote_creator->email,
            ],
            'cc' => $quote_creator->email,
            'to' => 'prod' === $this->server_env ? $decorated_quote['customer_email'] : '<EMAIL>',
            'context' => $decorated_quote,
            '_rel' => [
                'customer' => $decorated_quote['customer_id'],
                'quote' => $decorated_quote['quote_id'],
            ],
            '_sent_by' => $user->id_utilisateur,
        ];
    }

    private function canSend(QuoteEntity $quote): bool
    {
        switch ($quote->get('type')) {
            case QuoteEntity::TYPE_OFFER:
                return [] === $quote->getRemainingStepsToConvertToOffer();
            case QuoteEntity::TYPE_QUOTATION:
                return [] === $quote->getRemainingStepsToConvertToQuotation();
        }

        throw new \LogicException(sprintf('Quote of type "%s" cannot be sent.', $quote->get('type')));
    }
}
