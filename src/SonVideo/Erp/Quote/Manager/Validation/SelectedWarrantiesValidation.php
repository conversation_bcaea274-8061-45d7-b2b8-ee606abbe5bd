<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Manager\Validation;

use SonVideo\Erp\Quote\Entity\ProductInfoSelectedWarrantyEntity;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;

final class SelectedWarrantiesValidation
{
    public static function rules(): Constraint
    {
        return new Assert\Collection([
            'selected_warranties' => new Assert\Required([
                new Assert\Type('array'),
                new Assert\All([
                    new Assert\Collection([
                        'type' => new Assert\Required([
                            new Assert\NotBlank(),
                            new Assert\Type('string'),
                            new Assert\Choice([
                                ProductInfoSelectedWarrantyEntity::TYPE_EXTENSION,
                                ProductInfoSelectedWarrantyEntity::TYPE_THEFT_BREAKDOWN,
                            ]),
                        ]),
                        'label' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string')]),
                        'unit_selling_price_tax_included' => new Assert\Required([
                            new Assert\NotBlank(),
                            new Assert\PositiveOrZero(),
                            new Assert\Type('numeric'),
                        ]),
                        'duration' => new Assert\Required([
                            new Assert\NotBlank(),
                            new Assert\PositiveOrZero(),
                            new Assert\Type('numeric'),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }
}
