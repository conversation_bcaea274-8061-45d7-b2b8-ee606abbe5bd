<?php

namespace SonVideo\Erp\Quote\Manager;

use App\Contract\DataLoaderAwareInterface;
use App\Exception\NotFoundException;
use SonVideo\Erp\Quote\Contract\QuoteRetrieverTrait;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteLineRepository;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;

final class QuoteLineUpdater implements DataLoaderAwareInterface
{
    use QuoteRetrieverTrait;

    private QuoteLineRepository $repo;

    private QuoteRepository $quote_repository;

    public function __construct(QuoteLineRepository $repo, QuoteRepository $quote_repository)
    {
        $this->repo = $repo;
        $this->quote_repository = $quote_repository;
    }

    /** Move a quote line and return quote */
    public function moveQuoteLine(int $quote_id, int $quote_line_id, int $new_position): QuoteEntity
    {
        $quote_line = $this->repo->findByWithType($quote_line_id);

        if (!$quote_line || (int) $quote_line['quote_id'] !== $quote_id) {
            throw new NotFoundException(sprintf('The quote_line %s is unknown from quote %s', $quote_line_id, $quote_id));
        }

        $this->repo->updateQuoteLinePosition($quote_id, $new_position, $quote_line['display_order']);

        return $this->retrieveFullyHydratedQuote($quote_id, $this->quote_repository);
    }
}
