<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Client;

use App\Client\AbstractCurlClient;

class CarrierAWSClient extends AbstractCurlClient
{
    /** CarrierAWSClient constructor. */
    public function __construct(string $carrier_aws_end_point)
    {
        $this->setBaseUrl($carrier_aws_end_point);
    }

    /**
     * get.
     *
     * @param $method
     *
     * @return mixed
     *
     * @throws \Exception
     */
    public function get($method, array $params)
    {
        $options = [
            'returntransfer' => true,
            'ssl_verifypeer' => false,
            'ssl_verifyhost' => false,
            'httpheader' => ['Accept: application/json', 'Content-Type: application/json'],
        ];

        return $this->send(sprintf('%s%s?%s', $this->base_url, $method, http_build_query($params)), $options);
    }

    /**
     * ping.
     *
     * @throws \Exception
     */
    public function ping(): bool
    {
        $timeout = 10;

        $options = [
            'fresh_connect' => true,
            'timeout' => $timeout,
            'connecttimeout' => $timeout,
            'followlocation' => true,
            'returntransfer' => true,
            'ssl_verifypeer' => false,
            'ssl_verifyhost' => false,
        ];

        return $this->send('/api/doc', $options, function (int $http_code): void {
            if (200 !== $http_code) {
                throw new \Exception('Carrier AWS API is not available');
            }
        });
    }
}
