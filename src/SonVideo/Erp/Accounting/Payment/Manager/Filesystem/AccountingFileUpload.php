<?php

namespace SonVideo\Erp\Accounting\Payment\Manager\Filesystem;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use SonVideo\Erp\Filesystem\Manager\File;
use Stringy\Stringy;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class AccountingFileUpload extends File
{
    /**
     * @throws FileNotFoundException
     * @throws FileExistsException
     */
    public function upload(UploadedFile $uploaded_file): string
    {
        return $this->write($uploaded_file, $this->getFormattedFilenameFrom());
    }

    protected function getFormattedFilenameFrom(): callable
    {
        return function (UploadedFile $uploaded_file): string {
            $file_extension = strtolower(sprintf('.%s', $this->getFileExtension($uploaded_file)));
            $given_filename = str_replace($file_extension, '', $uploaded_file->getClientOriginalName());

            return sprintf('%s%s', Stringy::create($given_filename)->slugify('_'), $file_extension);
        };
    }
}
