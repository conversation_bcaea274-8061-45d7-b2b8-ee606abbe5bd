<?php

namespace SonVideo\Erp\Manager;

use App\Client\AbstractCurlClient;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

/**
 * Class MonitoringManager.
 */
class MonitoringManager extends AbstractCurlClient implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /** Healthcheck URL */
    public const CHECK_URL = 'https://hc-ping.com/';

    /** Healthcheck status */
    public const CHECK_STATUS = [
        'end' => '',
        'start' => '/start',
        'fail' => '/fail',
    ];

    public function __construct()
    {
        $this->setBaseUrl(self::CHECK_URL);
    }

    /** pingMonitoring */
    public function pingMonitoring(string $check_uuid, string $status = 'end', string $message = ''): void
    {
        try {
            if (!array_key_exists($status, self::CHECK_STATUS)) {
                throw new \Exception('Status not handled');
            }

            $this->send(sprintf('%s%s', $check_uuid, self::CHECK_STATUS[$status]), [
                'POSTFIELDS' => ['body' => $message],
            ]);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), $e->getTrace());
        }
    }
}
