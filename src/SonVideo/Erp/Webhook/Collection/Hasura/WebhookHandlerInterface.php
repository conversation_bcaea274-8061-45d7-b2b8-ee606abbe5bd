<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Webhook\Collection\Hasura;

use App\Contract\Collection\CollectableInterface;

interface WebhookHandlerInterface extends CollectableInterface
{
    public function process(array $payload): array;
}
