<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;

final class ArticleMagicSearchRepository extends AbstractLegacyRepository
{
    public const FULL_INDEX_CONDITIONS = 'WHERE TRUE';

    public function fetchArticlesGenerator(string $conditions = self::FULL_INDEX_CONDITIONS): \Generator
    {
        $this->legacy_pdo->fetchAffected('SET SESSION group_concat_max_len = 1000000');

        $sql = <<<'MYSQL'
        SELECT
          a.id_produit AS article_id,
          p.reference AS sku,
          a.modele AS name,
          CONCAT(m.marque, ' ', a.modele) AS computed_name,
          a.description_courte AS short_description,
          a.description_panier AS basket_description,
          COALESCE(IF(a.date_embargo IS NOT NULL AND a.date_embargo > now(), a.date_embargo, NULL), apd.date_lance_a, a.date_lance_a, apd.date_creation, a.date_creation) AS reference_date,
          a.status AS status,
          CASE
            WHEN a.compose = 1
              THEN 'compose'
            WHEN p.reference LIKE 'DESTOCK%'
              OR p.reference LIKE 'DESCTOCK%'
              THEN 'destock'
            WHEN p.id_souscategorie = 118
              THEN 'lampe'
              ELSE 'article'
            END AS type,
          JSON_OBJECT(
            'id', m.id_marque,
            'name', m.marque
          ) AS brand,
          m.type_marque_maison AS group_brand,
          JSON_OBJECT(
            'id', p.V_id_categorie,
            'name', cat.categorie
          ) AS category,
          JSON_OBJECT(
            'id', p.id_souscategorie,
            'name', scat.souscategorie
          ) AS subcategory,
          SUBSTR(bc.article_url, 26) AS article_url,
          backOffice.FORMAT_image_path_without_cdn(COALESCE(bc.media_300_square_uri, bc.media_largest_uri)) AS image,
          JSON_OBJECT(
            'eans', CAST(CONCAT('["', GROUP_CONCAT(DISTINCT ean.ean SEPARATOR '","'), '"]') AS JSON),
            'code128', backOffice.PDT_code128C(p.id_produit)
          ) AS barcodes,
          JSON_OBJECT(
            'ecotax', a.prix_ecotaxe,
            'selling_price', a.prix_vente,
            'selling_price_generally_observed', a.prix_vente_generalement_constate,
            'supplier_price', CASE
                                WHEN a.compose = 0
                                  THEN backOffice.PDT_ART_px_achat(a.id_produit)
                                  ELSE backOffice.PDT_CMP_px_achat(a.id_produit)
                                END,
            'supplier_price_tariff', CASE
                                       WHEN a.compose = 0
                                         THEN a.prix_achat_tarif
                                         ELSE c.prix_achat_tarif
                                       END,
            'intragroup', backOffice.PDT_ART_px_vte_intragroupe(a.id_produit, 0.03),
            'margin_rate', backOffice.PDT_ART_taux_marque(a.id_produit),
            'margin_tax_excluded', p.V_marge,
            'last_scrapping_date', MAX(cp.crawled_at)
          ) AS prices,
          bc.unbasketable_reason AS unbasketable_reason,
          CASE
            WHEN a.compose = 0
              THEN a.V_delai_lvr
              ELSE backOffice.PDT_CMP_V_delai_lvr(a.id_produit)
            END AS delay,
          CASE
            WHEN a.compose = 0
              THEN backOffice.PDT_ART_qte_stock_expedition(a.id_produit)
              ELSE backOffice.PDT_CMP_qte_stock_expedition(a.id_produit)
            END AS stock,
          CASE
            WHEN wl.location_id IS NOT NULL
              THEN
              CAST(CONCAT('[',
                          GROUP_CONCAT(DISTINCT JSON_OBJECT(
                            'location_id', wl.location_id,
                            'location_code', wl.code,
                            'delivery_note_id', wpl.delivery_ticket_id,
                            'move_mission_id', wpl.move_mission_id,
                            'quantity', wpl.quantity,
                            'location_label', wl.label,
                            'area_label', wa.label,
                            'warehouse_id', bsd.id,
                            'warehouse_name', bsd.nom_depot
                                                ))
                , ']') AS JSON)
              ELSE null
            END AS stock_details,
          JSON_OBJECT(
            'substitute_of', CAST(CONCAT('["', GROUP_CONCAT(DISTINCT pr.reference SEPARATOR '","'), '"]') AS JSON),
            'declinations', CAST(CONCAT('["', GROUP_CONCAT(DISTINCT pd.reference SEPARATOR '","'), '"]') AS JSON),
            'bundles', CAST(CONCAT('["', GROUP_CONCAT(DISTINCT pc2.reference SEPARATOR '","'), '"]') AS JSON)
          ) AS related_products
          FROM
            backOffice.article a
              INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
              INNER JOIN backOffice.marque m ON a.id_marque = m.id_marque
              LEFT JOIN backOffice.batch_catalog bc ON a.id_produit = bc.article_id
              LEFT JOIN backOffice.BO_CTG_PDT_ART_ean ean ON ean.BO_CTG_PDT_ART_article_id = a.id_produit
              LEFT JOIN backOffice.CTG_TXN_categorie cat ON p.V_id_categorie = cat.id_categorie
              LEFT JOIN backOffice.CTG_TXN_souscategorie scat ON p.id_souscategorie = scat.id
              LEFT JOIN backOffice.BO_PDT_ART_V_compose c ON a.id_produit = c.id
              -- substitute_of joins
              LEFT JOIN backOffice.article_replacement ar ON ar.id_replaced_by = a.id_produit
              LEFT JOIN backOffice.produit pr ON pr.id_produit = ar.id_replaced
              -- declinations joins
              LEFT JOIN backOffice.batch_catalog bc2
                ON (bc2.common_content_id = bc.common_content_id AND bc2.article_id != a.id_produit)
              LEFT JOIN backOffice.produit pd ON pd.id_produit = bc2.article_id
              -- packaged_articles joins
              LEFT JOIN backOffice.produit_compose pc ON (a.compose = 1 AND pc.id_compose = a.id_produit)
              LEFT JOIN backOffice.produit pc2 ON pc2.id_produit = pc.id_produit
              -- stock in each warehouse
              LEFT JOIN backOffice.WMS_product_location wpl ON p.id_produit = wpl.product_id
              LEFT JOIN backOffice.WMS_location wl ON wl.location_id = wpl.location_id
              LEFT JOIN backOffice.WMS_area wa ON wa.area_id = wl.area_id
              LEFT JOIN backOffice.BO_STK_depot bsd ON bsd.id = wa.warehouse_id
            -- competitors
              LEFT JOIN backOffice.competitor_pricing cp ON cp.sku = p.reference
            -- destock
              LEFT JOIN backOffice.article apd on a.stock_a_id_produit = apd.id_produit,

            -- outer data for the cron
            (
              SELECT
                backOffice.GET_SYS_VAR_datetime('magic_search.incremental_update.start_at') AS start_at,
                backOffice.GET_SYS_VAR_datetime('magic_search.incremental_update.stop_at') AS stop_at
              ) tmp
              {conditions}
            GROUP BY a.id_produit
        ;
        MYSQL;

        return $this->legacy_pdo->iterateAssociative(
            strtr($sql, [
                '{conditions}' => $conditions,
            ])
        );
    }

    public static function getIncrementalUpdateConditions(): string
    {
        return <<<'MYSQL'
        WHERE a.date_creation BETWEEN tmp.start_at AND tmp.stop_at
            OR a.modif_date BETWEEN tmp.start_at AND tmp.stop_at
            OR p.derniere_actualisation BETWEEN tmp.start_at AND tmp.stop_at
        MYSQL;
    }
}
