<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity;

use App\Entity\AbstractEntity;

/**
 * Class CustomerEntity.
 *
 * @deprecated  use src/SonVideo/Erp/Customer/Entity/CustomerEntity.php instead.
 */
class CustomerEntity extends AbstractEntity
{
    public int $id_prospect;

    public ?string $identifiant = null;

    public \DateTimeInterface $date_creation;

    public ?\DateTimeInterface $date_modification = null;

    public ?string $prescripteur = null;

    public ?string $origine = null;

    public ?\DateTimeInterface $origine_date = null;

    public string $cnt_type;

    public string $cnt_email;

    public string $cnt_societe;

    public string $cnt_civilite;

    public string $cnt_nom;

    public string $cnt_prenom;

    public string $cnt_adresse;

    public string $cnt_code_postal;

    public string $cnt_ville;

    public int $cnt_id_pays;

    public string $cnt_telephone;

    public string $cnt_telephone_bureau;

    public string $cnt_mobile;

    public string $cnt_fax;

    public ?string $cnt_numero_tva = null;

    public string $cnt_lvr_type;

    public string $cnt_lvr_email;

    public string $cnt_lvr_societe;

    public string $cnt_lvr_civilite;

    public string $cnt_lvr_nom;

    public string $cnt_lvr_prenom;

    public string $cnt_lvr_adresse;

    public string $cnt_lvr_code_postal;

    public string $cnt_lvr_ville;

    public int $cnt_lvr_id_pays;

    public string $cnt_lvr_telephone;

    public string $cnt_lvr_telephone_bureau;

    public string $cnt_lvr_mobile;

    public string $cnt_lvr_fax;

    public ?string $cnt_lvr_numero_tva = null;

    public ?string $site_web = null;

    public bool $blacklist;

    public ?\DateTimeInterface $date_naissance = null;

    public ?int $profession_id = null;

    public bool $envoi_email;

    public string $email;

    public string $type;

    public ?string $societe = null;

    public string $civilite;

    public string $nom;

    public string $prenom;

    public bool $envoi_identifiants;

    public float $encours_interne;

    public float $encours_sfac;

    public ?int $id_mode_paiement = null;

    public ?string $classification = null;

    public bool $acceptation_relicat;

    public ?float $franco = null;

    public ?string $RIB = null;

    public ?string $nom_banque = null;

    public ?string $ville_banque = null;

    public ?string $BIC = null;

    public ?string $IBAN = null;

    public ?string $atradius = 'Pas soumis';

    public ?string $siren = null;

    public ?string $incoterm = null;

    public ?bool $is_premium = null;

    public ?string $passion = null;

    public ?string $installation = null;

    public ?string $style_musique = null;

    public ?string $genre_cinema = null;

    public ?string $musiques_preferees = null;

    public ?string $cinemas_preferes = null;

    public ?bool $npai = null;
}
