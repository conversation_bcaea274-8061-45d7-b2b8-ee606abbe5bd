<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity\Shipping;

use App\Entity\AbstractEntity;

/**
 * Class ShipmentTrackingEntity.
 */
class ShipmentTrackingEntity extends AbstractEntity
{
    public int $delivery_ticket_id;

    public int $order_id;

    public string $external_order_number;

    public string $carrier_name;

    public string $shipping_method_name;

    /** @var ShipmentTrackingParcelEntity[] */
    public array $parcels;

    public bool $is_manual_shipment;
}
