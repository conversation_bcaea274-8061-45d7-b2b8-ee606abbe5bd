<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext;

use App\Entity\AbstractEntity;

/**
 * Class ShipmentMethodEligibilityContextEntity.
 */
class ShipmentMethodEligibilityContextEntity extends AbstractEntity
{
    public int $shipment_method_id;

    public bool $is_express;

    public bool $is_store;

    public string $carrier_code;

    public float $cost;
}
