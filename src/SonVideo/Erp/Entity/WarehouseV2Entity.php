<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

class WarehouseV2Entity extends AbstractEntity
{
    public int $warehouse_id;

    public string $name;

    public ?string $address = null;

    public ?string $postal_code = null;

    public ?string $city = null;

    public ?int $country_id = null;

    public ?string $country_name = null;

    public ?string $country_code = null;

    public ?string $phone_number = null;

    public ?string $email_address = null;

    public ?int $manager_id = null;

    public ?string $description = null;

    public ?string $transfer_name = null;

    public ?string $basket_name = null;

    public ?string $basket_address = null;

    public ?string $basket_city = null;

    public ?string $google_map_link = null;

    public int $sort_order;

    public ?string $opened = null;

    public ?string $opened_days = null;

    public ?string $opened_hours = null;

    public bool $is_active;

    public bool $is_active_in_bo;

    public string $shorthand_name;

    public bool $use_auto_picking;

    public string $wms_code;

    /** @var array|JsonType|null */
    public ?array $allowed_inventory_types = [];

    public ?string $warehouse_barcode = null;

    public ?int $location_id = null;

    public ?string $location_code = null;

    public ?string $location_label = null;

    public ?int $shipment_method_id = null;

    /** @var WarehouseUserEntity[]|null */
    public ?array $users = [];

    /** @var array|JsonType|null */
    public ?array $business_hours = [];

    /** @var array|JsonType|null */
    public ?array $delivery_days = [];
}
