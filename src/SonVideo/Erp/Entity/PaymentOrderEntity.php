<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity;

use App\Entity\AbstractEntity;

class PaymentOrderEntity extends AbstractEntity
{
    public int $id;

    public int $id_commande;

    public int $id_unique;

    public int $id_paiement;

    public string $type;

    public \DateTimeInterface $creation_date;

    public ?string $creation_usr = null;

    public float $creation_montant;

    public ?string $creation_justificatif = null;

    public string $creation_origine;

    public ?\DateTimeInterface $acceptation_date = null;

    public ?string $acceptation_usr = null;

    public float $acceptation_montant;

    public ?string $acceptation_justificatif = null;

    public ?\DateTimeInterface $annulation_date = null;

    public ?string $annulation_usr = null;

    public float $annulation_montant;

    public ?\DateTimeInterface $demande_remise_date = null;

    public ?string $demande_remise_usr = null;

    public ?\DateTimeInterface $remise_date = null;

    public ?string $remise_usr = null;

    public float $remise_montant;

    public ?string $remise_justificatif = null;

    public ?int $remise_bon = null;

    public float $remise_taux;

    public ?\DateTimeInterface $impaye_date = null;

    public ?string $impaye_usr = null;

    public float $impaye_montant;

    public ?string $auto_statut = null;

    public ?string $auto_statut_detail = null;

    public ?string $auto_garantie = null;

    public ?string $auto_garantie_detail = null;

    public ?string $pays_ip = null;

    public ?string $pays_origine = null;

    public ?int $store_id = null;
}
