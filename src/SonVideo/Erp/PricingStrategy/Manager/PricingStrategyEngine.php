<?php

namespace SonVideo\Erp\PricingStrategy\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Database\Orm\MysqlErp\Repository\CompetitorPricingRepository;
use App\Database\Orm\MysqlErp\Repository\Entity\CompetitorPricing;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Dto\SalesChannelComputedPriceDto;
use SonVideo\Erp\Article\Dto\SalesChannelPriceContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\TriggeredPriceUpdateContextDto;
use SonVideo\Erp\Article\Entity\ArticleSalesChannelEntity;
use SonVideo\Erp\Article\Manager\ArticleMarginCalculator;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleSalesChannelRepository;
use SonVideo\Erp\Article\Traits\ArticleSalesChannelUpdateSellingPriceTrait;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyEntity;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyProductEntity;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyProductPriceEntity;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyRepository;
use SonVideo\Erp\Referential\PricingStrategyStatus;
use SonVideo\Erp\Referential\Product;
use SonVideo\Erp\Referential\SalesChannel;
use SonVideo\Erp\SalesChannel\Exception\MarginValidationException;
use SonVideo\Erp\SalesChannel\Mysql\Repository\SalesChannelRepository;
use SonVideo\Erp\User\Entity\UserEntity;

class PricingStrategyEngine implements LoggerAwareInterface, SerializerAwareInterface
{
    use ArticleSalesChannelUpdateSellingPriceTrait;
    use LoggerAwareTrait;
    use SerializerAwareTrait;

    private PricingStrategyRepository $pricing_strategy_repository;
    private PricingStrategyProductManager $pricing_strategy_product_manager;
    private ArticleMarginCalculator $article_margin_calculator;
    private SalesChannelRepository $sales_channel_repository;
    private QueryBuilder $query_builder;
    private ArticleSalesChannelRepository $article_sales_channel_repository;
    private AccountQueryRepository $account_query_repository;
    private CompetitorPricingRepository $competitor_pricing_repository;
    private ?\DateTimeInterface $base_date = null;
    private ArticleRepository $article_repository;

    public function __construct(
        PricingStrategyRepository $pricing_strategy_repository,
        PricingStrategyProductManager $pricing_strategy_config_product_manager,
        ArticleMarginCalculator $article_margin_calculator,
        SalesChannelRepository $sales_channel_repository,
        QueryBuilder $query_builder,
        ArticleSalesChannelRepository $article_sales_channel_repository,
        AccountQueryRepository $account_query_repository,
        ArticleSalesChannelManager $article_sales_channel_manager,
        CompetitorPricingRepository $competitor_pricing_repository,
        ArticleRepository $article_repository
    ) {
        $this->pricing_strategy_repository = $pricing_strategy_repository;
        $this->pricing_strategy_product_manager = $pricing_strategy_config_product_manager;
        $this->article_margin_calculator = $article_margin_calculator;
        $this->sales_channel_repository = $sales_channel_repository;
        $this->query_builder = $query_builder;
        $this->article_sales_channel_repository = $article_sales_channel_repository;
        $this->account_query_repository = $account_query_repository;
        $this->article_sales_channel_manager = $article_sales_channel_manager;
        $this->competitor_pricing_repository = $competitor_pricing_repository;
        $this->article_repository = $article_repository;
    }

    public function setBaseDate(\DateTimeInterface $base_date): void
    {
        $this->base_date = $base_date;
    }

    public function getBaseDate(): \DateTimeInterface
    {
        if (!$this->base_date instanceof \DateTimeInterface) {
            $this->base_date = new \DateTime();
        }

        return $this->base_date;
    }

    /**
     * @return PricingStrategyProductEntity[]
     *
     * @throws InternalServerErrorException
     * @throws MarginValidationException
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function run(int $pricing_strategy_id, bool $dry_run = true): array
    {
        $strategy = $this->pricing_strategy_repository->fetchById($pricing_strategy_id);
        $products = $this->pricing_strategy_product_manager->getPricingStrategyProducts($pricing_strategy_id);

        if ([] === $strategy->competitors || [] === $strategy->sales_channels) {
            return [];
        }

        $min_margin_rate = $this->getMinMarginRate($strategy);
        $increment_amount = $this->getIncrementAmount($strategy);
        $sales_channels = $this->getSalesChannels(array_column($strategy->sales_channels, 'sales_channel_id'));

        /*
         * For each product in the strategy :
         * - search for the lowest competitor pricing
         *      if no price is found we do not update ours
         * - align price with an increment defined in the strategy
         * - if our new margin rate is lower than the minimum accepted
         *      -> we compute the new price according to the minimum margin of the strategy
         * - currently the prices are the same for all the sales channels
         */
        $this->computeProductsSellingPrice(
            $products,
            $increment_amount,
            $min_margin_rate,
            array_column($strategy->competitors, 'competitor_code'),
            $sales_channels,
            $strategy
        );

        if (!$dry_run && PricingStrategyStatus::ACTIVATED === $strategy->activation_status) {
            $this->updateProductsPrices($products);
        }

        return $products;
    }

    /**
     * @param PricingStrategyProductEntity[] $products
     *
     * @throws InternalServerErrorException
     * @throws MarginValidationException
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    private function updateProductsPrices(array $products): void
    {
        $user = $this->account_query_repository->getUser(UserEntity::SYSTEM_USERNAME);
        foreach ($products as $product) {
            foreach ($product->new_prices as $new_price) {
                $this->updateArticleSalesChannelSellingPrice($product->article_id, $new_price, $user);
                // context is not needed anymore and might incur response truncation
                $new_price->price_update_context = null;
            }
        }
    }

    /** @param PricingStrategyProductEntity[] $products */
    private function computeProductsSellingPrice(
        array &$products,
        float $increment_amount,
        float $min_margin_rate,
        array $competitor_codes,
        array $sales_channels,
        PricingStrategyEntity $strategy
    ): void {
        foreach ($products as $i => $product) {
            $lowest_competitor = $this->competitor_pricing_repository->findLowestCompetitorPricing(
                $product->article_id,
                $competitor_codes
            );

            if (!$lowest_competitor instanceof CompetitorPricing) {
                unset($products[$i]);
                continue;
            }

            $product->lowest_competitor = $lowest_competitor;

            $selling_price_context_dto = $this->createMarginDto($product);

            $new_prices = [];
            $force_svd_price = SalesChannel::SON_VIDEO_CODE === $lowest_competitor->competitor_code;
            $lowest_competitor_price =
                $lowest_competitor->selling_price_with_taxes - ($force_svd_price ? 0 : $increment_amount);

            foreach ($sales_channels as $sales_channel) {
                $sales_channel_product = $this->article_sales_channel_repository->findById(
                    $product->article_id,
                    $sales_channel['sales_channel_id']
                );
                if (!$sales_channel_product instanceof ArticleSalesChannelEntity) {
                    // product is not sold in the sale channel
                    continue;
                }

                $is_on_sale = (bool) $this->article_repository->getOneById($product->article_id, ['is_on_sale'])[
                    'is_on_sale'
                ];

                if ($is_on_sale && SalesChannel::SON_VIDEO === $sales_channel['sales_channel_id']) {
                    continue;
                }

                $selling_price_context_dto->sales_channel_commission_fee =
                    $sales_channel['average_commission_rate'] ?? 0;
                $selling_price_context_dto->sales_channel_id = $sales_channel['sales_channel_id'];
                $selling_price_context_dto->selling_price = $sales_channel_product->selling_price;

                $old_selling_price_dto = $this->article_margin_calculator->computeMargin($selling_price_context_dto);

                $selling_price_context_dto->selling_price = $lowest_competitor_price;

                $new_selling_price_dto = $this->article_margin_calculator->computeMargin($selling_price_context_dto);

                $margin_rate_if_cheapest = $new_selling_price_dto->margin_rate;
                $selling_price_if_cheapest = $new_selling_price_dto->selling_price_tax_included;

                if ($margin_rate_if_cheapest < $min_margin_rate && !$force_svd_price) {
                    $new_selling_price_dto = $this->getFallbackSellingPrice(
                        $product,
                        $competitor_codes,
                        $min_margin_rate,
                        $selling_price_context_dto
                    );
                }

                if ($new_selling_price_dto->selling_price_tax_included > $product->pvgc) {
                    continue;
                }

                $price = new PricingStrategyProductPriceEntity();
                $price->sales_channel = $sales_channel;
                $price->current_selling_price_tax_included = $old_selling_price_dto->selling_price_tax_included;
                $price->selling_price_tax_excluded = $new_selling_price_dto->selling_price_tax_excluded;
                $price->selling_price_tax_included = $new_selling_price_dto->selling_price_tax_included;
                $price->selling_price_if_cheapest = $selling_price_if_cheapest;
                $price->current_margin_rate = $old_selling_price_dto->margin_rate;
                $price->margin_rate = $new_selling_price_dto->margin_rate;
                $price->margin_rate_if_cheapest = $margin_rate_if_cheapest;
                $price->margin = $new_selling_price_dto->margin;

                $price_update_context = new TriggeredPriceUpdateContextDto();
                $price_update_context->competitor_pricing = $lowest_competitor;
                $price_update_context->pricing_strategy = $strategy;

                if ($force_svd_price) {
                    $price_update_context->warnings[] = sprintf(
                        'Application du prix SONVIDEO (%.2F€) au produit %d pour le canal %s',
                        $lowest_competitor->selling_price_with_taxes,
                        $product->article_id,
                        $sales_channel['label']
                    );
                }

                $price->price_update_context = $price_update_context;
                $new_prices[] = $price;
            }

            if ([] === $new_prices) {
                unset($products[$i]);
            }

            $product->new_prices = $new_prices;
        }
    }

    private function createMarginDto(PricingStrategyProductEntity $product): SalesChannelPriceContextDto
    {
        $dto = new SalesChannelPriceContextDto();

        $dto->pvgc = $product->pvgc;
        $dto->ecotax = $product->ecotax;
        $dto->sorecop = $product->sorecop;
        $dto->purchase_price_tax_excluded = $product->purchase_price;
        $dto->weighted_purchase_price_tax_excluded = $product->weighted_cost_tax_excluded;
        $dto->stock = $product->stock;
        $dto->unconditional_discount = $product->unconditional_discount;
        $dto->vat_rate = Product::VAT_BASE;
        $dto->promo_budget_amount = $product->promo_budget_amount;

        return $dto;
    }

    public function getMinMarginRate(PricingStrategyEntity $strategy): float
    {
        if (
            $this->getBaseDate()
                ->modify('+4 hours')
                ->format('N') >= 6
        ) {
            return $strategy->weekend_min_margin_rate / 100;
        }

        return $strategy->weekdays_min_margin_rate / 100;
    }

    public function getIncrementAmount(PricingStrategyEntity $strategy): float
    {
        if (
            $this->getBaseDate()
                ->modify('+4 hours')
                ->format('N') >= 6
        ) {
            return $strategy->weekend_increment_amount;
        }

        return $strategy->weekdays_increment_amount;
    }

    private function getSalesChannels(array $sales_channels_id): array
    {
        $this->query_builder
            ->setWhere(
                [
                    'sales_channel_id' => [
                        '_in' => $sales_channels_id,
                    ],
                ],
                SalesChannelRepository::COLUMNS_MAPPING
            )
            ->setOrderBy();

        return $this->sales_channel_repository->findAllPaginated($this->query_builder)->getResults();
    }

    private function getFallbackSellingPrice(
        PricingStrategyProductEntity $product,
        array $competitor_codes,
        float $min_margin_rate,
        SalesChannelPriceContextDto $margin_computing_dto
    ): SalesChannelComputedPriceDto {
        $product->is_margin_rate_to_low = true;

        if (in_array(SalesChannel::SON_VIDEO_CODE, $competitor_codes)) {
            $svd_competitor = $this->competitor_pricing_repository->findLowestCompetitorPricing($product->article_id, [
                SalesChannel::SON_VIDEO_CODE,
            ]);
            if ($svd_competitor instanceof CompetitorPricing) {
                $margin_computing_dto->selling_price = $svd_competitor->selling_price_with_taxes;
                $margin_result_dto = $this->article_margin_calculator->computeMargin($margin_computing_dto);
                if ($margin_result_dto->margin_rate > $min_margin_rate) {
                    $margin_computing_dto->selling_price = $this->article_margin_calculator->computeSellingPriceFromMarginRate(
                        $margin_computing_dto,
                        $min_margin_rate
                    );
                }

                return $this->article_margin_calculator->computeMargin($margin_computing_dto);
            }
        }

        $margin_computing_dto->selling_price = $this->article_margin_calculator->computeSellingPriceFromMarginRate(
            $margin_computing_dto,
            $min_margin_rate
        );

        return $this->article_margin_calculator->computeMargin($margin_computing_dto);
    }
}
