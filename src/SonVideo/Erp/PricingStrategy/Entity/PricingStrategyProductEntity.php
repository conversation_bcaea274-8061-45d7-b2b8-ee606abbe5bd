<?php

namespace SonVideo\Erp\PricingStrategy\Entity;

use App\Database\Orm\MysqlErp\Repository\Entity\CompetitorPricing;
use App\Entity\AbstractEntity;

class PricingStrategyProductEntity extends AbstractEntity
{
    public int $pricing_strategy_id;

    public string $sku;

    public int $article_id;

    public string $image;

    public string $article_name;

    public float $selling_price;

    public int $stock;

    public string $category;

    public ?float $margin = null;

    public ?float $margin_rate = null;

    public float $margin_tax_excluded;

    public ?\DateTimeInterface $last_scrapping_date = null;

    public float $purchase_price;

    public float $sorecop;

    public float $ecotax;

    public float $promo_budget_amount;

    public float $unconditional_discount;

    public float $weighted_cost_tax_excluded;

    public string $status;

    public ?int $delay = null;

    public float $pvgc;

    public ?CompetitorPricing $lowest_competitor = null;

    /** @var PricingStrategyProductPriceEntity[] */
    public array $new_prices = [];

    public bool $is_margin_rate_to_low = false;
}
