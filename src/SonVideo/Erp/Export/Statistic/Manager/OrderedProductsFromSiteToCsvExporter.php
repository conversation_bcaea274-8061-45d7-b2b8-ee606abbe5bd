<?php

namespace SonVideo\Erp\Export\Statistic\Manager;

use App\Database\PgDataWarehouse\DataSchema\CustomerOrderInitialLineModel;
use Aura\Sql\Exception\CannotBindValue;
use SonVideo\Erp\Brand\Mysql\Repository\BrandRepository;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;

final class OrderedProductsFromSiteToCsvExporter extends AbstractCvsExporter
{
    /** {@inheritdoc} */
    protected const INTERVAL = 24;
    /** {@inheritdoc} */
    protected const EXPORT_NAME = 'Export_produits_commandes_site_24mois.csv';

    protected const EXPORT_HEADERS = [
        'date' => 'Date',
        'year' => 'Année',
        'month' => 'Mois',
        'customer_order_id' => 'N° de commande',
        'order_status' => 'Statut de commande',
        'reference' => 'SKU',
        'product_type' => 'Type de produit',
        'brand_name' => 'Marque maison',
        'brand' => 'Marque',
        'domain' => 'Domaine',
        'category' => 'Catégorie',
        'subcategory' => 'Sous catégorie',
        'supplier' => 'Fournisseur',
        'origin' => 'Origine',
        'country' => 'Pays',
        'postal_code' => 'Code postal',
        'quantity' => 'Quantité',
        'total_gross_excl_tax' => 'Total brut HT',
        'total_net_excl_tax' => 'Total net HT',
        'total_purchase_cost' => 'Total achat HT',
        'total_discount_excl_tax' => 'Total remise HT',
        'total_guarantee_excl_tax' => 'Total GLD HT',
        'total_margin' => 'Marge',
        'model' => 'Modèle',
        'is_btob' => 'B2B',
        'is_internal' => 'Intragroupe',
        'source' => 'Source',
        'source_group' => 'Groupe source',
        'sales_location' => 'Lieu de vente',
        'created_by' => 'Vendeur',
        'is_destock' => 'Destock',
        'promo_code' => 'Code promo',
    ];

    protected const FLOAT_COLUMNS = [
        'total_purchase_cost',
        'total_gross_excl_tax',
        'total_discount_excl_tax',
        'total_net_excl_tax',
        'total_guarantee_excl_tax',
        'total_margin',
    ];

    private CustomerOrderInitialLineModel $customer_order_initial_line_repository;

    /** @var BrandRepository */
    private $group_brands;

    public function __construct(
        CustomerOrderInitialLineModel $customer_order_initial_line_repository,
        ExportedFile $exported_file,
        BrandRepository $brand_repository
    ) {
        parent::__construct($exported_file);
        $this->customer_order_initial_line_repository = $customer_order_initial_line_repository;

        $this->prepareGroupBrands($brand_repository);
    }

    /** @throws CannotBindValue */
    private function prepareGroupBrands(BrandRepository $brand_repository): void
    {
        $group_brands = $brand_repository->getGroupBrands();
        $this->group_brands = array_combine(
            array_column($group_brands, 'brand_name'),
            array_column($group_brands, 'group_brand')
        );
    }

    /**
     * Format csv columns.
     *
     * @return array<string, mixed>
     **/
    protected function formatFields(array $record): array
    {
        $record['brand_name'] = $this->group_brands[$record['brand']] ?? null;

        return parent::formatFields($record);
    }

    protected function getRecords(string $days_since): \Generator
    {
        return $this->customer_order_initial_line_repository->getExportOrderedProductsFromSite($days_since);
    }
}
