<?php

namespace SonVideo\Erp\Export\Statistic\Manager;

use App\Database\PgDataWarehouse\DataSchema\SellInLineModel;
use Aura\Sql\Exception\CannotBindValue;
use SonVideo\Erp\Brand\Mysql\Repository\BrandRepository;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;

final class SellInToCsvExporter extends AbstractCvsExporter
{
    /** {@inheritdoc} */
    protected const INTERVAL = 24;
    /** {@inheritdoc} */
    protected const EXPORT_NAME = 'Export_produits_entree_stock_24mois.csv';

    /** @var string[] */
    protected const EXPORT_HEADERS = [
        'delivery_date' => 'Date entrée stock',
        'ordered_date' => 'Date de commande',
        'supplier_order_id' => 'N° de commande fournisseur',
        'reference' => 'SKU',
        'ean_code' => 'EAN',
        'model' => 'Modèle',
        'brand_name' => 'Marque maison',
        'brand' => 'Marque',
        'domain' => 'Domaine',
        'category' => 'Catégorie',
        'subcategory' => 'Sous catégorie',
        'supplier' => 'Fournisseur',
        'quantity' => 'Quantité',
        'purchase_cost' => 'Prix achat HT',
        'warehouse' => 'Entrepôt',
        'weight' => 'Poids en gramme',
    ];

    /** @var string[] */
    protected const FLOAT_COLUMNS = ['purchase_cost'];

    private SellInLineModel $sell_in_line;

    /** @var BrandRepository */
    private $group_brands;

    public function __construct(
        SellInLineModel $sell_in_line,
        ExportedFile $exported_file,
        BrandRepository $brand_repository
    ) {
        parent::__construct($exported_file);
        $this->sell_in_line = $sell_in_line;

        $this->prepareGroupBrands($brand_repository);
    }

    /** Hydrate list of group brands.
     *
     * @throws CannotBindValue
     */
    private function prepareGroupBrands(BrandRepository $brand_repository): void
    {
        $group_brands = $brand_repository->getGroupBrands();
        $this->group_brands = array_combine(
            array_column($group_brands, 'brand_name'),
            array_column($group_brands, 'group_brand')
        );
    }

    /**
     * Format csv columns.
     *
     * @return array<string, mixed>
     **/
    protected function formatFields(array $record): array
    {
        $record['brand_name'] = $this->group_brands[$record['brand']] ?? null;

        return parent::formatFields($record);
    }

    protected function getRecords(string $days_since): \Generator
    {
        return $this->sell_in_line->getExportSellInGenerator($days_since);
    }
}
