<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Export\Cilo\Manager;

use App\Contract\EntityInterface;
use SonVideo\Erp\Repository\Marketplace\Cilo\CiloMarketplaceReadRepository;
use Symfony\Component\Serializer\Encoder\XmlEncoder;

/**
 * Class Catalog.
 */
class Catalog
{
    public const FILENAME = 'full_catalog.xml';

    public const XML_FILE_OPTIONS = [
        XmlEncoder::ROOT_NODE_NAME => 'offers_update',
        XmlEncoder::ENCODING => 'ISO-8859-1',
        XmlEncoder::FORMAT_OUTPUT => true,
    ];

    /** @var array */
    protected $formatted_products = [];

    protected CiloMarketplaceReadRepository $cilo_marketplace_read_repository;

    /** Catalog constructor. */
    public function __construct(CiloMarketplaceReadRepository $cilo_marketplace_read_repository)
    {
        $this->cilo_marketplace_read_repository = $cilo_marketplace_read_repository;
    }

    /** load */
    public function load(): Catalog
    {
        // Get product entities from the cilo product repository
        // No need to log here as SQL queries are already logged automatically
        $products = $this->cilo_marketplace_read_repository->fetchAllForCatalog();

        /**
         * Prepare data to reflect the target XML file row.
         *
         * @var EntityInterface $product
         */
        foreach ($products as $product) {
            $data = $product->toArray();

            /**
             * Custom mapping for the sku
             * <offer_reference type=SellerSku>SKU</offer_reference>
             * The plus merge the new key and $data, and put the new key as first element.
             */
            $data =
                [
                    'offer_reference' => [
                        '@type' => 'SellerSku',
                        '#' => $data['sku'],
                    ],
                ] + $data;

            // Remove unused keys
            unset($data['sku']);

            $this->formatted_products[] = $data;
        }

        return $this;
    }

    /** Returns the catalog as an XML string */
    public function exportAsXml(): string
    {
        if ([] === $this->formatted_products) {
            throw new \UnexpectedValueException('There are no formatted product to put in the catalog.');
        }

        $encoder = new XmlEncoder(static::XML_FILE_OPTIONS);

        /**
         * Create nodes to append to $parentNode based on the $key of this array
         * Produces <xml><item>0</item><item>1</item></xml>
         * From ["item" => [0,1]];.
         *
         * @see XmlEncoder
         */
        $result = $encoder->encode(['offer' => $this->formatted_products], 'xml');

        if (false === $result) {
            throw new \UnexpectedValueException('Failed to encode our products catalog in XML.');
        }

        return $result;
    }

    /**
     * Write the xml in the local filesystem.
     *
     * @return string Path of the created file
     */
    public function createLocalXml(): string
    {
        $xml = $this->exportAsXml();

        $file_path = sprintf('%s/%s', sys_get_temp_dir(), static::FILENAME);
        if (false === file_put_contents($file_path, $xml)) {
            throw new \UnexpectedValueException('Failed to write XML local file.');
        }

        return $file_path;
    }
}
