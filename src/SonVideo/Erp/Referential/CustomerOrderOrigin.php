<?php

namespace SonVideo\Erp\Referential;

final class CustomerOrderOrigin
{
    use ReferentialTrait;

    public const AMAZON_DE = 'amazon.de';
    public const AMAZON_ES = 'amazon.es';
    public const AMAZON_FR = 'amazon.fr';
    public const AMAZON_IT = 'amazon.it';
    public const BACKOFFICE = 'backoffice.sonvideopro.com';
    public const BOULANGER = 'boulanger.com';
    public const CDISCOUNT = 'cdiscount.com';
    public const CILO = 'cilo.dk';
    public const CULTURA = 'cultura.com';
    public const DARTY = 'darty.com';
    public const EASY_LOUNGE = 'ecranlounge.com';
    public const EBAY = 'ebay.fr';
    public const FNAC = 'fnac.fr';
    public const LA_REDOUTE = 'redoute';
    public const NUMERICABLE = 'numericable';
    public const MOINS_CHER_2X = '2xmoinscher.com';
    public const PARNASSE = 'parnasse';
    public const PIXMANIA = 'pixmania.com';
    public const PRICEMINISTER = 'priceminister.com';
    public const RUE_DU_COMMERCE = 'rueducommerce.fr';
    public const SITE = 'son-video.com';
    public const SITE_MOBILE = 'site_mobile';
}
