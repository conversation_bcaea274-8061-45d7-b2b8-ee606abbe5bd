<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Referential\Payment;

final class PaymentId
{
    public const AMERICAN_EXPRESS_OGONE = 60;
    public const BANCONTACT = 110;
    public const CILO_PAYMENT = 92;
    public const CREDIT_CARD_OGONE = 59;
    public const CREDIT_NOTE_WITH_PRIOR_PAYMENT = 17;
    public const EASYLOUNGE = 74;
    public const FLOA = 109;
    public const FULLCB3X = 90;
    public const FULLCB4X = 91;
    public const INGENICO_ELECTRONIC_PAYMENT_TERMINAL = 11;
    public const INGENICO_ELECTRONIC_PAYMENT_TERMINAL_AMX = 83;
    public const INGENICO_ELECTRONIC_PAYMENT_TERMINAL_PNF = 82;
    public const PAYPAL = 68;
    public const PRESTO = 71;
    public const RETAIL_CETELEM = 93;
    public const SVD_GIFT_CARD = 49;
    public const WORLDLINE_GROUPED_CREDIT_CARD_ID = 112;
}
