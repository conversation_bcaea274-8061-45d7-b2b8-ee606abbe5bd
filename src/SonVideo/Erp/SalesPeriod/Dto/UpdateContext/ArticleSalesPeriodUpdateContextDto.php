<?php

namespace SonVideo\Erp\SalesPeriod\Dto\UpdateContext;

use App\Validator\Constraint as CustomAssert;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Contract\ArticleUpdateContextDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticleSalesPeriodUpdateContextDto implements ArticleUpdateContextDtoInterface
{
    /**
     * @OA\Property(description="Id de la solde auquel appliquer la modification")
     *
     * @Assert\NotNull()
     */
    public int $article_sale_id;

    /**
     * @OA\Property(description="Id article lié à la modification")
     *
     * @Assert\NotNull()
     */
    public int $article_id;

    /**
     * @OA\Property(description="Si la solde est active ou non")
     *
     * @Assert\NotNull()
     */
    public bool $is_active;

    /**
     * @OA\Property(description="Prix de vente pendant la période de solde")
     *
     * @Assert\NotNull()
     * @Assert\Positive()
     * @CustomAssert\ArticleSalesPeriodPrice()
     */
    public float $selling_price;
}
