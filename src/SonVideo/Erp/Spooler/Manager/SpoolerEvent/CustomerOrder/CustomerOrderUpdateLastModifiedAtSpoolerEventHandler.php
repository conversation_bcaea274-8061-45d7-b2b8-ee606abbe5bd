<?php

namespace SonVideo\Erp\Spooler\Manager\SpoolerEvent\CustomerOrder;

use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicReadRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Cms\Manager\CustomerOrderForCmsManager;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use SonVideo\Erp\Referential\SpoolerEventAction;
use SonVideo\Erp\Spooler\Contract\SpoolerEventHandlerInterface;
use SonVideo\Erp\Spooler\Entity\SpoolerEvent;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerReadRepository;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerWriteRepository;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use SonVideo\Synapps\Client\Message;

class CustomerOrderUpdateLastModifiedAtSpoolerEventHandler implements SpoolerEventHandlerInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    private SpoolerReadRepository $spooler_read_repository;
    private CustomerOrderForCmsManager $customer_order_for_cms_manager;
    private SpoolerWriteRepository $spooler_write_repository;
    private SynappsNotifier $synapps_notifier;
    private SynchronizableTopicReadRepository $synchronizable_topic_repository;

    private int $handled_events = 0;

    public function __construct(
        SpoolerReadRepository $spooler_read_repository,
        CustomerOrderForCmsManager $customer_order_for_cms_manager,
        SpoolerWriteRepository $spooler_write_repository,
        SynappsNotifier $synapps_notifier,
        SynchronizableTopicReadRepository $synchronizable_topic_repository
    ) {
        $this->spooler_read_repository = $spooler_read_repository;
        $this->customer_order_for_cms_manager = $customer_order_for_cms_manager;
        $this->spooler_write_repository = $spooler_write_repository;
        $this->synapps_notifier = $synapps_notifier;
        $this->synchronizable_topic_repository = $synchronizable_topic_repository;
    }

    public function canHandle(string $key): bool
    {
        return SpoolerEventAction::CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT === $key;
    }

    public function handle(): void
    {
        $this->logger->warning(
            sprintf('Event "%s" : START', SpoolerEventAction::CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT)
        );

        $spooler_events = $this->spooler_read_repository->fetchQueuedEvents(
            [SpoolerEventAction::CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT],
            1000
        );

        $total_events = count($spooler_events);
        $this->logger->notice(sprintf('%d event(s) to dequeue', $total_events));

        foreach ($spooler_events as $idx => $spooler_event) {
            $this->logger->notice(
                sprintf('Event %s/%s : Customer order %s', $idx + 1, $total_events, $spooler_event->target)
            );

            $this->handleLine($spooler_event);
        }

        if ($total_events > 0) {
            $this->logger->notice(
                sprintf('%d/%d event(s) were handled successfully', $this->handled_events, $total_events)
            );
        }

        $this->logger->warning(sprintf('Event "%s" : END', SpoolerEventAction::CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT));
        $this->logger->info('------------------------------------------------------------------');
    }

    private function handleLine(SpoolerEvent $spooler_event): void
    {
        try {
            $this->logger->warning(
                sprintf('Handler for "%s" not active yet', SpoolerEventAction::CUSTOMER_ORDER_UPDATE_LAST_MODIFIED_AT)
            );
            // This one handles its own logs in its own try/catch block
            $this->customer_order_for_cms_manager->touchAndPush((int) $spooler_event->target);

            // Same here, we do not prevent de-queuing if the synapps notification fails
            $this->notify($spooler_event);

            // Same here, we do not prevent de-queuing if the insert fails
            $this->synchronizeInDataWarehouse($spooler_event);

            $this->spooler_write_repository->deleteEvent($spooler_event);
            $this->logger->info('Event dequeued successfully');

            ++$this->handled_events;
        } catch (\Exception $exception) {
            $wrapped_exception = new \Exception(
                sprintf(
                    'Failed to dequeue event "%s" with target "%s"',
                    $spooler_event->action,
                    $spooler_event->target
                ),
                0,
                $exception
            );
            $this->logger->error($exception->getMessage(), [
                'exception' => $wrapped_exception,
            ]);
        }
    }

    private function notify(SpoolerEvent $spooler_event): void
    {
        try {
            $synapps_message = new Message('customer_order.erp.upsert', [
                'customer_order_id' => (int) $spooler_event->target,
            ]);
            $this->synapps_notifier->notify($synapps_message->getSubject(), $synapps_message->getPayload());
            $this->logger->info('Customer order sent to Synapps successfully');
        } catch (\Exception $exception) {
            $this->logger->warning('Event not sent to synapps successfully', ['exception' => $exception]);
        }
    }

    private function synchronizeInDataWarehouse(SpoolerEvent $spooler_event): void
    {
        try {
            $this->synchronizable_topic_repository->upsert([
                'target' => (int) $spooler_event->target,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'content' => ['customer_order_id' => (int) $spooler_event->target],
            ]);
        } catch (\Exception $exception) {
            $this->logger->warning('Customer order not marked for synchronization in data warehouse', [
                'exception' => $exception,
            ]);
        }
    }
}
