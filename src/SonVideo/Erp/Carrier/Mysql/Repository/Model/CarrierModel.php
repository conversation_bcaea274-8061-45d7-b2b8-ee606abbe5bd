<?php

namespace SonVideo\Erp\Carrier\Mysql\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * @ORM\Model(name="backOffice.transporteur", engine="mysql")
 */
class CarrierModel
{
    /** @ORM\Column(name="t.id_transporteur") */
    public int $carrier_id;

    /** @ORM\Column(name="t.code") */
    public string $code;

    /** @ORM\Column(name="t.transporteur") */
    public string $name;

    /** @ORM\Column(name="t.liste") */
    public string $show_in_list;

    /** @ORM\Column(name="t.is_expressiste") */
    public bool $is_express;

    /** @ORM\Column(name="t.ordre_picking") */
    public int $picking_order;

    /** @ORM\Column(name="t.bl_max") */
    public int $delivery_note_max_threshold;

    /** @ORM\Column(name="t.description") */
    public string $description;

    /** @ORM\Column(name="t.coordonnees") */
    public string $coordinates;

    /** @ORM\Column(name="t.zone") */
    public string $delivery_zone;

    /** @ORM\Column(name="t.poids_min") */
    public float $min_weight;

    /** @ORM\Column(name="t.poids_max") */
    public float $max_weight;

    /** @ORM\Column(name="t.url_tracking") */
    public ?string $url_tracking = null;

    /** @ORM\Column(name="t.commentaire") */
    public ?string $comment = null;
}
