<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Mysql\Repository;

use App\Database\ConnectionProvider\AbstractMysqlErpPagerProvider;
use App\Sql\Helper\Pager;
use SonVideo\Erp\Carrier\Entity\Carrier;
use SonVideo\Erp\Carrier\Mysql\Repository\Model\CarrierModel;
use SonVideo\Orm\Entity\PaginatedCollectionRequest;

final class CarrierReadRepository extends AbstractMysqlErpPagerProvider
{
    public function findAllPaginated(PaginatedCollectionRequest $paginated_collection_request): Pager
    {
        $subquery = <<<'MYSQL'
        SELECT
            t.id_transporteur AS carrier_id,
            t.code AS code,
            t.transporteur AS name,
            t.liste AS show_in_list,
            t.is_expressiste AS is_express,
            t.ordre_picking AS picking_order,
            t.bl_max AS delivery_note_max_threshold,
            t.description AS description,
            t.coordonnees AS coordinates,
            t.zone AS delivery_zone,
            t.poids_min AS min_weight,
            t.poids_max AS max_weight,
            t.url_tracking AS url_tracking,
            t.commentaire AS comment
            FROM backOffice.transporteur t
        WHERE {conditions}
        MYSQL;

        return $this->paginateWithModel(
            self::wrapSubQuery($subquery),
            $paginated_collection_request,
            CarrierModel::class,
            Carrier::class
        );
    }
}
