<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Dto\ShipmentMethod;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class EligibilityEnvelopeItemDto
{
    /**
     * @OA\Property(example="BRANDMODEL")
     *
     * @Assert\NotBlank()
     * @Assert\Type("string")
     */
    public string $sku;

    /**
     * @OA\Property(example=2)
     *
     * @Assert\NotBlank()
     * @Assert\GreaterThan(0)
     * @Assert\Type("integer")
     */
    public int $quantity;

    /**
     * @OA\Property(example=99.99)
     *
     * @Assert\NotEqualTo("")
     * @Assert\Type("numeric")
     */
    public float $price_vat_excluded;

    /**
     * @OA\Property(example=99.99)
     *
     * @Assert\NotEqualTo("")
     * @Assert\Type("numeric")
     */
    public float $price;

    /**
     * Should be price * quantity.
     *
     * @OA\Property(example=199.98)
     *
     * @Assert\NotEqualTo("")
     * @Assert\Type("numeric")
     */
    public float $total_price;
}
