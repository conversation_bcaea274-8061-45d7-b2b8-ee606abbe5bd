<?php

namespace SonVideo\Erp\Carrier\Dto\ShipmentMethod;

use OpenApi\Annotations as OA;

final class EligibleShipmentMethodDto
{
    /** @OA\Property(example=15) */
    public int $shipment_method_id;

    /** @OA\Property(example="Chronopost livraison avant 13H") */
    public string $label;

    /** @OA\Property(example="Livraison J1 garantie partout en france.") */
    public ?string $comment = null;

    /** @OA\Property(example="Chronopost") */
    public string $carrier_name;

    /** @OA\Property(example=false) */
    public bool $is_retail_store;

    /** @OA\Property(example=12.9) */
    public float $cost;

    /** @OA\Property(type="array", @OA\Items(type="string"), example={"express"}) */
    public ?array $tags = null;
}
