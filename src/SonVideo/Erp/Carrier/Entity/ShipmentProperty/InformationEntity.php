<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Entity\ShipmentProperty;

use App\Entity\AbstractEntity;
use Symfony\Component\Validator\Constraints as Assert;

class InformationEntity extends AbstractEntity
{
    public int $delivery_note_id;

    /** @Assert\NotBlank(message="Le code transporteur est manquant") */
    public string $carrier_code;

    public int $carrier_id;

    public string $carrier_name;

    public int $shipment_method_id;

    public string $shipment_method_name;

    public string $shipment_method_code;

    public ?int $expedition_id = null;

    public int $print_status_code;

    public int $customer_order_id;

    public bool $use_insurance;

    public ?float $expedition_insurance_value = null;

    public float $total_delivery_fee_excluded;

    public float $total_delivery_fee_and_tax_excluded;

    public bool $is_mono_parcel;

    public ?int $expedition_parcel_id = null;

    public bool $is_registered_in_expedition;

    public bool $can_be_generated;

    public string $prepared_at = 'now';
}
