<?php

namespace SonVideo\Erp\Inventory\Manager;

use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryLocationReadRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Repository\InventoryLocationRepository;
use SonVideo\Erp\Repository\LocationRepository;

class InventoryLocationManager
{
    private InventoryReadRepository $inventory_read_repository;

    private QueryBuilder $query_builder;

    private LocationRepository $location_repository;

    private InventoryLocationRepository $inventory_location_repository;
    private InventoryLocationReadRepository $inventory_location_read_repository;

    public function __construct(
        LocationRepository $location_repository,
        InventoryLocationRepository $inventory_location_repository,
        InventoryLocationReadRepository $inventory_location_read_repository,
        InventoryReadRepository $inventory_read_repository,
        QueryBuilder $query_builder
    ) {
        $this->location_repository = $location_repository;
        $this->inventory_location_repository = $inventory_location_repository;
        $this->inventory_location_read_repository = $inventory_location_read_repository;
        $this->inventory_read_repository = $inventory_read_repository;
        $this->query_builder = $query_builder;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws InternalErrorException
     */
    public function getLocationByCodeForInventory(string $location_code, int $inventory_id): array
    {
        $inventory = $this->inventory_read_repository->findOneById($inventory_id);
        $location = $this->location_repository->findOneByCode($location_code)->toArray();

        if ('full' !== $inventory['type']) {
            $this->checkLocationIsInInventory($location, $inventory);
        }

        return $location;
    }

    /**
     * @throws InternalErrorException
     * @throws SqlErrorMessageException
     */
    private function checkLocationIsInInventory(array $location, array $inventory): void
    {
        if (
            'full' !== $inventory['type'] &&
            !$this->inventory_location_repository->isLocationInInventoryLocation(
                (int) $location['location_id'],
                (int) $inventory['inventory_id']
            )
        ) {
            throw new InternalErrorException(InternalError::INVENTORY_LOCATION_NOT_RELATED, new NotFoundException('This location is not part of this inventory.'), ['location_label' => $location['label'], 'inventory_id' => (int) $inventory['inventory_id']]);
        }
    }

    /** @return array{is_locked: bool, can_be_set_empty: bool} */
    public function getInventoryLocationMetaDataForUser(int $inventory_id, int $location_id, int $user_id): array
    {
        $user = $this->getUserLockingInventoryLocation($inventory_id, $location_id, $user_id);

        return [
            'is_locked_by' => $user,
            'is_locked' => null !== $user, // for backward compatibility
            'can_be_set_empty' => $this->canInventoryLocationBeSetEmpty($inventory_id, $location_id),
        ];
    }

    private function getUserLockingInventoryLocation(int $inventory_id, int $location_id, int $id_utilisateur): ?string
    {
        $where = [
            'inventory_id' => ['_eq' => $inventory_id],
            'user_id' => ['_neq' => $id_utilisateur],
            'location_id' => ['_eq' => $location_id],
            'collect_id' => ['_ceq' => 'active_collect_id'],
        ];

        $this->query_builder
            ->setWhere($where, InventoryReadRepository::INFO_COLUMS_MAPPING)
            ->setPage(1, 1)
            ->setOrderBy('article_collect_id ASC');

        $infos = $this->inventory_read_repository
            ->findAllInfoOnArticleCollectPaginated($this->query_builder)
            ->getResults();

        return $infos['0']->username ?? null;
    }

    private function canInventoryLocationBeSetEmpty(int $inventory_id, int $location_id): bool
    {
        if ($this->isInventoryLocationScannedEmpty($inventory_id, $location_id)) {
            return false;
        }

        return $this->isThereCollectedProductsAtInventoryLocation($inventory_id, $location_id);
    }

    private function isInventoryLocationScannedEmpty(int $inventory_id, int $location_id): bool
    {
        $inventory_location = $this->inventory_location_repository->findOne($inventory_id, $location_id);

        return false === $inventory_location || null !== $inventory_location['scanned_empty_at'];
    }

    private function isThereCollectedProductsAtInventoryLocation(int $inventory_id, int $location_id): bool
    {
        $where = [
            'inventory_id' => ['_eq' => $inventory_id],
            'location_id' => ['_eq' => $location_id],
        ];
        $this->query_builder->setWhere($where, InventoryReadRepository::INFO_COLUMS_MAPPING);

        return [] ===
            $this->inventory_read_repository->findAllInfoOnArticleCollectPaginated($this->query_builder)->getResults();
    }

    /**
     * @throws SqlErrorMessageException
     * @throws NotFoundException
     */
    public function getRemainingLocationsForActiveCollecte(int $inventory_id): array
    {
        if (!$this->inventory_read_repository->exists($inventory_id)) {
            throw new NotFoundException('Inventory not found.');
        }

        return $this->inventory_location_read_repository->findRemainingLocationsForActiveCollecte($inventory_id);
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function countInventoryLocations(int $inventory_id): int
    {
        if (!$this->inventory_read_repository->exists($inventory_id)) {
            throw new NotFoundException('Inventory not found');
        }

        return $this->inventory_location_read_repository->countLocationsForInventory($inventory_id);
    }
}
