<?php

namespace SonVideo\Erp\DataWarehouse\Repository\Mysql;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Database\ConnectionProvider\MysqlErpConnectionProvider;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrder;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrderLine;
use SonVideo\Erp\Referential\Product;

class CustomerOrderLineForDataWarehouseRepository implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    private MysqlErpConnectionProvider $connection_provider;

    public function __construct(MysqlErpConnectionProvider $connection_provider)
    {
        $this->connection_provider = $connection_provider;
    }

    public function findByIdForDataWarehouse(int $customer_order_id): CustomerOrder
    {
        $sql = <<<'MYSQL'
        SELECT
            c.id_commande AS customer_order_id,
            c.date_creation AS created_at,
            q.created_by,
            c.creation_origine AS origin,
            ctt.meta->>'$.source' AS source,
            ctt.meta->>'$.source_group' AS source_group,
            CASE
              WHEN d.nom_depot = 'Champigny' THEN NULL
              WHEN d.nom_depot = 'Champigny 2' THEN 'Emport Champigny'
              ELSE d.nom_depot END AS sales_location,
            c.cnt_lvr_code_postal AS postal_code,
            p.pays AS country,
            c.cmd_intragroupe AS is_internal,
            cb.id_commande IS NOT NULL AS is_btob,
            c.last_modified_at,
            c.id_prospect AS customer_id,
            q.quote_id,
            TIMESTAMPDIFF(YEAR, pr.date_naissance, c.date_creation)  AS customer_age,
            IF(pr.civilite='M.', 'H', 'F') AS customer_gender,
            pr.date_creation AS customer_since,
            NULL AS customer_from,
            IF(CONCAT_WS(',', c.cnt_lvr_adresse,c.cnt_lvr_code_postal, c.cnt_lvr_ville) = CONCAT_WS(',', c.cnt_fct_adresse,c.cnt_fct_code_postal, c.cnt_fct_ville), TRUE, FALSE) AS shipping_is_billing,
            SUM(ROUND((lvr.quantite * lvr.prix_vente)/(1 + lvr.tva), 2)) AS shipping_cost,
            t.transporteur AS carrier_selected,
            pcom.paiement AS payment_method,
            pcom.creation_date AS payment_created_at,
            pcom.acceptation_date AS payment_accepted_at,
            c.promotion_id AS promo_code,
            csw.description AS status_name,
            SHA1(CONCAT_WS(',', c.creation_origine, c.cnt_fct_email, c.cnt_lvr_email)) AS customer_hash
        FROM backOffice.commande AS c
            LEFT JOIN (
                SELECT
                    q.id_commande,
                    qt.quote_id AS quote_id,
                    u.username AS created_by
                FROM backOffice.commande AS q
                LEFT JOIN backOffice.quote qt ON q.quote_id = qt.quote_id
                LEFT JOIN backOffice.sf_guard_user u ON qt.created_by = u.id
            ) q ON q.id_commande = c.id_commande
            LEFT JOIN backOffice.commande_btob cb ON c.id_commande = cb.id_commande
            LEFT JOIN backOffice.pays p ON p.id_pays = c.cnt_lvr_id_pays
            LEFT JOIN backOffice.BO_STK_depot d ON d.id = c.warehouse_id
            LEFT JOIN backOffice.commande_tag ct ON c.id_commande = ct.id_commande AND ct.tag_id LIKE 'source.%'
            LEFT JOIN backOffice.commande_taxonomy_tag ctt ON ct.tag_id = ctt.tag_id
            LEFT JOIN backOffice.prospect pr ON pr.id_prospect = c.id_prospect
            LEFT JOIN backOffice.produit_commande lvr ON lvr.id_commande = c.id_commande AND lvr.id_produit = {shipment_product_id}
            LEFT JOIN backOffice.transporteur t ON t.id_transporteur = c.id_transporteur
            INNER JOIN backOffice.commande_status csw ON csw.status_id = c.V_computed_status
            LEFT JOIN (
                SELECT pcom.id_commande, pay.paiement, pcom.creation_date, pcom.acceptation_date
                FROM backOffice.paiement_commande pcom
                LEFT JOIN backOffice.paiement pay ON pay.id_paiement = pcom.id_paiement
                WHERE pcom.type = 'paiement'
                AND pcom.annulation_date IS NULL
                AND pcom.id_commande = :customer_order_id
                GROUP BY id_commande
                ORDER BY pcom.creation_date DESC, pcom.creation_montant DESC
            ) pcom ON pcom.id_commande = c.id_commande
            WHERE c.id_commande = :customer_order_id
            GROUP BY lvr.id_commande
        MYSQL;

        $sql = strtr($sql, [
            '{shipment_product_id}' => Product::SHIPMENT_PRODUCT_ID,
        ]);

        $result = $this->connection_provider
            ->getConnection()
            ->fetchAssociative($sql, ['customer_order_id' => $customer_order_id]);

        if (false === $result) {
            throw new \RuntimeException(sprintf('Customer order not found for data warehouse synchronization with id "%s".', $customer_order_id));
        }

        return $this->serializer->denormalize($result, CustomerOrder::class);
    }

    /**
     * Get the customer order line SQL for data warehouse.
     *
     * @return CustomerOrderLine[]
     */
    public function findAllLinesByIdForDataWarehouse(int $customer_order_id): array
    {
        $sql = <<<'MYSQL'
        SELECT
            pc.id_commande AS customer_order_id,
            pc.id AS customer_order_line_id,
            CASE
                WHEN c.V_statut_traitement LIKE '%anomalie%' THEN 'Erreur'
                WHEN c.flux = 'annulation' THEN 'Annulée'
                WHEN pc.quantite < 0 THEN 'Créditée'
                WHEN bl.id_facture IS NOT NULL
                    OR c.flux = 'cloture'
                    OR (c.creation_origine = 'ecranlounge.com' AND bl.date_export_transporteur IS NOT NULL)
                    THEN 'Facturée'
                WHEN bl.date_export_transporteur IS NOT NULL THEN 'Préparée'
                WHEN bl.id_bon_livraison IS NOT NULL THEN 'En préparation'
                WHEN c.V_montant_ttc = c.trcns_montant_accepte THEN
                    IF(
                        backOffice.CMD_PDT_qte_en_attente(c.id_commande, pc.id_produit) <= (backOffice.PDT_ART_V_qte_dispo(pc.id_produit) - backOffice.PDT_ART_qte_reservee(p.id_produit, c.id_commande)),
                        'Préparable',
                        'Payée'
                    )
                ELSE 'Ouverte'
            END AS order_status,
            ean.code AS ean_code,
            p.reference AS reference,
            CONVERT(a.modele USING utf8) AS model,
            CONVERT(m.marque USING utf8) AS brand,
            CONVERT(f.fournisseur USING utf8) AS supplier,
            pc.quantite AS quantity,
            pc.prix_achat * pc.quantite AS total_purchase_cost,
            pc.prix_vente * pc.quantite / (1 + COALESCE(pbl.tva, pc.tva)) AS total_gross_excl_tax,
            IF(pc.quantite < 0, -1, 1) * pc.remise_montant / (1 + COALESCE(pbl.tva, pc.tva)) AS total_discount_excl_tax,
            (pc.prix_vente * pc.quantite + IF(pc.quantite < 0, -1, 1) * pc.remise_montant) / (1 + COALESCE(pbl.tva, pc.tva)) AS total_net_excl_tax,
            pc.prix_garantie_ext * pc.quantite / (1 + COALESCE(pbl.tva, pc.tva)) AS total_guarantee_excl_tax,
            (pc.prix_vente * pc.quantite + IF(pc.quantite < 0, -1, 1) * pc.remise_montant) / (1 + COALESCE(pbl.tva, pc.tva)) - pc.prix_achat * pc.quantite AS total_margin,
            cts.id AS subcategory_id,
            CONVERT(cts.souscategorie USING utf8) AS subcategory,
            ctc.id_categorie AS category_id,
            CONVERT(ctc.categorie USING utf8) AS category,
            ctd.id AS domain_id,
            CONVERT(ctd.domaine USING utf8) AS domain,
            d.nom_depot AS warehouse,
            COALESCE(t.transporteur, c.transporteur) AS carrier,
            COALESCE(ot.libelle_produit, c.libelle_produit) AS shipment_method,
            CASE
                WHEN c.creation_origine = 'ecranlounge.com' THEN bl.date_export_transporteur
                WHEN pc.quantite < 0 THEN c.cloture_date
                ELSE fa.date_creation
            END AS invoiced_at,
            c.id_commande AS customer_order_id,
            NULL AS deleted_at,
            col.statut_date AS shipped_at,
            COALESCE(pbl.tva, pc.tva) AS vat_rate,
            a.poids * 1000 AS weight,
            a.reference_fournisseur AS supplier_reference
        FROM (
            SELECT
                c.id_commande,
                c.creation_origine,
                c.V_statut_traitement,
                c.flux,
                c.V_montant_ttc,
                ROUND(SUM(acceptation_montant + IF(impaye_date IS NOT NULL, impaye_montant, annulation_montant)), 2) AS trcns_montant_accepte,
                c.date_creation,
                c.last_modified_at,
                t.transporteur,
                ot.libelle_produit,
                c.cloture_date
            FROM backOffice.commande AS c
            LEFT JOIN backOffice.transporteur t ON t.id_transporteur = c.id_transporteur
            LEFT JOIN backOffice.BO_TPT_PDT_liste ot ON ot.id = c.id_pdt_transporteur
            LEFT JOIN backOffice.paiement_commande pc
                ON c.id_commande = pc.id_commande
                AND id_paiement != 70 -- Ignore 'Emport dépôt' payment
                AND pc.annulation_date IS NULL
                AND pc.acceptation_date IS NOT NULL
            WHERE c.date_creation >= '2021-01-01 00:00:00' -- source and source group implementation date
              AND c.id_commande = :customer_order_id
            GROUP BY c.id_commande
        ) c
            INNER JOIN backOffice.produit_commande AS pc ON c.id_commande = pc.id_commande
            INNER JOIN backOffice.produit p ON p.id_produit = pc.id_produit
            LEFT JOIN (
                SELECT
                  ean.BO_CTG_PDT_ART_article_id AS article_id,
                  MAX(ean.ean) AS code
                  FROM backOffice.produit_commande pc
                    INNER JOIN backOffice.BO_CTG_PDT_ART_ean AS ean ON ean.BO_CTG_PDT_ART_article_id = pc.id_produit
                    WHERE pc.id_commande = :customer_order_id
                  GROUP BY ean.BO_CTG_PDT_ART_article_id
            ) ean ON ean.article_id = pc.id_produit
            INNER JOIN backOffice.article a ON p.id_produit = a.id_produit
            INNER JOIN backOffice.marque m ON a.id_marque = m.id_marque
            INNER JOIN backOffice.fournisseur f ON a.id_fournisseur = f.id_fournisseur
            INNER JOIN backOffice.CTG_TXN_souscategorie cts ON p.id_souscategorie = cts.id
            INNER JOIN backOffice.CTG_TXN_categorie ctc ON cts.dft_categorie_id = ctc.id_categorie
            INNER JOIN backOffice.CTG_TXN_domaine ctd ON ctc.dft_domaine_id = ctd.id
            LEFT JOIN backOffice.bon_livraison bl ON pc.id_bon_livraison = bl.id_bon_livraison
            LEFT JOIN backOffice.produit_bon_livraison pbl ON pc.id_bon_livraison = pbl.id_bon_livraison AND a.id_produit = pbl.id_produit
            LEFT JOIN (
                SELECT cl_stat.statut_date, eblcp.produit_id, cl.id_bon_livraison
                    FROM
                      backOffice.colis cl
                        INNER JOIN backOffice.bon_livraison bl ON cl.id_bon_livraison = bl.id_bon_livraison
                        INNER JOIN backOffice.BO_EXP_expeditions_bl ebl ON ebl.bon_livraison_id = cl.id_bon_livraison
                        INNER JOIN backOffice.BO_EXP_expeditions_bl_colis eblc
                          ON eblc.expedition_bl_id = ebl.id AND CONVERT(eblc.no_colis USING UTF8) = CONVERT(cl.no_colis USING UTF8)
                        INNER JOIN backOffice.BO_EXP_expeditions_bl_colis_produit eblcp ON eblc.id = eblcp.colis_id
                        INNER JOIN backOffice.colis_statut cl_stat
                          ON cl.id_colis = cl_stat.id_colis AND cl_stat.statut IN ('livre', 'livre_retard')
                    WHERE bl.id_commande = :customer_order_id
                    GROUP BY eblcp.produit_id,
                             cl.id_bon_livraison
            ) col ON col.produit_id = pc.id_produit AND col.id_bon_livraison = bl.id_bon_livraison
            LEFT JOIN backOffice.transporteur t ON t.id_transporteur = bl.id_transporteur
            LEFT JOIN backOffice.BO_TPT_PDT_liste ot ON ot.id = bl.id_pdt_transporteur
            LEFT JOIN backOffice.BO_STK_depot d ON bl.id_depot = d.id
            LEFT JOIN backOffice.facture fa ON fa.id_facture = bl.id_facture
            WHERE pc.id_produit NOT IN ({shipment_product_id}, {catalog_product_id}, {magazine_product_id})
                AND c.id_commande = :customer_order_id
            GROUP BY pc.id
            ORDER BY c.last_modified_at
        MYSQL;

        $sql = strtr($sql, [
            '{shipment_product_id}' => Product::SHIPMENT_PRODUCT_ID,
            '{catalog_product_id}' => Product::CATALOG_PRODUCT_ID,
            '{magazine_product_id}' => Product::MAGAZINE_PRODUCT_ID,
        ]);

        return $this->serializer->denormalize(
            $this->connection_provider
                ->getConnection()
                ->fetchAllAssociative($sql, ['customer_order_id' => $customer_order_id]),
            CustomerOrderLine::class . '[]'
        );
    }
}
