<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\ConnectionProvider\PgDataWarehouseConnectionProvider;
use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderLineRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model\CustomerOrderLineModel;
use Doctrine\DBAL\Exception\DriverException;
use function Sentry\captureException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\CustomerOrderUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Repository\Mysql\CustomerOrderLineForDataWarehouseRepository;
use SonVideo\Erp\PromoOffer\Manager\CachedOngoingPromoCodes;
use SonVideo\Erp\Referential\DataWarehouse\CustomerOrderLineStatus;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use SonVideo\Orm\Definition\Operator;
use SonVideo\Orm\Entity\DefinedSource;
use SonVideo\Orm\WhereConditionMaker;

final class CustomerOrderUpsertTopicSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::CUSTOMER_ORDER_UPSERT;

    private PgDataWarehouseConnectionProvider $pg_data_warehouse_connection_provider;
    private CustomerOrderLineForDataWarehouseRepository $customer_order_line_for_data_warehouse_repository;
    private CachedOngoingPromoCodes $cached_ongoing_promo_codes;
    private CustomerOrderRepository $customer_order_repository;
    private CustomerOrderLineRepository $customer_order_line_repository;

    public function __construct(
        PgDataWarehouseConnectionProvider $pg_data_warehouse_connection_provider,
        CustomerOrderLineForDataWarehouseRepository $customer_order_line_for_data_warehouse_repository,
        CachedOngoingPromoCodes $cached_ongoing_promo_codes,
        CustomerOrderRepository $customer_order_repository,
        CustomerOrderLineRepository $customer_order_line_repository
    ) {
        $this->pg_data_warehouse_connection_provider = $pg_data_warehouse_connection_provider;
        $this->customer_order_line_for_data_warehouse_repository = $customer_order_line_for_data_warehouse_repository;
        $this->cached_ongoing_promo_codes = $cached_ongoing_promo_codes;
        $this->customer_order_repository = $customer_order_repository;
        $this->customer_order_line_repository = $customer_order_line_repository;
    }

    /** @param CustomerOrderUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            // Start a PG transaction in the DW database (we only need to write in that database)
            $this->pg_data_warehouse_connection_provider->getConnection()->beginTransaction();

            $customer_order = $this->customer_order_line_for_data_warehouse_repository->findByIdForDataWarehouse(
                $synchronizable_topic->customer_order_id
            );

            $promo_codes = $this->cached_ongoing_promo_codes->getAllLabelsById();

            if (null !== $customer_order->promo_code) {
                // promo_code id for this order matches a known value from $promo_codes : use the value instead.
                $customer_order->promo_code = $promo_codes[$customer_order->promo_code] ?? null;
            }

            $this->customer_order_repository->upsert($this->serializer->normalize($customer_order));

            $lines = $this->customer_order_line_for_data_warehouse_repository->findAllLinesByIdForDataWarehouse(
                $customer_order->customer_order_id
            );

            foreach ($lines as $line) {
                $this->customer_order_line_repository->upsert($this->serializer->normalize($line));
            }

            $where = (new WhereConditionMaker(DefinedSource::with(CustomerOrderLineModel::class)))->make([
                'customer_order_line_id' => [
                    Operator::NIN => array_column($lines, 'customer_order_line_id'),
                ],
                'customer_order_id' => [
                    Operator::EQ => $customer_order->customer_order_id,
                ],
            ]);

            $this->customer_order_line_repository->updateWhere(
                ['deleted_at' => new \DateTime(), 'order_status' => CustomerOrderLineStatus::DELETED],
                $where->conditions,
                $where->binded_params
            );

            $this->pg_data_warehouse_connection_provider->getConnection()->commit();
        } catch (\Throwable $exception) {
            $this->pg_data_warehouse_connection_provider->getConnection()->rollBack();

            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            // An SQL driver exception should be logged directly in order to not lose the exception trace
            if ($exception instanceof DriverException) {
                captureException($exception);
            }

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
