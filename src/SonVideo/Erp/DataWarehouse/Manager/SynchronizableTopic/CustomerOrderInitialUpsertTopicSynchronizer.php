<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\ConnectionProvider\PgDataWarehouseConnectionProvider;
use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderInitialLineRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\CustomerOrderInitialUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Repository\Mysql\CustomerOrderInitialForDataWarehouseRepository;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class CustomerOrderInitialUpsertTopicSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT;
    private CustomerOrderInitialLineRepository $customer_order_initial_line_repository;
    private CustomerOrderInitialForDataWarehouseRepository $customer_order_initial_repository;
    private PgDataWarehouseConnectionProvider $pg_data_warehouse_connection_provider;

    public function __construct(
        CustomerOrderInitialLineRepository $customer_order_initial_line_repository,
        CustomerOrderInitialForDataWarehouseRepository $customer_order_initial_repository,
        PgDataWarehouseConnectionProvider $pg_data_warehouse_connection_provider
    ) {
        $this->customer_order_initial_line_repository = $customer_order_initial_line_repository;
        $this->customer_order_initial_repository = $customer_order_initial_repository;
        $this->pg_data_warehouse_connection_provider = $pg_data_warehouse_connection_provider;
    }

    /** @param CustomerOrderInitialUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            // Start a PG transaction in the DW database (we only need to write in that database)
            $this->pg_data_warehouse_connection_provider->getConnection()->beginTransaction();

            $products = $this->customer_order_initial_repository->findByIdForDataWarehouse(
                $synchronizable_topic->customer_order_id
            );

            foreach ($products as $product) {
                $this->customer_order_initial_line_repository->upsert($this->serializer->normalize($product));
            }

            $this->pg_data_warehouse_connection_provider->getConnection()->commit();
        } catch (\Throwable $exception) {
            $this->pg_data_warehouse_connection_provider->getConnection()->rollBack();

            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
