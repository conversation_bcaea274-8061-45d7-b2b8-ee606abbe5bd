<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderPaymentStatusRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\CustomerOrderPaymentStatusUpsertTopicContent;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class CustomerOrderPaymentStatusUpsertTopicSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT;
    private CustomerOrderPaymentStatusRepository $customer_order_payment_status_repository;

    public function __construct(CustomerOrderPaymentStatusRepository $customer_order_payment_status_repository)
    {
        $this->customer_order_payment_status_repository = $customer_order_payment_status_repository;
    }

    /** @param CustomerOrderPaymentStatusUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            $this->customer_order_payment_status_repository->upsert(
                $this->serializer->normalize($synchronizable_topic)
            );
        } catch (\Exception $exception) {
            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
