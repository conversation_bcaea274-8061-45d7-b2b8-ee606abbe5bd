<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SafetyStockRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SafetyStockUpsertTopicContent;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class SafetyStockTopicUpsertSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::SAFETY_STOCK_UPSERT;
    private SafetyStockRepository $safety_stock_repository;

    public function __construct(SafetyStockRepository $safety_stock_repository)
    {
        $this->safety_stock_repository = $safety_stock_repository;
    }

    /** @param SafetyStockUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            $this->safety_stock_repository->upsert($this->serializer->normalize($synchronizable_topic));
        } catch (\Exception $exception) {
            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
