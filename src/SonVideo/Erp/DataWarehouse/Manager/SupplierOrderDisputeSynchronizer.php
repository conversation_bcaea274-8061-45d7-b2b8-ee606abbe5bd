<?php

namespace SonVideo\Erp\DataWarehouse\Manager;

use App\Database\ConstQueriesInterface;
use App\Database\PgDataWarehouse\DataSchema\DataParameterModel;
use App\Database\PgDataWarehouse\DataSchema\SupplierOrderDisputeModel;
use SonVideo\Erp\SupplierOrderProduct\Mysql\Repository\SupplierOrderProductRepository;

class SupplierOrderDisputeSynchronizer extends AbstractSynchronizer
{
    public const LAST_UPDATE = 'synchronizer.supplier_order_dispute.last_update';
    private SupplierOrderProductRepository $supplier_order_product_repository;
    private SupplierOrderDisputeModel $supplier_order_dispute_model;

    public function __construct(
        SupplierOrderProductRepository $supplier_order_product_repository,
        SupplierOrderDisputeModel $supplier_order_dispute_model,
        DataParameterModel $data_parameter_model
    ) {
        parent::__construct($data_parameter_model);
        $this->supplier_order_product_repository = $supplier_order_product_repository;
        $this->supplier_order_dispute_model = $supplier_order_dispute_model;
    }

    /** @throws \Exception */
    public function synchronize(\DateTime $start_date, \DateTime $end_date, int $buffer_size = 1000): void
    {
        $this->synchronizeOne(0, $buffer_size);
    }

    /** {@inheritDoc}
     * @throws \Exception
     */
    public function synchronizeOne($key, int $buffer_size = 1000): void
    {
        $supplier_order_disputes_count = $this->supplier_order_product_repository->fetchSupplierOrderDisputes();

        $this->supplier_order_dispute_model->bufferize($supplier_order_disputes_count);

        $this->supplier_order_dispute_model->flush(ConstQueriesInterface::UPSERT);
    }
}
