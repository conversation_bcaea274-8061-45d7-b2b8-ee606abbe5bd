<?php

namespace SonVideo\Erp\DataWarehouse\Manager;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use function Sentry\captureException;
use SonVideo\Erp\DataWarehouse\Contract\SynchronizableTopicFeedbackHandlerInterface;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicFeedback;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class SynchronizableTopicCommandFeedbackHandler implements SynchronizableTopicFeedbackHandlerInterface, SerializerAwareInterface, LoggerAwareInterface
{
    use SerializerAwareTrait;
    use LoggerAwareTrait;

    /** @var SynchronizableTopicFeedback[] */
    private array $synchronizable_topics_feedback = [];

    private InputInterface $input;
    private OutputInterface $output;
    private OutputInterface $feedback_section;
    private SymfonyStyle $io;

    public function __construct(InputInterface $input, ConsoleOutput $output, ?SymfonyStyle $io = null)
    {
        $this->input = $input;
        $this->output = $output;

        $this->feedback_section = $output->section();
        $this->io = $io ?? new SymfonyStyle($input, $this->feedback_section);
    }

    public function start(array $synchronizable_topics_feedback): void
    {
        $this->synchronizable_topics_feedback = $synchronizable_topics_feedback;

        // Initial feedback
        $io = new SymfonyStyle($this->input, $this->output);
        $io->title('Topics à synchronizer dans le data-warehouse...');
        $io->comment('Les traitements en erreur des runs précédents sont inclus dans le run actuel');

        // Only clear the console if we're not in verbose mode
        if ($this->output->getVerbosity() >= OutputInterface::VERBOSITY_VERBOSE) {
            $io->comment('Utilisation du mode "verbose", la section ne sera pas effacée à chaque mise à jour');
        }
    }

    public function getByTopic(string $topic): SynchronizableTopicFeedback
    {
        if (!isset($this->synchronizable_topics_feedback[$topic])) {
            throw new \InvalidArgumentException(sprintf('No feedback found for topic "%s"', $topic));
        }

        return $this->synchronizable_topics_feedback[$topic];
    }

    public function progress(?SynchronizableTopicFeedback $synchronizable_topic_feedback = null): void
    {
        // Update feedback
        if ($synchronizable_topic_feedback instanceof SynchronizableTopicFeedback) {
            $this->synchronizable_topics_feedback[
                $synchronizable_topic_feedback->getTopic()
            ] = $synchronizable_topic_feedback;

            // Only clear the console if we're not in verbose mode
            if ($this->feedback_section->getVerbosity() < OutputInterface::VERBOSITY_VERBOSE) {
                $this->feedback_section->clear();
            }
        }

        $this->io->table(
            ['Topic', 'Inséré', 'En erreur', 'Total à traiter'],
            $this->serializer->normalize($this->synchronizable_topics_feedback)
        );
    }

    public function end(): void
    {
        $synchronizable_topics_feedback = $this->serializer->normalize($this->synchronizable_topics_feedback);

        $total_handled = array_sum(array_column($synchronizable_topics_feedback, 'handled'));
        $total_errors = array_sum(array_column($synchronizable_topics_feedback, 'errors'));

        if ($this->feedback_section->getVerbosity() < OutputInterface::VERBOSITY_VERBOSE) {
            $this->io->success(sprintf('%d topic(s) synchronisé(s) dans le data-warehouse', $total_handled));
        }

        if ($total_errors > 0) {
            $message = sprintf(
                '[DATA-WAREHOUSE] %d topic(s) de synchro en erreur - Vérifiez les breadcrumbs pour plus de détails',
                $total_errors
            );

            // Directly use Sentry captureException because the console event listener for sentry symfony removes
            // all the breadcrumbs.
            captureException(new \Exception($message));
        }
    }
}
