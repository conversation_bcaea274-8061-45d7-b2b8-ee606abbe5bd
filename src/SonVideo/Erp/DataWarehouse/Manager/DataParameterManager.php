<?php

namespace SonVideo\Erp\DataWarehouse\Manager;

use App\Database\PgDataWarehouse\DataSchema\DataParameter;
use App\Database\PgDataWarehouse\DataSchema\DataParameterModel;

class DataParameterManager
{
    private DataParameterModel $model;

    public function __construct(DataParameterModel $model)
    {
        $this->model = $model;
    }

    /** Get the parameter value */
    public function getSyncLastUpdate(string $name): DataParameter
    {
        return $this->model->findByPK(['key' => $name]);
    }
}
