<?php

namespace SonVideo\Erp\SalesChannel\Dto;

use Symfony\Component\Validator\Constraints as Assert;

final class SalesChannelContextDto
{
    /** @Assert\NotBlank() */
    public int $sales_channel_id;

    /** @Assert\PositiveOrZero() */
    public ?float $average_commission_rate = null;

    /** @Assert\PositiveOrZero() */
    public ?float $minimum_margin_rate = null;

    /** @Assert\PositiveOrZero() */
    public int $minimum_available_quantity;
}
