<?php

namespace SonVideo\Erp\SalesChannel\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

class SalesChannelEntity extends AbstractEntity
{
    public int $sales_channel_id;

    public string $label;

    public ?string $legacy_name = null;

    public int $display_order;

    public ?float $average_commission_rate = null;

    public ?float $minimum_margin_rate = null;

    public ?int $minimum_available_quantity = null;

    public ?float $minimum_selling_price = null;

    public ?float $maximum_selling_price = null;

    /** @var array|JsonType|null */
    public ?array $statistics_sales_channel = null;
}
