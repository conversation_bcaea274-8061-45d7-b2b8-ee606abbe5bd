<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Wms;

use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

/**
 * Class MoveMissionReadRepository.
 */
class MoveMissionReadRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'move_mission_id' => 'wmm.move_mission_id',
        'move_mission_type_id' => 'wmm.move_mission_type_id',
        'created_at' => 'wmm.created_at',
        'assigned_to' => 'wmm.assigned_to',
        'assigned_at' => 'wmm.assigned_at',
        'started_at' => 'wmm.started_at',
        'ended_at' => 'wmm.ended_at',
        'canceled_at' => 'wmm.canceled_at',
        'canceled_by' => 'wmm.canceled_by',
        'product_id' => 'wmm.product_id',
        'product_sku' => 'p.reference',
        'product_name' => 'a.description_courte',
        'quantity' => 'wmm.quantity',
        'mission_type_code' => 'wmmt.code',
        'warehouse_id' => 'wa.warehouse_id',
        'location_id' => 'wl.location_id',
        'location_code' => 'wl.code',
        'move_location_id' => 'ms.id_emplacement',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
              SELECT
                wmm.move_mission_id                                      AS move_mission_id,
                wmm.move_mission_type_id                                 AS move_mission_type_id,
                wmm.created_at                                           AS created_at,
                wmm.assigned_to                                          AS assigned_to,
                backOffice.GET_COMPUTED_USER_NAME_BY_ID(wmm.assigned_to) AS assigned_to_name,
                wmm.assigned_at                                          AS assigned_at,
                wmm.started_at                                           AS started_at,
                wmm.ended_at                                             AS ended_at,
                wmm.canceled_at                                          AS canceled_at,
                wmm.canceled_by                                          AS canceled_by,
                backOffice.GET_COMPUTED_USER_NAME_BY_ID(wmm.canceled_by) AS canceled_by_name,
                wmm.product_id                                           AS product_id,
                p.reference                                              AS product_sku,
                a.description_courte                                     AS product_name,
                wmm.quantity                                             AS quantity,
                wmmt.code                                                AS mission_type_code,
                wa.warehouse_id                                          AS warehouse_id,
                wl.location_id                                           AS location_id,
                wl.code                                                  AS location_code
                FROM
                  backOffice.WMS_move_mission                   wmm
                    INNER JOIN backOffice.WMS_move_mission_type wmmt ON wmm.move_mission_type_id = wmmt.move_mission_type_id
                    LEFT JOIN  backOffice.WMS_product_location  wpl ON wmm.move_mission_id = wpl.move_mission_id AND wmm.product_id = wpl.product_id
                    LEFT JOIN  backOffice.WMS_location          wl ON wpl.location_id = wl.location_id
                    LEFT JOIN  backOffice.WMS_area              wa ON wl.area_id = wa.area_id
                    INNER JOIN backOffice.produit               p ON wmm.product_id = p.id_produit
                    INNER JOIN backOffice.article               a ON a.id_produit = p.id_produit
                    LEFT JOIN backOffice.mouvement_stock        ms ON wmm.move_mission_id = ms.move_mission_id
                WHERE {conditions}
              GROUP BY wmm.move_mission_id
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        $pager = $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );

        $results = $pager->getResults();
        foreach ($results as $key => $result) {
            $results[$key]->moves = $this->getMoves($result->move_mission_id, $result->product_id);
        }

        $pager->setResults($results);

        return $pager;
    }

    protected function getMoves(int $move_mission_id, int $product_id): array
    {
        $sql = <<<SQL
        SELECT
          ms.id_mouvement_stock AS move_id,
          ms.quantite           AS quantity,
          ms.date_creation      AS created_at,
          wl.location_id        AS location_id,
          wl.code               AS location_code,
          wl.label              AS location_label
          FROM
            backOffice.mouvement_stock           ms
              INNER JOIN backOffice.WMS_location wl ON ms.id_emplacement = wl.location_id
          WHERE ms.id_produit = :product_id
            AND ms.move_mission_id = :move_mission_id
          ORDER BY ms.date_creation ASC
        SQL;

        return $this->legacy_pdo->fetchObjects($sql, [
            'move_mission_id' => $move_mission_id,
            'product_id' => $product_id,
        ]);
    }

    public function exists(int $move_mission_id): bool
    {
        $result = $this->legacy_pdo->fetchValue(
            <<<SQL
            SELECT
              EXISTS(
                SELECT 1 FROM backOffice.WMS_move_mission WHERE move_mission_id = :move_mission_id
                ) AS entry;
            SQL
            ,
            ['move_mission_id' => $move_mission_id]
        );

        return (bool) $result;
    }

    public function findMoveMissionsBlockingPartialInventory(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
            SELECT
                wmm.move_mission_id AS move_mission_id,
                wmm.quantity        AS quantity,
                wmm.created_at      AS created_at
            FROM backOffice.WMS_move_mission wmm
            INNER JOIN  backOffice.WMS_product_location  wpl
                ON wmm.move_mission_id = wpl.move_mission_id
                AND wmm.product_id = wpl.product_id
                AND wpl.quantity > 0
            INNER JOIN  backOffice.WMS_location wl ON wpl.location_id = wl.location_id
            INNER JOIN  backOffice.WMS_area wa ON wl.area_id = wa.area_id
            INNER JOIN backOffice.BO_INV_zone_location bizl ON bizl.location_id = wpl.location_id
            INNER JOIN backOffice.BO_INV_zone_inventory bizi ON bizi.zone_id = bizl.zone_id
            INNER JOIN backOffice.BO_INV_inventaire bii ON bizi.inventory_id = bii.id AND bii.id_depot = wa.warehouse_id
            WHERE bizi.inventory_id = :inventory_id AND wmm.quantity > 0
            GROUP BY wmm.move_mission_id
            ORDER BY created_at DESC
            ) tmp
        SQL;

        return $this->legacy_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }

    public function findMoveMissionsBlockingProductInventory(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
            SELECT
                wmm.move_mission_id AS move_mission_id,
                wmm.quantity        AS quantity,
                wmm.created_at      AS created_at
            FROM backOffice.WMS_move_mission wmm
            INNER JOIN  backOffice.WMS_product_location  wpl
                ON wmm.move_mission_id = wpl.move_mission_id
                AND wmm.product_id = wpl.product_id
                AND wpl.quantity > 0
            INNER JOIN  backOffice.WMS_location wl ON wpl.location_id = wl.location_id
            INNER JOIN  backOffice.WMS_area wa ON wl.area_id = wa.area_id
            INNER JOIN backOffice.BO_INV_zone_location bizl ON bizl.location_id = wpl.location_id
            INNER JOIN backOffice.BO_INV_zone_inventory bizi ON bizi.zone_id = bizl.zone_id
            INNER JOIN backOffice.BO_INV_inventaire bii ON bizi.inventory_id = bii.id AND bii.id_depot = wa.warehouse_id
            -- No LATERAL in Mysql5 so we have to use subqueries for 'id_depot'
            INNER JOIN (
                -- find location containing products that have not been counted this year
                SELECT bizl.location_id
                FROM backOffice.BO_INV_zone_inventory bizi
                  INNER JOIN backOffice.BO_INV_zone_location bizl ON bizi.zone_id = bizl.zone_id
                  INNER JOIN backOffice.WMS_location wl ON bizl.location_id = wl.location_id
                  LEFT JOIN backOffice.WMS_product_location wpl ON wl.location_id = wpl.location_id
                  LEFT JOIN backOffice.article a ON wpl.product_id = a.id_produit
                  LEFT JOIN backOffice.BO_INV_differential bid ON a.id_produit = bid.product_id
                  LEFT JOIN backOffice.BO_INV_inventaire _bii ON
                    bid.inventory_id = _bii.id
                    AND _bii.id_depot = (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                WHERE
                  backOffice.PDT_ART_qte_stock_depot(
                    a.id_produit,
                    (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                  ) > 0
                  AND a.compose = 0
                  AND a.is_auto_picked = 0
                  AND wpl.quantity > 0
                  AND bizi.inventory_id = :inventory_id
                  AND wl.is_active = 1
                GROUP BY wl.location_id
                HAVING MAX(_bii.inv_date_closed) < YEAR(NOW()) OR MAX(_bii.id) IS NULL
                ) inv_location ON wpl.location_id = inv_location.location_id
            WHERE bizi.inventory_id = :inventory_id AND wmm.quantity > 0
            GROUP BY wmm.move_mission_id
            ORDER BY created_at DESC
            ) tmp
        SQL;

        return $this->legacy_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }
}
