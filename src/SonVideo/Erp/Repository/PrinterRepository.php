<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository;

use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

/**
 * Class PrinterRepository.
 */
class PrinterRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'name' => 'p.name',
        'printer_id' => 'p.printer_id',
        'cups_name' => 'p.status',
        'warehouse_id' => 'p.id_depot',
        'type' => 'p.type',
        'status' => 'p.status',
        'created_at' => 'p.created_at',
    ];

    /** @throws NotFoundException */
    public function findByNameAndType(string $printer_name, string $type): \stdClass
    {
        $sql = <<<SQL
        SELECT
          name,
          printer_id,
          cups_name,
          id_depot,
          type,
          status,
          created_at
        FROM backOffice.printer
        WHERE name = :printer_name
          AND type = :type
        SQL;

        $printer = $this->legacy_pdo->fetchOne($sql, ['printer_name' => $printer_name, 'type' => $type]);

        if (false === $printer) {
            throw new NotFoundException('Printer not found.');
        }

        return (object) $printer;
    }

    /** @throws NotFoundException */
    public function findByIdAndType(int $printer_id, string $type): \stdClass
    {
        $sql = <<<SQL
        SELECT
          name,
          printer_id,
          cups_name,
          id_depot,
          type,
          status,
          created_at
        FROM backOffice.printer
        WHERE printer_id = :printer_id
          AND type = :type
        SQL;

        $printer = $this->legacy_pdo->fetchOne($sql, ['printer_id' => $printer_id, 'type' => $type]);

        if (false === $printer) {
            throw new NotFoundException('Printer not found.');
        }

        return (object) $printer;
    }

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                p.name       AS name,
                p.printer_id AS printer_id,
                p.cups_name  AS cups_name,
                p.id_depot   AS warehouse_id,
                p.type       AS type,
                p.status     AS status,
                p.created_at AS created_at
                FROM backOffice.printer p
                WHERE {where}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
