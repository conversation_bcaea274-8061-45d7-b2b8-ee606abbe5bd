<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository;

use App\Sql\AbstractLegacyRepository;

class PaymentCorrectiveWriteRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'payment_id' => 'id_paiement',
        'id_trans_banque' => 'id_trans_banque',
        'payment_means' => 'moyen',
        'amount' => 'montant',
        'status' => 'statut',
        'status_detail' => 'statut_detail',
        'valid_until' => 'date_validite',
        'card_number' => 'no_porteur',
        'warranty_type' => 'grt_type',
        'warranty_status' => 'grt_statut',
        'warranty_status_detail' => 'grt_statut_detail',
        'remit_number' => 'no_remise',
        'accepted_date' => 'date_acceptation',
        'cancel_date' => 'date_annulation',
        'remit_date' => 'date_remise',
        'bank' => 'banque',
        'currency' => 'devise',
        'correction_date' => 'date_correctif',
        'creation_date' => 'date_creation',
    ];

    /** create */
    public function create(array $data): int
    {
        $mapping = [];
        foreach (array_keys($data) as $key) {
            $mapping[] = sprintf('%s = :%s', self::COLUMNS_MAPPING[$key], $key);
        }
        $sql = <<<SQL
        INSERT INTO paiements.correctif
        SET {values}
        SQL;
        $sql = strtr($sql, [
            '{values}' => implode(",\n", $mapping),
        ]);

        return $this->legacy_pdo->fetchAffected($sql, $data);
    }
}
