<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Shipping;

use App\Sql\AbstractLegacyRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

/**
 * Class DeliveryTicketActivityLogReadRepository.
 */
class DeliveryTicketActivityLogReadRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /** fetchValidatedByOrderOriginForNotification */
    public function fetchValidatedByOrderOriginForNotification(string $order_origin): array
    {
        $sql = <<<SQL
        SELECT dtal.delivery_ticket_id
          FROM
            backOffice.delivery_ticket_activity_log              dtal
              LEFT JOIN  backOffice.delivery_ticket_activity_log dtal2 ON dtal.delivery_ticket_id = dtal2.delivery_ticket_id AND dtal2.action = 'TRACKING_NOTIFIED'
              INNER JOIN backOffice.bon_livraison                bl ON dtal.delivery_ticket_id = bl.id_bon_livraison
              INNER JOIN backOffice.commande                     c ON bl.id_commande = c.id_commande
          WHERE dtal.action = 'VALIDATED'
            AND (dtal2.action IS NULL OR dtal2.created_at > now() - INTERVAL 15 minute )
            AND c.creation_origine = :order_origin
          GROUP BY dtal.delivery_ticket_id
          ORDER BY dtal.created_at ASC
        ;
        SQL;

        return $this->legacy_pdo->fetchObjects($sql, ['order_origin' => $order_origin]);
    }
}
