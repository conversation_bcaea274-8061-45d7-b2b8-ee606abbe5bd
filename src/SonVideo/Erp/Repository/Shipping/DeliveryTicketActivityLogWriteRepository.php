<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Shipping;

use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

/**
 * Class DeliveryTicketActivityLogWriteRepository.
 */
class DeliveryTicketActivityLogWriteRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /**
     * write.
     *
     * @throws SqlErrorMessageException
     */
    public function write(
        int $delivery_ticket_id,
        string $action,
        int $user_id,
        string $description = ''
    ): DeliveryTicketActivityLogWriteRepository {
        $sql = <<<SQL
        CALL backOffice.log_activity_delivery_ticket(:delivery_ticket_id, :action, :user_id, :description);
        SQL;

        $this->legacy_pdo->fetchValue($sql, [
            'delivery_ticket_id' => $delivery_ticket_id,
            'action' => $action,
            'user_id' => $user_id,
            'description' => $description,
        ]);

        return $this;
    }
}
