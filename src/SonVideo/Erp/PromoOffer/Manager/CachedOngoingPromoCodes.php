<?php

namespace SonVideo\Erp\PromoOffer\Manager;

use Son<PERSON>ideo\Synapps\Client\RpcClientAwareInterface;
use SonVideo\Synapps\Client\RpcClientAwareTrait;

final class CachedOngoingPromoCodes implements RpcClientAwareInterface
{
    use RpcClientAwareTrait;

    private bool $has_been_collected = false;
    private array $collected_promo_codes = [];

    /** @return array<int,string> Array associating each promo_code id to its label (eg: 3001 => "BLUEWINE") */
    public function getAllLabelsById(): array
    {
        if (!$this->has_been_collected) {
            $this->retrieveAndCachePromoCodes();
        }

        return $this->collected_promo_codes;
    }

    /**
     * Retrieve all promo codes from CMS.
     * The result is cached locally in order to be reused consecutively in loop processes.
     */
    private function retrieveAndCachePromoCodes(): void
    {
        // Request the first promo code to get total_items count.
        $first_promo_code = $this->rpc_client->call('bo-cms', 'promo_offer:list', [['type' => 'promo_code'], 1, 1]);
        if (0 === $first_promo_code['result']['total_items']) {
            $this->has_been_collected = true;

            return;
        }

        // Request all promo codes
        $all_promo_codes = $this->rpc_client->call('bo-cms', 'promo_offer:list', [
            ['type' => 'promo_code'],
            $first_promo_code['result']['total_items'],
            1,
        ]);

        // Create array associating each promo_code id to its label
        $this->collected_promo_codes = [];
        foreach ($all_promo_codes['result']['promo_offers'] as $promo_code) {
            if (empty($promo_code['definition']['promo_code']['code'])) {
                continue;
            }
            $this->collected_promo_codes[$promo_code['promo_offer_id']] =
                $promo_code['definition']['promo_code']['code'];
        }

        $this->has_been_collected = true;
    }
}
