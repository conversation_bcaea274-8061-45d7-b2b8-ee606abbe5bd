<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\User\Entity;

use App\Entity\AbstractEntity;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * This represents the user as expected by symfony auth
 * hence why it extends the UserInterface.
 */
class UserEntity extends AbstractEntity implements UserInterface
{
    public const SYSTEM_USERNAME = 'backoffice';
    public const SYSTEM_ID = 1000;
    public const CATHY_ID = 1185;

    public int $id_utilisateur;

    public string $utilisateur;

    public string $status;

    public ?string $groupes = null;

    public ?string $societe = null;

    public ?string $site = null;

    public ?string $titre = null;

    public ?string $civilite = null;

    public ?string $nom = null;

    public ?string $prenom = null;

    public string $email;

    public string $employe;

    public ?string $signature = null;

    public ?array $roles = null;

    public string $username;

    public ?int $warehouse_id = null;

    public ?string $seller_commission_role = null;

    /** @{@inheritDoc} */
    public function getRoles()
    {
        return $this->roles;
    }

    /** @{@inheritDoc} */
    public function getPassword()
    {
        return '';
    }

    /** @{@inheritDoc} */
    public function getSalt()
    {
        return '';
    }

    /** @{@inheritDoc} */
    public function getUsername()
    {
        return $this->username;
    }

    /** @{@inheritDoc} */
    public function eraseCredentials(): void
    {
    }
}
