services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: true

    App\Tests\Utils\:
        resource: '../tests/Utils/*'

    #
    # Autowiring
    # Redeclared if one the manager is redeclared (mostly for mocking purpose)
    #
    _instanceof:
        SonVideo\Erp\Carrier\Contract\CarrierClientInterface:
            public: true
            tags: [ 'app.carrier.client' ]
        SonVideo\Erp\Mailing\Contract\EmailDispatcherInterface:
            public: true
            tags: [ 'app.mailing.dispatcher' ]

    #
    # Misc
    #
    SonVideo\Synapps\Client\Manager\SynappsNotifier:
        class: App\Tests\Mock\SynappsNotifierMock
        public: true

    SonVideo\Erp\Document\Manager\HtmlToPdfGenerator:
        class: App\Tests\Mock\HtmlToPdfGeneratorMock

    SonVideo\Synapps\Client\RpcClientService:
        class: App\Tests\Mock\RpcClientServiceMock
        arguments:
            - '%synapps_client.server_uri%'
            - "@=service('pomm').getDefaultSession()"
            - '%synapps_client.client_options%'

    SonVideo\Erp\Payment\Manager\PaymentV2StateInterface:
        class: 'App\Tests\Mock\Erp\Client\PaymentV2State'
        public: true

    SonVideo\Erp\Payment\Manager\PaymentV2CreatorInterface:
        class: 'App\Tests\Mock\Erp\Client\PaymentV2Creator'
        public: true

    #
    # Erp
    #
    SonVideo\Erp\Client\Erpv1ApiClient:
        class: App\Tests\Mock\Erp\Client\ErpV1ApiClient

    SonVideo\Erp\Client\Erpv1PaymentSoapClient:
        class: App\Tests\Mock\Erp\Client\Erpv1PaymentSoapClient

    SonVideo\Erp\Client\OgoneApiClient:
        class: App\Tests\Mock\Erp\Client\OgoneApiClient
        arguments:
            - '%env(OGONE_STATUS_API_ENDPOINT)%'
            - '%env(OGONE_STATUS_API_USERID)%'
            - '%env(OGONE_STATUS_API_ACCOUNT1_PSPID)%'
            - '%env(OGONE_STATUS_API_ACCOUNT1_PSWD)%'
            - '%env(OGONE_STATUS_API_ACCOUNT2_PSPID)%'
            - '%env(OGONE_STATUS_API_ACCOUNT2_PSWD)%'

    SonVideo\Erp\Client\MailjetApiTransactionalClient:
        class: App\Tests\Mock\Erp\Client\MailjetApiTransactionalClient
        arguments:
            - '%env(string:MAILJET_TRANSACTIONAL_API_KEY)%'
            - '%env(string:MAILJET_TRANSACTIONAL_API_SECRET)%'
            - '%env(bool:MAILJET_USE_API)%'

    #
    # Carrier
    #
    SonVideo\Erp\Carrier\Client\EnvoiDuNetClient:
        class: App\Tests\Mock\Carrier\Client\EnvoiDuNetClient
        arguments: [ '@oneup_flysystem.mount_manager' ]

    SonVideo\Erp\Carrier\Client\ChronopostClient:
        class: App\Tests\Mock\Carrier\Client\ChronopostClient
        arguments: [ '@oneup_flysystem.mount_manager' ]

    SonVideo\Erp\Carrier\Client\MondialRelayClient:
        class: App\Tests\Mock\Carrier\Client\MondialRelayClient
        arguments: [ '@oneup_flysystem.mount_manager' ]

    SonVideo\Erp\Client\CarrierV2Client:
        class: App\Tests\Mock\Carrier\CarrierV2Client

    App\Tests\Mock\ElasticSearchMockHandler:
        class: App\Tests\Mock\ElasticSearchMockHandler

    App\Tests\Mock\Erp\Mailing\DummyEmailDispatcher:
        class: App\Tests\Mock\Erp\Mailing\DummyEmailDispatcher

    Elasticsearch\ClientBuilder:
        class: Elasticsearch\ClientBuilder
        calls:
            - [ 'setHosts', [ [ 'localhost' ] ] ]
            - [ 'setHandler', [ '@App\Tests\Mock\ElasticSearchMockHandler' ] ]

    Elasticsearch\Client:
        class: Elasticsearch\Client
        factory: [ '@Elasticsearch\ClientBuilder', 'build' ]

    SonVideo\Erp\Article\Manager\ArticleMediaVariations:
        arguments:
            $assets_cdn_images: '%kernel.project_dir%/var/test/svd-statics/s3'
