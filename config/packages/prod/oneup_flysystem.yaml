# Read the documentation: https://github.com/1up-lab/OneupFlysystemBundle/tree/master/Resources/doc/index.md
oneup_flysystem:
    adapters:
        # on distant server, the default filesystem is on s3
        # this is due to the application being load balanced which does not guarantee
        # that a file uploaded locally will be read on the same instance
        default_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(ERP_S3_DEFAULT_BUCKET_NAME)%'
                prefix: '%env(ERP_S3_DEFAULT_BUCKET_PREFIX)%'
        uploads_s3_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(ERP_S3_DEFAULT_BUCKET_NAME)%'
                prefix: '%env(ERP_S3_DEFAULT_BUCKET_UPLOADS_PREFIX)%'
        cilo_ftp_adapter:
            ftp:
                host: '%env(CILO_FTP_HOST)%'
                username: '%env(CILO_FTP_USERNAME)%'
                password: '%env(CILO_FTP_PASSWORD)%'
        cilo_s3_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(CILO_S3_BUCKET)%'
                prefix: '%env(CILO_S3_PREFIX)%'
        wiser_ftp_adapter:
            ftp:
                host: '%env(WISER_FTP_HOST)%'
                username: '%env(WISER_FTP_USERNAME)%'
                password: '%env(WISER_FTP_PASSWORD)%'
        wiser_ftp_svd_adapter:
            ftp:
                host: '%env(WISER_FTP_SVD_HOST)%'
                username: '%env(WISER_FTP_SVD_USERNAME)%'
                password: '%env(WISER_FTP_SVD_PASSWORD)%'
        wiser_ftp_easylounge_adapter:
            ftp:
                host: '%env(WISER_FTP_EASYLOUNGE_HOST)%'
                username: '%env(WISER_FTP_EASYLOUNGE_USERNAME)%'
                password: '%env(WISER_FTP_EASYLOUNGE_PASSWORD)%'
        wiser_backup_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(WISER_S3_BUCKET)%'
                prefix: '%env(WISER_S3_PREFIX)%'
        gfk_ftp_adapter:
            ftp:
                host: '%env(GFK_FTP_HOST)%'
                username: '%env(GFK_FTP_USERNAME)%'
                password: '%env(GFK_FTP_PASSWORD)%'
        sticker_s3_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(STICKER_S3_BUCKET)%'
                prefix: '%env(STICKER_S3_PREFIX)%'
        legacy_s3_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(ERP_BACKOFFICE_S3_BUCKET)%'
        legacy_france_express_s3_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(ERP_BACKOFFICE_S3_BUCKET)%'
                prefix: '%env(TRANSFER_FRANCE_EXPRESS_S3_PREFIX)%'
        svd_statics_s3_adapter:
            awss3v3:
                client: 'app.s3_client.statics'
                bucket: '%env(SVD_STATICS_S3_BUCKET_NAME)%'
        mondial_relay_ftp_adapter:
            ftp:
                host: '%env(MONDIAL_RELAY_HOST)%'
                username: '%env(MONDIAL_RELAY_USERNAME)%'
                password: '%env(MONDIAL_RELAY_PASSWORD)%'
        mondial_relay_s3_adapter:
            awss3v3:
                client: 'app.s3_client'
                bucket: '%env(MONDIAL_RELAY_S3_BUCKET)%'
                prefix: '%env(MONDIAL_RELAY_S3_PREFIX)%'
        group_digital_ftp_adapter:
            ftp:
                host: '%env(GROUP_DIGITAL_FTP_HOST)%'
                username: '%env(GROUP_DIGITAL_FTP_USERNAME)%'
                password: '%env(GROUP_DIGITAL_FTP_PASSWORD)%'
                ssl: '%env(bool:GROUP_DIGITAL_FTP_USE_SSL)%'
