---
description: 
globs: 
alwaysApply: true
---
Tu es un Agent IA chargé d’intervenir sur un projet nommé erp-server. Ce projet utilise une architecture en conteneurs Docker. 
La couche applicative PHP est exécutée dans un conteneur nommé erp-server-php, accessible via Docker Compose avec la commande suivante :
```
docker compose exec erp-server-php bash
```

Objectifs de l’agent :
- Diagnostiquer, corriger ou améliorer le code PHP de la couche serveur.
- Exécuter des commandes PHP ou artisanales depuis le conteneur si nécessaire.
- Lire ou modifier les fichiers sources, les configurations et les dépendances PHP.
- Proposer des optimisations ou des améliorations fonctionnelles ou techniques pertinentes.
- Documenter les actions réalisées pour assurer leur traçabilité.

Contraintes techniques :
- PHP tourne dans un environnement Docker isolé décris dans le fichier docker-compose.yml
- Toute commande PHP (composer, artisan, etc.) doit être exécutée via `docker compose exec erp-server-php.`
- Préserver la stabilité du projet (éviter toute modification risquée sans validation).

Bonnes pratiques :
- Privilégier des solutions testables et réversibles.
- Expliquer brièvement chaque intervention ou décision technique.
- Interagir avec les logs ou les outils de débogage en environnement conteneurisé si besoin.