{"name": "son-video/sisyphus", "type": "project", "license": "proprietary", "repositories": [{"type": "vcs", "url": "**************:son-video/data-warehouse-schema.git"}, {"type": "vcs", "url": "**************:son-video/shared/erp-pg-schema.git"}, {"type": "vcs", "url": "**************:son-video/shared/hal-middleware-bundle.git"}, {"type": "vcs", "url": "**************:son-video/svd-rpc-bundle"}, {"type": "vcs", "url": "**************:son-video/synapps-client.git"}, {"type": "vcs", "url": "**************:son-video/phcdb.git"}, {"type": "vcs", "url": "**************:son-video/shared/orm.git"}], "require": {"php": "^7.4", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-pdo": "*", "ext-soap": "*", "ext-xmlreader": "*", "adbario/php-dot-notation": "^2.5", "doctrine/annotations": "^1.14", "doctrine/dbal": "^2.13", "doctrine/doctrine-bundle": "^2.7", "elasticsearch/elasticsearch": "^7.13", "endroid/qr-code": "^3.5", "firebase/php-jwt": "^6.2", "friendsofsymfony/rest-bundle": "^2.5", "league/commonmark": "^1.0", "league/csv": "^9.2", "league/flysystem-aws-s3-v3": "^1.0", "mailjet/mailjet-apiv3-php": "^1.5", "nelmio/api-doc-bundle": "^4.11", "oneup/flysystem-bundle": "^3.1", "php-http/guzzle6-adapter": "^2.0", "php-http/message-factory": "^1.1", "phpdocumentor/reflection-docblock": "^5.3", "picqer/php-barcode-generator": "^2.4", "sentry/sentry-symfony": "^4.14", "son-video/hal-middleware-bundle": "2.0.*", "son-video/orm": "^v0.0", "son-video/svd-rpc-bundle": "^1.1", "son-video/synapps-client": "^1.1", "statamic/stringy": "^3.1", "swiftmailer/swiftmailer": "^6.2", "symfony/asset": "4.4.*", "symfony/console": "4.4.*", "symfony/css-selector": "4.4.*", "symfony/dom-crawler": "4.4.*", "symfony/dotenv": "4.4.*", "symfony/expression-language": "4.4.*", "symfony/flex": "^1.3.1", "symfony/framework-bundle": "4.4.*", "symfony/lock": "4.4.*", "symfony/monolog-bundle": "^3.4", "symfony/property-access": "4.4.*", "symfony/property-info": "4.4.*", "symfony/security-bundle": "4.4.*", "symfony/serializer": "4.4.*", "symfony/templating": "4.4.*", "symfony/twig-bundle": "4.4.*", "symfony/validator": "4.4.*", "symfony/yaml": "4.4.*", "thecodingmachine/gotenberg-php-client": "^6.1", "twig/twig": "^2.11"}, "require-dev": {"ext-simplexml": "*", "atoum/atoum": "^3.4.2", "behat/behat": "^3.5", "behat/mink": "^1.9", "behat/mink-extension": "^2.3", "behat/mink-goutte-driver": "^2.0", "behat/symfony2-extension": "^2.1", "behatch/contexts": "^3.2", "dg/bypass-finals": "^1.8", "friendsofphp/php-cs-fixer": "^3.4", "mockery/mockery": "^1.6", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-mockery": "^2", "phpstan/phpstan-symfony": "^2.0", "phpunit/phpunit": "^9.6", "rector/rector": "^2.0.0-rc2", "son-video/data-warehouse-schema": "dev-master", "son-video/erp-pg-schema": "dev-master", "son-video/phcdb": "dev-2359_kpi_safety_stock", "symfony/browser-kit": "4.4.*", "symfony/debug-bundle": "4.4.*", "symfony/filesystem": "4.4.*", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "4.4.*", "symfony/web-profiler-bundle": "4.4.*"}, "config": {"preferred-install": {"*": "dist"}, "github-token": {"github.com": "****************************************"}, "gitlab-token": {"gitlab.com": "**************************"}, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "php-http/discovery": false}}, "autoload": {"psr-4": {"App\\": "src/App/", "SonVideo\\": "src/SonVideo/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\Acceptance\\": "tests/Acceptance/", "App\\Tests\\Mock\\": "tests/Mock/", "App\\Tests\\Unit\\": "tests/Unit/App/", "App\\Tests\\Utils\\": "tests/Utils/", "SonVideo\\Erp\\Tests\\Unit\\": "tests/Unit/SonVideo/Erp/", "SonVideo\\Carrier\\Tests\\Unit\\": "tests/Unit/SonVideo/Carrier/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "4.4.*"}}}