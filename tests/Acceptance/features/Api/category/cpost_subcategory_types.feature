Feature: Get list of subcategory types
  As someone identified in the ERP
  I want to see the subcategory types

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "category/categories.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategory-types"
    Then  the response status code should be 401

  Scenario: Get list of subcategory types without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategory-types"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->subcategory_types" should have 3 elements

  Scenario: Get list of subcategory types filtered by an arbitrary type
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategory-types" with body:
    """
    {
      "where": {
        "type": {
          "_eq": "HIFI"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->subcategory_types" should have 1 elements
    And   the JSON node "data->subcategory_types[0]->type" should be equal to "HIFI"
    And   the JSON node "data->subcategory_types[0]->label" should be equal to "univers HIFI"
