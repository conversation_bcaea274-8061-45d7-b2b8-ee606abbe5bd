Feature: Add a category to a category

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "category/categories.sql"
    And   I send a "POST" request to "/api/v1/category"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/category"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    When  I send a "POST" request to "/api/v1/category"
    Then  the response status code should be 403

  Scenario: Test to create category with name already exist
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/category" with body:
    """
    {
      "category_name": "Enceintes",
      "domain_id": 15
    }
    """
    Then  the response status code should be 409

  Scenario: Create category success
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/category" with body:
    """
    {
      "category_name": "toto",
      "domain_id": 13
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->category_id" should be equal to the number "138"
