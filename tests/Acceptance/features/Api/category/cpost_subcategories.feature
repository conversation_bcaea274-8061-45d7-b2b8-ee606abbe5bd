Feature: Get list of subcategories
  As someone identified in the ERP
  I want to see the subcategories and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "category/categories.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategories"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategories"
    Then  the response status code should be 401

  Scenario: Get list of subcategories without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategories"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data->subcategories" should have 4 elements

  <PERSON><PERSON><PERSON>: Get list of subcategories filtered by an arbitrary id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/subcategories" with body:
    """
    {
      "where": {
        "subcategory_id": {
          "_eq": 144
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->subcategories" should have 1 element
    And   the JSON node "data->subcategories[0]->subcategory_id" should be equal to "144"
    And   the JSON node "data->subcategories[0]->name" should be equal to "Pieds d'enceintes"
    And   the JSON node "data->subcategories[0]->parent_category_id" should be equal to "59"
    And   the JSON node "data->subcategories[0]->parent_category_name" should be equal to "Meubles et supports"
    And   the JSON node "data->subcategories[0]->associated_products" should be equal to "0"
    And   the JSON node "data->subcategories[0]->associated_products_on_sale" should be equal to "0"
    And   the JSON node "data->subcategories[0]->outsize" should be equal to "0"
    And   the JSON node "data->subcategories[0]->charged_delivery" should be equal to "9.99"
    And   the JSON node "data->subcategories[0]->warranty_type" should be equal to "SON"
    And   the JSON node "data->subcategories[0]->bbac_subtype_name" should be equal to "Accessoires enceinte"
    And   the JSON node "data->subcategories[0]->bbac_subtype_id" should be equal to 8
    And   the JSON node "data->subcategories[0]->subcategory_type" should be equal to "HIFI"
    And   the JSON node "data->subcategories[0]->seller_commission_config" should have 2 elements
    And   the JSON node "data->subcategories[0]->custom_code" should be equal to "87654321"
    And   the JSON node "data->subcategories[0]->ecotax_code" should be equal to "55555"
