Feature: Update quantity of products in a location
  In order to manage product's quantities in locations through API
  As a user with enough rights
  I need to be able to make corrections on these quantities

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "locations/put_location_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": 2,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": 2,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": 2,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Failed on negative quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": -1,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 400

  Scenario: Failed if comment is too short
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": 2,
      "comment": "pouet"
    }
    """
    Then  the response status code should be 400

  Scenario: Failed if quantity is below the unused quantity of products (= not linked to delivery ticket nor mission)
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": 2,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 1003
    And   the JSON node "data" should have 3 elements
    And   the JSON node "message" should be equal to the string
    """
    Corrected quantity does not cover locked quantity, should be higher than "4".
    """
    And   the JSON node "data->computed_diff_quantity" should be equal to the number "-11"
    And   the JSON node "data->computed_unused_quantities" should be equal to the number "9"
    And   the JSON node "data->computed_locked_quantities" should be equal to the number "4"

  Scenario: Do the correction on usable products
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/1" with body:
    """
    {
      "quantity": 10,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->quantities" should have 3 elements
    And   the JSON node "data->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->quantities[0]->quantity" should be equal to the number "6"
    And   the JSON node "data->quantities[1]->delivery_ticket_id" should be null
    And   the JSON node "data->quantities[1]->move_mission_id" should be equal to the number "1"
    And   the JSON node "data->quantities[1]->quantity" should be equal to the number "1"
    And   the JSON node "data->quantities[2]->delivery_ticket_id" should be equal to the number "4263254"
    And   the JSON node "data->quantities[2]->move_mission_id" should be null
    And   the JSON node "data->quantities[2]->quantity" should be equal to the number "3"

  Scenario: Do the correction on another warehouse than the 1
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81078/location/3" with body:
    """
    {
      "quantity": 10,
      "comment": "finalement on a plein de trucs ici"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->quantities" should have 1 element
    And   the JSON node "data->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->quantities[0]->quantity" should be equal to the number "10"

  Scenario: Correction on a stock B product cannot be greater than 1
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/1/location/3" with body:
    """
    {
      "quantity": 2,
      "comment": "finalement on a plein de trucs ici"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 1001
    And   the JSON node "data" should have 0 elements
    And   the JSON node "message" should be equal to the string
    """
    Too much quantity for product in stock B
    """

  Scenario: Correction on a stock B product cannot be greater than 1 on the entire database
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/1/location/3" with body:
    """
    {
      "quantity": 1,
      "comment": "finalement on a plein de trucs ici"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 1002
    And   the JSON node "data" should have 0 elements
    And   the JSON node "message" should be equal to the string
    """
    Stock B already exists for this product
    """

  Scenario: Failed if total quantity would drop under the delivery notes requirements
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/143088/location/1" with body:
    """
    {
      "quantity": 1,
      "comment": "oups, il manque des produits ici"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 1004
    And   the JSON node "data" should have 4 elements
    And   the JSON node "message" should be equal to the string
    """
    Corrected quantity does not cover stock needs for the following delivery notes: "1111111"
    """
    And   the JSON node "data->delivery_needs" should be equal to the number "3"
    And   the JSON node "data->qty_in_warehouse" should be equal to the number "3"
    And   the JSON node "data->delivery_notes" should be equal to the string
    """
    1111111
    """
    And   the JSON node "data->computed_diff_quantity" should be equal to the number "-1"

  @clear-database
  Scenario: Fail to correct a stock linked to a supplier order that do not exists
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "locations/put_location_products.sql"
    And   I load mysql fixtures from file "wms/product/put_product_location_with_supplier_order.sql"
    Then  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81079/location/1" with body:
    """
    {
      "quantity": 1,
      "comment": "DUMMY_COMMENT_OVERRIDDEN",
      "supplier_order_id": 666
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    Le produit "81079" n'a pas été trouvé dans la commande fournisseur "666"
    """

  Scenario: Fail to correct a stock linked to a supplier order because because of the order status
    Then  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81079/location/1" with body:
    """
    {
      "quantity": 1,
      "comment": "DUMMY_COMMENT_OVERRIDDEN",
      "supplier_order_id": 1
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    La mise à jour de la quantité livrée n'est pas autorisée sur une commande fournisseur en statut: "annulee"
    """

  Scenario: Fail to correct a stock linked to a supplier order because it would exceed the ordered quantity
    Then  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81079/location/1" with body:
    """
    {
      "quantity": 3,
      "comment": "DUMMY_COMMENT_OVERRIDDEN",
      "supplier_order_id": 3
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    La quantité livrée sur la commande fournisseur "1 (quantité livrée) + 1 (correction)" ne peut pas excéder la quantité commandée "1"
    """

  Scenario: Fail to correct a stock linked to a supplier order because the delivered quantity would be negative
    Then  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81079/location/1" with body:
    """
    {
      "quantity": 0,
      "comment": "DUMMY_COMMENT_OVERRIDDEN",
      "supplier_order_id": 3
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    La quantité livrée sur la commande fournisseur "1 (quantité livrée) -2 (correction)" ne peut pas être négative
    """

  Scenario: Fail to correct a stock linked to a supplier order because it does not contains the given product
    Then  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81079/location/1" with body:
    """
    {
      "quantity": 0,
      "comment": "DUMMY_COMMENT_OVERRIDDEN",
      "supplier_order_id": 2
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    Le produit "81079" n'a pas été trouvé dans la commande fournisseur "2"
    """

  Scenario: Fail to correct a stock linked to a supplier order because it does not contains the given product
    Then  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/81080/location/1" with body:
    """
    {
      "quantity": 0,
      "comment": "DUMMY_COMMENT_OVERRIDDEN",
      "supplier_order_id": 2
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "success"
    And   the JSON node "data->quantities" should have 0 element

  Scenario: Correction on an auto-picked product fails if the target location is not virtual
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/139789/location/1" with body:
    """
    {
      "quantity": 2,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 1005
    And   the JSON node "message" should be equal to '"1" is not an appropriate location for an auto picked product'

  Scenario: Correction on an auto-picked product succeeds if target location is virtual
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/product/139789/location/3" with body:
    """
    {
      "quantity": 2,
      "comment": "produit non trouvé"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "success"