Feature: Get list of pricing strategy products

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "pricing_strategy/pricing_strategy.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy/products"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy/products"
    Then  the response status code should be 401

  Scenario: Check successful response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy/products" with body:
    """
    {
      "where": {
        "pricing_strategy_id": {
          "_eq": "5"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->pricing_strategy_products" should have 2 element
    And   the JSON node "data->pricing_strategy_products[1]" should be identical to
    """
    {
        "pricing_strategy_id": 5,
        "sku": "LBCLD25BP",
        "article_id": 81123,
        "image": "/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg",
        "is_margin_rate_to_low": false,
        "lowest_competitor": null,
        "article_name": "La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130",
        "selling_price": 389,
        "stock": 2,
        "category": "Meubles et supports",
        "margin": 0.838,
        "margin_rate": 0.456,
        "margin_tax_excluded": 147.82,
        "last_scrapping_date": null,
        "purchase_price": 195,
        "sorecop": 0,
        "ecotax": 0,
        "promo_budget_amount": 0,
        "unconditional_discount": 0,
        "weighted_cost_tax_excluded": 176.35,
        "status": "oui",
        "delay": 0,
        "pvgc": 389,
        "new_prices": []
    }
    """

  Scenario: Check successful response with filter
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy/products" with body:
    """
    {
      "where": {
        "in_strategy": {
          "_eq": "1"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->pricing_strategy_products" should have 2 element
    And   the JSON node "data->pricing_strategy_products[0]" should be identical to
    """
    {
       "pricing_strategy_id": 5,
       "sku": "ARCAMRBLINKNR",
       "article_id": 81078,
       "image": "/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg",
       "is_margin_rate_to_low": false,
       "lowest_competitor": null,
       "article_name": "Arcam rBlink",
       "selling_price": 249,
       "stock": 3,
       "category": "Distributeurs et transmetteurs",
       "margin": 0.576,
       "margin_rate": 0.366,
       "margin_tax_excluded": 75.8,
       "last_scrapping_date": "2024-06-01 08:35:00",
       "purchase_price": 131.58,
       "sorecop": 0,
       "ecotax": 0.15,
       "promo_budget_amount": 0,
       "unconditional_discount": 0,
       "weighted_cost_tax_excluded": 131.58,
       "status": "oui",
       "delay": 0,
       "pvgc": 249,
       "new_prices": []
    }
    """
