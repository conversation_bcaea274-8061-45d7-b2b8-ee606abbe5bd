Feature: Get list of pricing strategies

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "pricing_strategy/pricing_strategy.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategies"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategies"
    Then  the response status code should be 401

  Scenario: Check successful response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategies" with body:
    """
    {
      "where": {
        "activation_status": {
          "_eq": "CREATED"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data->pricing_strategies" should have 4 element
    And   the JSON node "data->pricing_strategies[0]" should be identical to
    """
    {
      "pricing_strategy_id": 5,
      "name": "strat de chokbar",
      "starts_at": "2024-06-01 10:00:00",
      "ends_at": "2032-06-02 10:00:00",
      "activation_status": "CREATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 5,
      "weekend_increment_amount": 0 ,
      "weekend_min_margin_rate": 5,
      "created_at": "2024-06-01 08:30:00",
      "updated_at": "2024-06-01 08:35:00",
      "competitors": [],
      "sales_channels": [],
      "products": [],
      "count_products": 2
    }
    """
