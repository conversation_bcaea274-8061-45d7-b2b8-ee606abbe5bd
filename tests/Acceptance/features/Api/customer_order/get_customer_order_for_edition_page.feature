Feature: get customer order
  In order to manage a customer order through the API
  As a user
  I need to be able to retrieve information about a customer order

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I send a "GET" request to "/api/v1/customer-order/1/for-edition-page"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/customer-order/1/for-edition-page"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/customer-order/1/for-edition-page"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Load information on a customer order
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 1, "cost": 9.9}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/1/for-edition-page"
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to "1"
    And   the JSON node "data->has_inconsistent_carrier" should be false

  Scenario: Load information on a customer order with an inconsistent shipment method id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 11, "cost": 9.9},
  {"shipment_method_id": 22, "cost": 9.9}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/1/for-edition-page"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to "1"
    And   the JSON node "data->has_inconsistent_carrier" should be true

  Scenario: Load information on an amazon business and duty free customer order
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 1, "cost": 9.9}
]
        """
    And   I send a "GET" request to "/api/v1/customer-order/3/for-edition-page"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->tags[0]" with ignored columns "modified_at,remitted_at" should be identical to
    """
    {
        "meta": {
            "HT": true
        },
        "name": "amazon_business",
        "label": "Amazon Business",
        "taxonomy_meta": null
    }
    """
