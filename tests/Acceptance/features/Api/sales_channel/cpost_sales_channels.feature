Feature: Get list of sales channels
  As someone identified in the ERP
  I want to see the articles and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel"
    Then  the response status code should be 401

  Scenario: Get list of sales channels without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "12"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->sales_channels" should have 12 elements

  Scenario: Attempt to load list but provide an incorrect dependency
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel" with body:
    """
    {
      "included_dependencies": ["imposteur"]
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Optional column with "imposteur" does not exists, Available keys are : "statistics_sales_channel".
    """

  Scenario: Get list of sales channels filtered on id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-channel" with body:
    """
    {
      "where": {
        "sales_channel_id": {
          "_eq": 1
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->sales_channels" should have 1 elements
    And   the JSON node "data->sales_channels[0]->sales_channel_id" should contain "1"
    And   the JSON node "data->sales_channels[0]->label" should contain "son-video.com"
    And   the JSON node "data->sales_channels[0]->legacy_name" should contain "site"
    And   the JSON node "data->sales_channels[0]->display_order" should contain "1"
    And   the JSON node "data->sales_channels[0]->average_commission_rate" should contain "11.2"
    And   the JSON node "data->sales_channels[0]->minimum_margin_rate" should contain "0"
    And   the JSON node "data->sales_channels[0]->minimum_available_quantity" should contain "0"
    And   the JSON node "data->sales_channels[0]->minimum_selling_price" should contain "0"
    And   the JSON node "data->sales_channels[0]->maximum_selling_price" should contain "20000"
    And   the JSON node "data->sales_channels[0]->statistics_sales_channel" should be null
