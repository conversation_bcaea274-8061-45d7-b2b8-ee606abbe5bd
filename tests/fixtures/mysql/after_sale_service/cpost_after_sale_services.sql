INSERT INTO backOffice.sf_guard_user(id, username, algorithm, salt, password, created_at, last_login, is_active,
                                     is_super_admin)
VALUES (1000, 'backoffice', 'none', '', '', now(), now(), 1, 1)
;

INSERT INTO backOffice.sf_guard_user_profile (id, societe, site, usr_societe, titre, civilite, nom, prenom, email,
                                              poste, poste_sda, employe, signature)
VALUES (1000, 'Son Video Distribution', 'Champigny', 1, 'Systéme', 'M.', 'Admin', 'AI', '<EMAIL>',
        '666', 1, 1, 'La matrice')
;

INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste,
                             cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport,
                             transport_offre_speciale)
VALUES (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;

INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page,
                                        menu, comprendre_pour_choisir, meta_description, presentation)
VALUES (3, 1, 0, 'Haute-fidélité', 0, 'Audio Vidéo', 2, '/Conseil/Hifi/Hifi.html', 1, 1, '', null),
       (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', ''),
       (13, 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null),
       (15, 1, 0, 'Enceintes', 0, 'tmp', 3, '/Enceintes', 1, 1, '', '')
;

INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids,
                                          neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id,
                                          garantie_2, garantie_5, garantie_5_bis, garantie_vc_1, garantie_vc_2,
                                          videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva,
                                          export, section, url_section, typologie, critere_section_sommaire,
                                          export_amazon, keyword_amazon, categorie_amazon, hors_gabarit,
                                          hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id,
                                          mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
VALUES (59, 1, 0, 1, 0, 0, 1, 'Meubles et supports', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 15.00,
        0.200, 'oui', '', '', '', 'marque', '1', 'home cinema', 'TV, DVD, Home Cinéma', 'Y', 0, '85299041',
        '/Rayons/Accessoires/MeubleHifi.html', 3760, 0, 0, '610', '8529904900'),
       (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 11,
        9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080',
        '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080'),
       (137, 1, 1, 1, 1, 0, 1, 'Enceintes', '', 15, 'oui', 'oui', 0, 'non', 'non', 'non', 'non', 1, 0.00, 0.200, 'oui',
        '', '', '', 'marque', '0', null, null, 'Y', 17, null, '/Rayons/Hifi/Enceintes/CatEnceintes1.html', null, 0, 0,
        '613', '85182200'),
       (18, 1, 0, 1, 0, 0, 1, 'Câbles audio', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 7.90, 0.200, 'oui',
        '', '', '', 'marque', '1', 'câble', 'MP3, Audio portable, Hi-fi', 'N', null, '85444991',
        '/Rayons/Cables-audio.html', 118, 0, 1, '610', '85444991')
;

INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;

INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture,
                                              port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav,
                                              id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay,
                                              pixmania_segment_id, hors_gabarit, illustration,
                                              redoute_nomenclature_node)
VALUES (95, 1, 1, 'Enceintes encastrables', '/Rayons/HomeCinema/EnceintesAV/Inwall.html', 7.99, 0.200, 137, 1,
        'MC-11226', '/Parts/Nav/NavR_EnceintesEncastrees.html', 3, 13, '93382', '3', 106, 0,
        'http://www.son-video.com/images/dynamic/Enceintes_encastrables/articles/Artsound/ARTSFL101/Artsound-FL101_P_140.jpg',
        ''),
       (144, 1, 1, 'Pieds d''enceintes', '/Rayons/Accessoires/PiedsEnceinte.html', 9.99, 0.200, 59, 1, 'MC-5335',
        '/Parts/Nav/NavR_Pied_Enceinte.html', 3, 13, '137923', '3', 3764, 0,
        'http://www.son-video.com/images/dynamic/Supports/articles/NorStone/NORSTSTYLUM2NR/NorStone-Stylum-2-Noir_P_180.jpg',
        ''),
       (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null,
        null, null, null, '79323', '1', 9630, 0,
        'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg',
        null),
       (56, 1, 1, 'Câbles d''enceintes', '/Rayons/Cables/EspaceCable/CablesA_Enceintes.html', 5.99, 0.200, 18, 1,
        'MC-4702', '/Parts/Nav/NavR_Cable_Enceintes.html', 3, 13, '137917', '1', 118, 0,
        'http://www.son-video.com/images/dynamic/Cables_audio/composes/NORSTCL40010M/NorStone-CL400-Classic-2-x-4-mm2-10-m-_P_180.jpg',
        '')
;

INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type,
                                derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva,
                                V_taux_marge, V_taux_marque, V_marge)
VALUES (1, 1, 154357533114364, 81078, 'ARCAMRBLINKNR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576,
        0.366, 75.80),
       (1, 1, 617301053011139, 81123, 'LBCLD25BP', 'article', '2019-07-25 19:02:14', 144, 59, 6, 0.200, 0.838, 0.456,
        147.82),
       (1, 1, 848803876758370, 128416, 'BWCCM74', 'article', '2018-06-19 16:48:33', 95, 137, 15, 0.200, 0.818, 0.450,
        337.50),
       (1, 1, 57384874725141, 13895, 'NORSTCL81123M', 'compose', '2019-09-03 20:04:50', 56, 18, 6, 0.200, 1.214, 0.548,
        20.33)
;

INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte,
                                    id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte,
                                    encours_maximum, encours_consomme, marque_disponible, marque_en_vente,
                                    siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax,
                                    siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse,
                                    siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile,
                                    commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile,
                                    comptabilite_email, technique_contact, technique_telephone, technique_mobile,
                                    technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen,
                                    frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape,
                                    hors_delai_auto, reliquat_attente_auto)
VALUES (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
       (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
       (943050784937153, 400, 'LA BOITE CONCEPT', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;

INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc,
                               url_source_image, specialite, produit_de_reference, gamme_qualite, public,
                               type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif,
                               prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis,
                               V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES (864984545730971, 262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '',
        '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
       (720253785811601, 292, 'B&W', 'http://www.son-video.com/images/static/marques/Bowers_et_Wilkins.gif', 'oui', '',
        '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'false', 27828, 246, 4.736, 239, 0, '', '', ''),
       (485762893674674, 959, 'La Boite Concept', 'http://www.son-video.com/images/static/marques/La-boite-concept.gif',
        'oui', 'La Boite Concept', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2,
        4.000, 1, 0, '', '', ''),
       (940157217849192, 520, 'NorStone', 'http://www.son-video.com/images/static/marques/Norstone.gif', 'oui',
        'Inovadis', '', '', 'Eric', '', '', 5, '', '', '', '', 67, 0.000, 0.000, 'oui', 'true', 2789, 1451, 4.597, 1418,
        0, 'norstone,câble,audio,vidéo,HDMI,meuble,hi-fi,TV,support,pieds,cablage,câblage', '',
        'Tous les produits NorStone : câble enceinte, câble audio vidéo, HDMI, meuble hi-fi, meuble vidéo, meuble TV avec support, support TV mural et pieds d''enceinte.')
;

INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41'),
       (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1,
        '2019-03-01 10:35:41')
;

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status,
                                prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente,
                                prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids,
                                poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur,
                                id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur,
                                V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit,
                                V_qte_dispo_resa, V_delai_lvr, V_delai_lvr_ferme, etat_statut, etat_devalorisation,
                                etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert,
                                V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page,
                                url_image, comparateur, recherche, description_panier, description_courte, diagonale,
                                description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max,
                                prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine,
                                code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage,
                                compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants,
                                fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre,
                                rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost,
                                is_main)
VALUES ('2019-09-03 01:06:38', 1, 0, 81078, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00,
        0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null,
        null, null, null, 0, 0, 0, 0, 4, 2, '',
        'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html',
        'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg',
        'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00,
        199.00, '2015-03-24', 67,
        '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6,
        15, 2, 0, 1, 1),
       ('2019-07-26 00:34:00', 1, 0, 81123, '2013-02-21', 'oui', 195.00, 176.35, 0.00, 389.00, 389.00, 0.00, 0.00, 0.00,
        8.450, 'N', 1, 1, 'N', null, 'LD-F-25mm-N-P', 400, 959, 'Pieds Noir laqué pour station HiFi LD120 / LD130',
        'Boîte Concept Pieds LD120 LD130', 1, 0.00, 2, 0, 'a', null, 2, 0, 1, null, null, null, null, 0, 0, 0, 0, 0, 2,
        '', 'http://www.son-video.com/Rayons/Enceintes-Multimedia/La-Boite-Concept-pieds-LD120-LD130.html',
        'http://www.son-video.com/images/dynamic/Enceintes/articles/La_Boite_Concept/LBCLD25BP/La-Boite-Concept-Pieds-pour-station-HiFi-LD120-Noir-laque_P_180.jpg',
        'oui', 'oui', 'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130',
        'Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)', 0, '', 0.000, 0.000, 0, 0, 0.00, 389.00,
        '2014-10-14', 67,
        '85182200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, '', 0, 23785814, null, null, null, 0, 0.0, 1, 0, 0, 0,
        2, 0, 1, 1),
       ('2018-06-20 00:34:00', 1, 0, 128416, '2018-06-19', 'tmp', 412.50, null, 0.00, 900.00, 900.00, 0.00, 0.00, 0.00,
        4.000, 'Y', 1, 1, 'N', null, null, 1, 292, 'BWCCM74', null, 1, 0.00, 0, 0, 'a', null, 0, null, 0, null, null,
        null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'non', 'Enceinte encastrable BW CCM 7.4', 'Arcam BW CCM', 0,
        '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1,
        null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1),
       ('2019-09-04 00:34:01', 1, 0, 13895, '2003-09-22', 'oui', 16.75, 16.75, 0.00, 45.00, 62.50, 0.50, 0.00, 0.00,
        2.000, 'N', 1, 1, 'N', null, null, 1, 520, 'CL250 Classic 2,5 mm2 (25 m)', null, 1, 25.00, 235, 0, 'a', null,
        183, 0, 1, null, null, null, null, 0, 0, 0, 0, 0, 0, '',
        'http://www.son-video.com/Rayons/Cables/EspaceCable/CablesA-Enceintes-NorStone-CL250-Classic.html',
        'http://www.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'non', 'oui',
        'Câble d''enceintes Norstone Audio CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 25 m',
        'NorStone CL250 Classic (25 m)', 0, null, 0.000, 0.000, 0, 0, 0.00, 0.00, null, null,
        null, null, 0.00, 0.000, 0.00, 0.00, 1, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 2, 0,
        1, 1)
;

INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste,
                                     ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai,
                                     prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES (20030702191152, 2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1,
        'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000,
        0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', '', 5),
       (20030702191206, 5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null,
        1),
       (0, 14, 'EDP8', 'Emport Depot Paris 8', 'non', 0, 2, 0, 'Emport Depot Paris 8', '', '', 0.000, 0.000, '0', 0.000,
        0, null, null, 1),
       (0, 31, 'EDNA', 'Emport Depot Nantes', 'non', 0, 2, 0, 'Emport Depot Nantes', '', '', 0.000, 0.000, '0', 0.000,
        0, null, null, 1)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type,
                                         mono_colis, spidy_tracking_number_mask)
VALUES (1, 2, '6C', 'Colissimo domicile', 1, 'Livraison sous 48/72h au domicile remis contre signature', 'messagerie',
        1, null)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible,
                                     generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible,
                                     expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone,
                                     email, id_user, description, surcout_emport, product_emport, nom_transfert,
                                     nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre,
                                     horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code,
                                     tpe_id, abreviation)
VALUES (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67,
        '0155091779', '<EMAIL>', 1,
        'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.',
        null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne',
        'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi',
        'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
       (2, 'Paris 8e', 14, 0, 0, 0, 0, 0, '1 Avenue de Friedland', '75008', 'Paris', 67, '0155091888',
        '<EMAIL>', 1,
        'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.',
        null, null, 'Magasin Son-Vidéo.com', 'magasin (Paris 8e)', '1 avenue de Friedland', 'Paris 8e',
        'https://goo.gl/maps/jvuXoxj8gms', 'ouvert du lundi au vendredi, 10h - 19h', 99, 'du lundi au vendredi',
        'de 10h &agrave; 19h', null, 1, 1, '02', 6230679, 'Par8'),
       (3, 'Havre', null, 1, 1, 0, 0, 0, 'RD 910 Le Montcriquet', '76210', 'Saint Jean de la Neuville', 67,
        '0155091830', '<EMAIL>', 1, null, null, null, null, null, 'RD 910 Le Montcriquet', null,
        null, null, 2, null, null, null, 1, 1, '01', null, 'Hav'),
       (4, 'Schenker', null, 0, 0, 0, 0, 0, 'Parc du Hode route industrielle BP263', '76430', 'St Vigor d''Ymonville',
        67, '0155091080', '<EMAIL>', 1, null, null, null, null, null,
        'Parc du Hode route industrielle BP263', null, null, null, 99, null, null, null, 0, 0, '04', null, 'Sch'),
       (5, 'Nantes', 31, 0, 0, 0, 0, 0, '9 place de la Bourse', '44100', 'Nantes', 67, '0249442402',
        '<EMAIL>', 1,
        'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.',
        null, null, 'Magasin Son-Vidéo.com', 'magasin (Nantes)', '9 place de la Bourse', 'Nantes',
        'https://goo.gl/maps/Cucfvro99n22', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi',
        'de 10h &agrave; 19h', null, 1, 1, '05', 6297732, 'Nan')
;

INSERT INTO backOffice.paiement (id_paiement, moyen, actif, operation, avoir, paiement, description, surcout,
                                 attente_paiement, creation_distante, autorisation_distante, annulation_distante,
                                 demande_remise_distante, remboursement_distant, remise_directe, declaration_impaye,
                                 interrogation_etat_distante, statut_source, garantie_source, justificatif_creation,
                                 justificatif_creation_source, justificatif_creation_type, justificatif_acceptation,
                                 justificatif_acceptation_source, justificatif_acceptation_type,
                                 justificatif_acceptation_motif, justificatif_remise, justificatif_remise_source,
                                 justificatif_remise_type, justificatif_remise_motif, bon_remise, bon_remise_motif,
                                 compte_bancaire, journal, emport_depot)
VALUES (2, 'CBS', 'N', 'paiement', 'N', 'CBOL', 'Carte de crédit en ligne sécurisée', 0.00, 'N', 'Y', 'N', 'Y', 'N',
        'Y', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE',
        'N'),
       (4, 'CHK', 'N', 'paiement,remboursement', 'N', 'Chèque', 'Chèque', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y',
        'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '^\\d{7}$', 'Y',
        'manuel', 'No cheque', '^\\d{7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
       (5, 'AUR', 'N', 'paiement', 'N', 'Aurore', 'Carte Aurore', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N',
        'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9]{1,64}$', 'Y', 'auto',
        'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (6, 'CRDT', 'N', '', 'N', 'Crédit personnalisé', 'Paiement en 3, 5, 10 fois ou plus', 0.00, 'Y', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '',
        'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (7, 'CR', 'N', 'paiement', 'N', 'CR', 'Paiement à la livraison', 10.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'manuel', 'manuel', 'N', 'manuel', 'No facture', 'Y', 'manuel', 'Confirmation CR', '^ok$', 'Y', 'manuel',
        'No releve', '^\\d{1,64}$', 'N', '', '5121', 'CC', 'N'),
       (8, 'BKDO', 'Y', 'paiement', 'N', 'Bon Kado', 'Le Bon Kado', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (9, 'VIR', 'Y', 'paiement,remboursement', 'N', 'Virement', 'Virement bancaire', 0.00, 'Y', 'N', 'N', 'N', 'N',
        'N', 'Y', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'Date de virement',
        '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'auto', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512010', 'BQ',
        'Y'),
       (10, 'DIF3M', 'N', '', 'N', 'Différé 3 mois', 'Paiement différe à 3 mois (Réflexion 3)', 0.00, 'Y', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque',
        '', 'Y', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (11, 'CTPE', 'Y', 'paiement', 'N', 'CTPE', 'Carte terminal de paiement', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^\\d{1,64}$',
        'Y', 'manuel', 'No remise', '^(?:TPE-)?\\d{1,64}$', 'N', '', '512300', 'CE', 'Y'),
       (12, 'ESP', 'Y', 'paiement,remboursement', 'N', 'Espèces', 'Espèces', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', '', 'N', 'manuel', 'No cheque', '^[A-Z0-9]{9}$', 'N', 'manuel',
        'No remise', '', 'N', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
       (13, 'MDT', 'N', '', 'N', 'Mandat', 'Mandat', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel',
        'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '',
        'N', '', '512000', 'BQ', 'N'),
       (15, 'TCMD', 'Y', 'remboursement', 'Y', 'Avoir (Transfert commande)', 'Avoir (Transfert commande)', 0.00, 'Y',
        'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel',
        'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (16, 'RCRT', 'N', '', 'N', 'Recrédit carte', 'Recrédit sur carte', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (17, 'PANT', 'Y', 'paiement', 'Y', 'Avoir (Pmt antérieur)', 'Avoir (Paiement antérieur)', 0.00, 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque',
        '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (18, 'TIRG', 'Y', 'paiement', 'N', 'Tir Groupé', 'Tir Groupé', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '^[A-Z0-9]{12}$', 'Y',
        'manuel', 'No cheque', '^[A-Z0-9]{12}$', 'N', '', '512000', 'BQ', 'Y'),
       (19, 'TRT30', 'N', 'paiement', 'N', 'Traite 30 j', 'Traite à 30 jours', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No SIREN', '^\\d{9}$', 'Y',
        'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512000', 'BQ', 'Y'),
       (20, 'CHKF', 'Y', 'paiement', 'N', 'Chèque à récept facture', 'Chèque à réception de facture', 0.00, 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel',
        'No SIREN', '^\\d{9}$', 'Y', 'manuel', 'No cheque', '^\\d{7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
       (22, 'PSM', 'N', '', 'N', 'PSM', 'Paiement sur mobile', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (23, 'PMR', 'Y', 'remboursement', 'N', 'PriceMinister', 'PriceMinister', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande PriceMinister', 'Y', 'auto',
        'No commande PriceMinister', '', 'Y', 'auto', 'No commande PriceMinister', '', 'N', '', '512000', 'BQ', 'N'),
       (24, 'SFCO', 'N', '', 'N', 'sofinco', 'crédit sofinco', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (25, 'SFCO2', 'N', '', 'N', 'Sofinco FdP', 'Sofinco Foire de Paris', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'Y', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (26, 'AMZN', 'Y', 'remboursement', 'N', 'Amazon', 'commande passée sur Amazon', 0.00, 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Amazon', 'Y', 'auto', 'No commande Amazon',
        '', 'Y', 'auto', 'No commande Amazon', '', 'N', '', '512000', 'BQ', 'N'),
       (30, '1EC', 'N', 'paiement', 'N', '1euro.com', 'crédit 1euro.com', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '[0-9]{1,64}', 'Y',
        'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'N'),
       (31, 'EKDO', 'Y', 'paiement', 'N', 'eKDO', 'eKDO', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto',
        'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No cheque',
        '^\\d{7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'N'),
       (32, 'CGA', 'N', 'paiement', 'N', 'Cofinoga', 'Carte Cofinoga', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N',
        'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '[0-9]{1,64}', 'Y', 'auto',
        'No remise', '[0-9]{1,64}', 'N', '', '512000', 'BQ', 'N'),
       (33, 'AMX', 'N', 'paiement', 'N', 'Amex', 'Carte Amex Spplus', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N',
        'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9]{1,64}$', 'Y', 'auto',
        'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'AMX', 'N'),
       (34, 'MDTC', 'Y', 'paiement', 'N', 'Mandat Cash', 'Mandat Cash', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No mandat', '^\\w{1,64}$', 'N',
        'manuel', 'No mandat', '^\\w{1,64}$', 'N', '', '5121', 'CC', 'N'),
       (35, 'MDTA', 'Y', 'paiement', 'N', 'Mandat Admin', 'Mandat Administratif', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No mandat', '^\\w{1,64}$',
        'Y', 'manuel', 'No mandat', '^\\w{1,64}$', 'N', '', '512000', 'BQ', 'Y'),
       (36, 'CRTAT', 'N', 'paiement', 'N', 'CR TAT', 'Paiement à la livraison TAT', 10.00, 'Y', 'N', 'N', 'N', 'N', 'N',
        'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', '', 'Y', 'manuel', 'Confirmation CR', '^ok$', 'Y', 'manuel',
        'No cheque', '^\\d{1,7}$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'N'),
       (37, '2XMC', 'N', 'remboursement', 'N', '2xMoinsCher', '2xMoinsCher', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande 2xMoinsCher', 'Y', 'auto', 'No commande 2xMoinsCher',
        '', 'Y', 'auto', 'No commande 2xMoinsCher', '', 'N', '', '512000', 'BQ', 'N'),
       (38, 'RAP', 'N', 'paiement', 'N', 'RAP', 'Kwixo Comptant', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N',
        'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'Y',
        'manuel', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (39, 'RAPC', 'N', 'paiement', 'N', 'RAP', 'Kwixo Crédit', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N',
        'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'Y',
        'manuel', 'No transaction', '^[0-9A-Za-z]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (40, 'CBS-P', 'N', 'paiement', 'N', 'CBOL-Paybox', 'Carte de crédit en ligne sécurisée Paybox', 0.00, 'N', 'Y',
        'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto',
        'No transaction', '^[A-Z0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Z0-9]{1,64}$', 'N', '', '512300', 'CE',
        'N'),
       (41, 'AMX-P', 'N', 'paiement', 'N', 'Amex-Paybox', 'Carte American Express Paybox', 0.00, 'N', 'Y', 'N', 'N',
        'N', 'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'AMX', 'N'),
       (42, 'AUR-P', 'N', 'paiement', 'N', 'Aurore-Paybox', 'Carte Aurore Paybox', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N',
        'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9\\-]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (43, 'CGA-P', 'N', 'paiement', 'N', 'Cofinoga-Paybox', 'Carte Cofinoga Paybox', 0.00, 'N', 'Y', 'N', 'N', 'N',
        'N', 'N', 'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[0-9\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[0-9]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (44, 'PPAL-P', 'Y', 'paiement', 'N', 'Paypal-Paybox', 'Paypal Paybox', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y',
        'Y', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction',
        '^[A-Z0-9\\-]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9\\-]{1,64}$', 'N', '', '512020', 'PP', 'Y'),
       (45, 'EBAY', 'Y', 'remboursement', 'N', 'Ebay-Paypal', 'Ebay-Paypal', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'Y',
        'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Ebay', 'Y', 'manuel', 'No transaction',
        '^[A-Z0-9]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9]{1,64}$', 'N', '', '512020', 'PP', 'N'),
       (46, 'PIXM', 'Y', 'remboursement', 'N', 'Pixmania', 'Pixmania', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Pixmania', 'Y', 'auto', 'No commande Pixmania', '', 'Y',
        'auto', 'No commande Pixmania', '', 'N', '', '512000', 'BQ', 'N'),
       (47, 'RUEDC', 'Y', 'remboursement', 'N', 'Rue du Commerce', 'Rue du Commerce', 0.00, 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Rue du Commerce', 'Y', 'auto',
        'No commande Rue du Commerce', '', 'Y', 'auto', 'No commande Rue du Commerce', '', 'N', '', '512000', 'BQ',
        'N'),
       (48, 'DCT', 'Y', 'remboursement', 'N', 'Décote', 'Décote', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (49, 'SVDCC', 'Y', 'paiement', 'N', 'Carte Cadeau SVD', 'Carte Cadeau Son-Vidéo.com', 0.00, 'N', 'Y', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No Carte',
        '^[0-9]{1,64}$', 'Y', 'manuel', 'No Carte', '^[0-9]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
       (50, 'TSVDCC', 'N', 'remboursement', 'Y', 'Avoir (Carte Cadeau SVD)', 'Avoir (Carte Cadeau SVD)', 0.00, 'Y', 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel',
        'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (51, 'CHAVAS', 'N', 'paiement', 'N', 'Chèque Havas', 'Chèque Havas', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (52, 'ENOE', 'Y', 'paiement', 'N', 'Chèque Enfants Noë', 'Chèque Enfants Noë', 0.00, 'Y', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N',
        'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (53, 'CADHOC', 'N', 'paiement', 'N', 'Chèque CADHOC', 'Chèque CADHOC', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (54, 'BES', 'N', 'paiement', 'N', 'Chèque Bonus Euro System', 'Chèque Bonus Euro System', 0.00, 'Y', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque',
        '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (55, 'TTCR', 'N', 'paiement', 'N', 'Ticket Compliment Référence', 'Ticket Compliment Référence', 0.00, 'Y', 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel',
        'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (56, 'TTCB', 'N', 'paiement', 'N', 'Ticket Compliment Bienvenue', 'Ticket Compliment Bienvenue', 0.00, 'Y', 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel',
        'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (57, 'TTI', 'Y', 'paiement', 'N', 'Ticket Infini', 'Ticket Infini', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (58, 'TTH', 'Y', 'paiement', 'N', 'Ticket Horizon', 'Ticket Horizon', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'Y'),
       (59, 'CBS-O', 'N', 'paiement', 'N', 'CBOL-Ogone-3DS', 'Carte de crédit en ligne sécurisée Ogone 3DS', 0.00, 'N',
        'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto',
        'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '',
        '512300', 'CE', 'Y'),
       (60, 'AMX-O', 'N', 'paiement', 'N', 'Amex-Ogone', 'Carte American Express Ogone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y',
        'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'manuel', 'No remise', '^[A-Za-z0-9_\\/]{1,64}$', 'N', '', '512000', 'AMX',
        'Y'),
       (61, 'AUR-O', 'N', 'paiement', 'N', 'Aurore-Ogone', 'Carte Aurore Ogone', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y',
        'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\-\\/]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\-\\/]{1,64}$', 'N', '', '512000', 'BQ',
        'N'),
       (62, 'CGA-O', 'N', 'paiement', 'N', 'Cofinoga-Ogone', 'Carte Cofinoga Ogone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y',
        'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (63, 'PPAL-O', 'N', 'paiement', 'N', 'Paypal-Ogone', 'Paypal Ogone', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'N', 'Y',
        'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction',
        '^[A-Z0-9_\\-]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9_\\-\\.]{1,64}$', 'N', '', '512020', 'PP', 'Y'),
       (64, 'CBS-OT', 'Y', 'paiement', 'N', 'CBOL-Ogone-Tél', 'Carte de crédit en ligne sécurisée Ogone Téléphone',
        0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto',
        'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '',
        '512300', 'CE', 'Y'),
       (65, 'AMX-OT', 'Y', 'paiement', 'N', 'Amex-Ogone-Tél', 'Carte American Express Ogone Téléphone', 0.00, 'N', 'Y',
        'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto',
        'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'manuel', 'No remise', '^[A-Za-z0-9_\\/]{1,64}$', 'N', '',
        '512000', 'AMX', 'Y'),
       (66, 'AUR-OT', 'Y', 'paiement', 'N', 'Aurore-Ogone-Tél', 'Carte Aurore Ogone Téléphone', 0.00, 'N', 'Y', 'N',
        'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\-]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (67, 'CGA-OT', 'Y', 'paiement', 'N', 'Cofinoga-Ogone-Tél', 'Carte Cofinoga Ogone Téléphone', 0.00, 'N', 'Y', 'Y',
        'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (68, 'PPAL', 'N', 'paiement', 'N', 'Paypal', 'Paypal', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'auto',
        'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^[A-Z0-9_\\-]{1,64}$', 'Y', 'auto',
        'No transaction', '^[A-Z0-9_\\-\\.]{1,64}$', 'N', '', '512020', 'PP', 'Y'),
       (69, 'CBS-O2', 'N', 'paiement', 'N', 'CBOL-Ogone', 'Carte de crédit en ligne sécurisée Ogone', 0.00, 'N', 'Y',
        'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto',
        'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '',
        '512000', 'BQ', 'N'),
       (70, 'EMPT', 'Y', 'paiement', 'N', 'Emport dépôt', 'Emport dépôt', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'Y',
        'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No commande', '^\\d{1,9}$', 'Y',
        'manuel', 'No cheque', '^a2gH7em9p$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'Y'),
       (71, 'PRESTO', 'Y', 'paiement', 'N', 'Cetelem Presto', 'crédit Cetelem Presto', 0.00, 'N', 'Y', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '[0-9]{1,64}', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ', 'N'),
       (72, 'CBS-EMPT', 'Y', 'paiement', 'N', 'CBOL-Ogone-emport',
        'Carte de crédit en ligne sécurisée Ogone - réservation emport', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y',
        'Y', 'auto', 'manuel', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction',
        '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE',
        'Y'),
       (73, 'FNAC', 'Y', 'remboursement', 'N', 'Fnac', 'Fnac', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'manuel', 'manuel', 'Y', 'auto', 'No commande Fnac', 'Y', 'auto', 'No commande FNAC', '^[A-Z0-9]{1,64}$', 'Y',
        'auto', 'No commande FNAC', '^[A-Z0-9]{1,64}$', 'N', '', '512000', 'BQ', 'N'),
       (74, 'ECLNG', 'Y', 'paiement,remboursement', 'N', 'Ecranlounge', 'Ecranlounge', 0.00, 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Ecranlounge', 'Y', 'auto',
        'No commande Ecranlounge', '', 'Y', 'auto', 'No commande Ecranlounge', '', 'N', '', '512000', 'BQ', 'N'),
       (75, 'TSAV', 'Y', 'remboursement', 'Y', 'Avoir (SAV)', 'Avoir (SAV)', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No cheque', '', 'N', 'manuel',
        'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (76, 'RETANT', 'Y', 'paiement', 'N', 'Retour SAV anticipé', 'Retour SAV anticipé', 0.00, 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No SIREN', '', 'Y',
        'manuel', 'No cheque', '', 'Y', '', '512000', 'BQ', 'Y'),
       (77, 'AMZNUK', 'Y', 'remboursement', 'N', 'Amazon UK', 'commande passée sur Amazon UK', 0.00, 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Amazon', 'N', 'manuel',
        'No commande Amazon', '', 'Y', 'auto', 'No commande Amazon', '', 'N', '', '512000', 'UK', 'N'),
       (78, 'RDTE', 'Y', 'remboursement', 'N', 'Redoute', 'commande passée sur Redoute', 0.00, 'Y', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'auto', 'No commande Redoute', 'N', 'manuel',
        'No commande Redoute', '', 'N', 'auto', 'No cheque', '', 'N', '', '512000', 'BQ', 'N'),
       (79, '', 'Y', 'paiement', 'N', '', '', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel',
        'N', 'auto', 'No facture', 'N', 'manuel', 'No cheque', '', 'N', 'manuel', 'No cheque', '', 'N', '', '512000',
        'BQ', 'N'),
       (80, 'CBS-COT', 'Y', 'paiement', 'N', 'CBOL-Ogone-COT', 'Carte de crédit en ligne sécurisée Ogone - réservation',
        0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'manuel', 'N', 'auto', 'ID Trans. Client', 'Y',
        'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$',
        'N', '', '512300', 'CE', 'N'),
       (81, 'COT', 'Y', 'paiement', 'N', 'Demande de cotation', 'Demande de cotation', 0.00, 'Y', 'N', 'N', 'N', 'N',
        'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'N', 'manuel', 'No commande',
        '^\\d{1,9}$', 'Y', 'manuel', 'No cheque', '^a2gH7em9p$', 'Y', '^\\d{1,64}$', '512000', 'BQ', 'N'),
       (82, 'CTPE-PNF', 'Y', 'paiement', 'N', 'CTPE PNF', 'Carte terminal de paiement PNF', 0.00, 'Y', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction',
        '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512300', 'CE', 'Y'),
       (83, 'CTPE-AMX', 'Y', 'paiement', 'N', 'CTPE Amex', 'Carte terminal de paiement AMEX', 0.00, 'Y', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction',
        '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512000', 'BQ', 'Y'),
       (84, 'NXCB', 'Y', 'paiement', 'N', 'Cetelem NxCB', 'Paiement en 3 ou 4 fois par carte bancaire', 0.00, 'N', 'Y',
        'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto',
        'No transaction', '[0-9]{1,64}', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000',
        'BQ', 'Y'),
       (85, 'NXCB-CB', 'Y', 'paiement', 'N', 'NxCB CB', 'Paiement CB du NxCB', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'Date de virement',
        '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ',
        'Y'),
       (86, 'NXCB-CET', 'Y', 'paiement', 'N', 'NxCB Cetelem', 'Paiement Cetelem du NxCB', 0.00, 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'Date de virement',
        '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N', '', '512000', 'BQ',
        'Y'),
       (87, 'NXCB-RMB', 'Y', 'remboursement', 'N', 'NxCB Remboursement', 'Remboursement de paiement NxCB', 0.00, 'N',
        'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel',
        'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'Y', 'manuel', 'Date de virement', '^\\d{2}-\\d{2}-\\d{2}$', 'N',
        '', '512000', 'BQ', 'N'),
       (88, 'CDSCNT', 'Y', 'remboursement', 'N', 'Cdiscount', 'commande passée sur Cdiscount', 0.00, 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Cdiscount', 'Y', 'auto',
        'No commande Cdiscount', '', 'Y', 'auto', 'No commande Cdiscount', '', 'N', '', '512000', 'BQ', 'N'),
       (89, 'DARTY', 'Y', 'remboursement', 'N', 'Darty', 'commande passée sur Darty', 0.00, 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande Darty', 'Y', 'auto', 'No commande Darty', '',
        'Y', 'auto', 'No commande Darty', '', 'N', '', '512300', 'BQ', 'N'),
       (90, 'FULLCB3X', 'N', 'paiement', 'N', 'FullCB 3x', 'Cetelem FullCB 3x', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N',
        'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel',
        'No remise', '', 'N', '', '512000', 'BQ', 'Y'),
       (91, 'FULLCB4X', 'N', 'paiement', 'N', 'FullCB 4x', 'Cetelem FullCB 4x', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N',
        'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel',
        'No remise', '', 'N', '', '512000', 'BQ', 'Y'),
       (92, 'CILO', 'Y', 'remboursement', 'N', 'Cilo', 'Commande chez Cilo', 0.00, 'N', 'N', 'N', 'N', 'N', 'N', 'N',
        'N', 'N', 'manuel', 'manuel', 'Y', 'auto', 'No commande', 'Y', 'auto', 'No commande', '', 'Y', 'auto',
        'No commande', '', 'N', '', '512000', 'BQ', 'N')
;

-- customer order
INSERT INTO backOffice.prospect (id_prospect, cnt_email, cnt_lvr_email, email)
VALUES (1, '<EMAIL>', '<EMAIL>', '<EMAIL>'),
       (2, '<EMAIL>', '<EMAIL>', '<EMAIL>')
;

INSERT INTO backOffice.commande (id_commande, id_prospect, cnt_fct_email, cnt_lvr_email, commentaire_facture,
                                 no_commande_origine, flux, promotion_id, depot_emport, id_transporteur,
                                 id_pdt_transporteur, sales_channel_id, date_creation)
VALUES (1, 1, '<EMAIL>', '<EMAIL>', 'test', '1', 'traitement', null, null, 2, 1, 1,
        '2018-09-11'),
       (2, 2, '<EMAIL>', '<EMAIL>', 'test', '2', 'traitement', null, 1, null, null,
        1, '2018-09-11'),
       (3, 2, '<EMAIL>', '<EMAIL>', 'test', '3', 'traitement', null, 1, null, null,
        1, '2021-01-15'),
       (4, 2, '<EMAIL>', '<EMAIL>', 'test', '4', 'traitement', null, 5, null, null,
        1, '2021-01-15')
;

INSERT INTO backOffice.produit_commande (id, id_commande, id_produit, id_unique, tva, quantite, prix_vente, prix_achat,
                                         prix_achat_nc, description, prix_ecotaxe, duree_garantie_ext,
                                         prix_garantie_ext, tva_garantie_ext, commission_garantie_ext,
                                         tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc,
                                         prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc,
                                         vendeur_garantie_vc, remise_type, remise_montant, remise_description,
                                         groupe_type, groupe_description, id_bon_livraison, reservation, prix_sorecop)
VALUES (10, 1, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null,
        0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
       (11, 2, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null,
        0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
       (12, 3, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null,
        0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
       (13, 4, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null,
        0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null)
;

INSERT INTO backOffice.paiement_commande (id, id_commande, id_unique, id_paiement, type, warehouse_id, creation_date,
                                          creation_usr, creation_montant, creation_justificatif, creation_origine,
                                          acceptation_date, acceptation_usr, acceptation_montant,
                                          acceptation_justificatif, annulation_date, annulation_usr, annulation_montant,
                                          demande_remise_date, demande_remise_usr, remise_date, remise_usr,
                                          remise_montant, remise_justificatif, remise_bon, remise_taux, impaye_date,
                                          impaye_usr, impaye_montant, auto_statut, auto_statut_detail, auto_garantie,
                                          auto_garantie_detail, pays_ip, pays_origine)
VALUES (101, 1, 1, 11, 'paiement', null, '2019-10-08 19:15:18', 'admin', 249.00, null, 'son-video.com',
        '2019-10-08 19:15:40', 'admin', 249.00, 'XXXXXX', null, null, 0.00, null, null, now(), 'admin', 249.00, 'TPE-1',
        null, 1, null, null, 0.00, null, null, null, null, null, null),
       (102, 2, 1, 11, 'paiement', null, '2018-09-11 14:36:23', 'admin', 249.00, null, 'son-video.com',
        '2019-10-08 19:15:40', 'admin', 249.00, 'XXXXXX', now(), 'admin', 249.00, null, null, null, null, 0.00, null,
        null, 1, null, null, 0.00, null, null, null, null, null, null),
       (103, 2, 2, 11, 'paiement', 5, '2018-09-11 14:36:23', 'admin', 249.00, null, 'son-video.com',
        '2018-09-11 14:36:23', 'admin', 249.00, 'XXXXXX', null, null, 0.00, null, null, null, null, 0.00, null, null, 1,
        null, null, 0.00, null, null, null, null, null, null)
;

-- Those can serve for front-end tests as well
INSERT INTO backOffice.paiement_commande(id, id_commande, id_unique, id_paiement, type, warehouse_id, creation_date,
                                         creation_usr, creation_montant, creation_justificatif, creation_origine,
                                         acceptation_date, acceptation_usr, acceptation_montant,
                                         acceptation_justificatif, annulation_date, annulation_usr, annulation_montant,
                                         demande_remise_date, demande_remise_usr, remise_date, remise_usr,
                                         remise_montant, remise_justificatif, remise_bon, remise_taux, impaye_date,
                                         impaye_usr, impaye_montant, auto_statut, auto_statut_detail, auto_garantie,
                                         auto_garantie_detail, pays_ip, pays_origine)
VALUES (3560956, 3, 2, 18, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 18:33:57', 'admin', 10, '230288954932', null, null, 0.00, null, null, null, null, 0.00, null, null,
        1, null, null, 0.00, null, null, null, null, null, null),
       (3560963, 3, 3, 70, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 18:31:46', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, null, null, null, null, null, null),
       (3568623, 3, 4, 57, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 09:52:45', 'admin', 10, '429497955', null, null, 0.00, null, null, null, null, 0.00, null, null, 1,
        null, null, 0.00, null, null, null, null, null, null),
       (3568646, 3, 5, 8, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 10:00:37', 'admin', 10, '0054672876', null, null, 0.00, null, null, null, null, 0.00, null, null, 1,
        null, null, 0.00, null, null, null, null, null, null),
       (3572551, 3, 7, 59, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, '2011127-1', 'son-video.com',
        '2021-01-15 15:07:36', 'admin', 10, '125701-5715964699', null, null, 0.00, null, null, null, null, 0.00, null,
        null, 1, null, null, 0.00, 'accepte', '5', '3DS', '90', null, null),
       (3577135, 3, 8, 15, 'remboursement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 16:00:44', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, null, null, null, null, null, null),
       (3577208, 3, 12, 12, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 16:35:19', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, null, null, null, null, null, null),
       (3574404, 3, 13, 49, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, '2011638-1', 'son-video.com',
        '2021-01-15 11:00:32', 'admin', 10, '07805983757699326929', null, null, 0.00, null, null, null, null, 0.00,
        null, null, 1, null, null, 0.00, 'accepte', 'Paiement accepté : 40', 'SVDCC', '100', null, null),
       (3574403, 3, 14, 59, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, '2011638-2', 'son-video.com',
        '2021-01-15 11:00:32', 'admin', 10, '664843-5717484144', null, null, 0.00, null, null, null, null, 0.00, null,
        null, 1, null, null, 0.00, 'accepte', '5', '3DS', '90', null, null),
       (3575151, 3, 16, 17, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 16:04:02', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, null, null, null, null, null, null),
       (3576730, 3, 18, 11, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 12:38:41', 'admin', 10, '362869', null, null, 0.00, null, null, null, null, 0.00, null, null, 1,
        null, null, 0.00, null, null, null, null, null, null),
       (3574971, 3, 20, 20, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 14:51:53', 'admin', 10, '111111111', null, null, 0.00, null, null, null, null, 0.00, null, null, 1,
        null, null, 0.00, null, null, null, null, null, null),
       (3574981, 3, 21, 60, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, '2011974-1', 'son-video.com',
        '2021-01-15 15:10:12', 'admin', 10, '206207-5718084301', null, null, 0.00, null, null, null, null, 0.00, null,
        null, 1, null, null, 0.00, 'accepte', '5', '3DS', '90', null, null),
       (3575037, 3, 22, 83, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 15:13:16', 'admin', 10, '884517', null, null, 0.00, null, null, null, null, 0.00, null, null, 1,
        null, null, 0.00, null, null, null, null, null, null),
       (3575198, 3, 23, 91, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, '6aaae29d-bc7b-44c6-827b-49f616a668fc',
        'son-video.com', '2021-01-15 16:40:20', 'admin', 10, '6aaae29d-bc7b-44c6-827b-49f616a668fc', null, null, 0.00,
        null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', 'success', null, null, null, null),
       (3575376, 3, 24, 8, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 17:38:29', 'admin', 10, '253361441010', null, null, 0.00, null, null, null, null, 0.00, null, null,
        1, null, null, 0.00, null, null, null, null, null, null),
       (3576149, 3, 26, 17, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 07:27:59', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, null, null, null, null, null, null),
       (3575562, 3, 27, 90, 'paiement', 1, '2021-01-15 12:34:56', 'admin', 10, '831ab5db-e938-421e-9f42-b3115544b036',
        'son-video.com', '2021-01-15 19:40:03', 'admin', 10, '831ab5db-e938-421e-9f42-b3115544b036', null, null, 0.00,
        null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', 'success', null, null, null, null),
       (3575563, 4, 28, 90, 'paiement', 5, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 19:40:03', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, 'accepte', 'success', null, null, null, null),
       (575564, 4, 29, 90, 'paiement', 5, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 19:40:03', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, 'accepte', 'success', null, null, null, null),
       (3575565, 4, 30, 90, 'paiement', 5, '2021-01-15 12:34:56', 'admin', 10, null, 'son-video.com',
        '2021-01-15 19:40:03', 'admin', 10, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null,
        null, 0.00, 'accepte', 'success', null, null, null, null)
;

INSERT INTO backOffice.bon_retour (id_bon_retour, id_commande, date_creation, statut)
VALUES (69285, 1, '2021-06-27 23:48:06', 'attente');
INSERT INTO backOffice.bon_retour (id_bon_retour, id_commande, date_creation, statut)
VALUES (69284, 2, '2021-06-27 19:41:12', 'attente');
INSERT INTO backOffice.bon_retour (id_bon_retour, id_commande, date_creation, statut)
VALUES (69283, 3, '2021-06-27 15:40:32', 'attente');

INSERT INTO backOffice.produit_bon_retour (semaphore, id, id_bon_retour, id_produit, quantite, quantite_retour,
                                           motif_retour, erreur_article, id_produit_retour, no_serie, id_bon_livraison,
                                           date_expedition, date_livraison, reexpedition, piece_retour,
                                           stock_emplacement, etat_statut, etat_commentaire, etat_commentaire_public,
                                           etat_commentaire_client, date_retour, date_refus, id_depot)
VALUES ('tmp', 83937, 69283, 81078, 1, 0, 'retractation', 'N', null, '', 4605537, '2021-06-18', '2021-06-18', 'N', null,
        'a', null, null, null, 'je ne trouve pas de numéro de série', null, null, 21);
INSERT INTO backOffice.produit_bon_retour (semaphore, id, id_bon_retour, id_produit, quantite, quantite_retour,
                                           motif_retour, erreur_article, id_produit_retour, no_serie, id_bon_livraison,
                                           date_expedition, date_livraison, reexpedition, piece_retour,
                                           stock_emplacement, etat_statut, etat_commentaire, etat_commentaire_public,
                                           etat_commentaire_client, date_retour, date_refus, id_depot)
VALUES ('tmp', 83938, 69284, 81123, 1, 0, 'retractation', 'N', null, '', 4605133, '2021-06-17', '2021-06-21', 'N', null,
        'a', null, null, null, '', null, null, 21);
INSERT INTO backOffice.produit_bon_retour (semaphore, id, id_bon_retour, id_produit, quantite, quantite_retour,
                                           motif_retour, erreur_article, id_produit_retour, no_serie, id_bon_livraison,
                                           date_expedition, date_livraison, reexpedition, piece_retour,
                                           stock_emplacement, etat_statut, etat_commentaire, etat_commentaire_public,
                                           etat_commentaire_client, date_retour, date_refus, id_depot)
VALUES ('tmp', 83939, 69284, 128416, 1, 0, 'retractation', 'N', null, '', 4605133, '2021-06-17', '2021-06-21', 'N',
        null, 'a', null, null, null, '', null, null, 21);
INSERT INTO backOffice.produit_bon_retour (semaphore, id, id_bon_retour, id_produit, quantite, quantite_retour,
                                           motif_retour, erreur_article, id_produit_retour, no_serie, id_bon_livraison,
                                           date_expedition, date_livraison, reexpedition, piece_retour,
                                           stock_emplacement, etat_statut, etat_commentaire, etat_commentaire_public,
                                           etat_commentaire_client, date_retour, date_refus, id_depot)
VALUES ('tmp', 83944, 69285, 13895, 1, 0, 'retractation', 'N', null, '0660', 4603618, '2021-06-14', '2021-06-14', 'N',
        null, 'a', null, null, null, '', null, null, 21);

INSERT INTO backOffice.BO_SAV_station (id, nom, adresse1, adresse2, adresse3, adresse4, code_postal, ville, pays_id,
                                       email, telephone, fax, procedure_PAD, procedure_SAV, precisions_demande_RMA)
VALUES (4, 'Stas', '25 BOULEVARD DES ARTISANS', 'BAILLY PARK - LOT 13', 'MARNE LA VALLEE', null, '77700',
        'BAILLY ROMAINVILLIERS', 67, '<EMAIL>', '01 64 63 26 14', null, 'envoi direct', 'envoi direct', 'la société STAS passe tous les vendredi sur Champigny, dossier SAV complété à imprimer et mettre sur le colis ! Pour les boutiques, nous renvoyer le produit avec le dossier SAV imprimé.
Produit Klipsch : quand Sol Com evoquée,  faire suivre le mail à Fabien en copie pour mise en place de l''avoir
<EMAIL>
PDE grilles Klipsch Heritage ou Cornwall IV : <EMAIL> (en anglais)');
INSERT INTO backOffice.BO_SAV_station (id, nom, adresse1, adresse2, adresse3, adresse4, code_postal, ville, pays_id,
                                       email, telephone, fax, procedure_PAD, procedure_SAV, precisions_demande_RMA)
VALUES (102, 'FOCAL (SAV + PDE )', '108 rue de l''Avenir', null, null, null, '42350', 'LA TALAUDIERE ', 67,
        '<EMAIL>', '04 77 43 57 25', null, 'demande RMA', 'demande RMA',
        'ne prends pas les intras encrassés et les renvoie');
INSERT INTO backOffice.BO_SAV_station (id, nom, adresse1, adresse2, adresse3, adresse4, code_postal, ville, pays_id,
                                       email, telephone, fax, procedure_PAD, procedure_SAV, precisions_demande_RMA)
VALUES (139, 'Fiio China ( SAV + PDE )', '2/F, F Building', 'Industrial Zone Shigang Village', 'Huangshi West Road',
        'Baiyun District', '510430', 'GuangZhou', 40, '<EMAIL>', '+86-13922476096', null, 'demande RMA',
        'demande RMA', 'Son-Video est importateur, il n''y a pas de RMA a demander.
Si defectueux, le renvoyer à Champigny pour examen via le SAV et si un probleme, echange a neuf. nous negocierons le rachat de garantie.
PDE : <NAME_EMAIL>');
INSERT INTO backOffice.BO_SAV_station (id, nom, adresse1, adresse2, adresse3, adresse4, code_postal, ville, pays_id,
                                       email, telephone, fax, procedure_PAD, procedure_SAV, precisions_demande_RMA)
VALUES (390, 'DALI (Casques)', 'Demande à faire sur le portail', 'Casques uniquement', null, null, null, '', 67, null,
        'tmp', null, 'demande RMA', 'demande RMA', 'https://www.dali-speakers.com/info/contact/warranty/');

INSERT INTO backOffice.BO_SAV_article (id, produit_bon_retour_id, station_id, station_date_envoi,
                                       station_numero_colis_envoi, station_date_renvoi_client, station_date_avoir,
                                       station_num_avoir, station_montant_avoir, commentaire, reexpedition,
                                       cloture_date, panne, numero_serie, numero_rma, avoir_commande,
                                       reference_destockage, relance_date)
VALUES (17269, 83937, 390, '2021-06-25 18:39:31', 'XJ102760863JF', null, null, null, null, '', 0, null,
        'déconnexion constante, bouton défectueux, son brouillé et modifié', '5205885.2003', 'ncc003266', 0, null,
        '2021-06-18 00:00:00');
INSERT INTO backOffice.BO_SAV_article (id, produit_bon_retour_id, station_id, station_date_envoi,
                                       station_numero_colis_envoi, station_date_renvoi_client, station_date_avoir,
                                       station_num_avoir, station_montant_avoir, commentaire, reexpedition,
                                       cloture_date, panne, numero_serie, numero_rma, avoir_commande,
                                       reference_destockage, relance_date)
VALUES (17323, 83938, 139, '2021-06-25 12:46:01', '8R43066521812', null, null, null, null, '', 0, null,
        'Pixels éteints parfois sur une petite zone sinon sur une ligne complète', '60030347770207', null, 0, null,
        null);
INSERT INTO backOffice.BO_SAV_article (id, produit_bon_retour_id, station_id, station_date_envoi,
                                       station_numero_colis_envoi, station_date_renvoi_client, station_date_avoir,
                                       station_num_avoir, station_montant_avoir, commentaire, reexpedition,
                                       cloture_date, panne, numero_serie, numero_rma, avoir_commande,
                                       reference_destockage, relance_date)
VALUES (17331, 83939, 102, '2021-06-25 12:01:05', '00EXU0ZJ', null, null, null, null, '', 0, null, 'Canal gauche HS',
        'A1BEBG007418', '42299', 0, null, null);
INSERT INTO backOffice.BO_SAV_article (id, produit_bon_retour_id, station_id, station_date_envoi,
                                       station_numero_colis_envoi, station_date_renvoi_client, station_date_avoir,
                                       station_num_avoir, station_montant_avoir, commentaire, reexpedition,
                                       cloture_date, panne, numero_serie, numero_rma, avoir_commande,
                                       reference_destockage, relance_date)
VALUES (17293, 83944, 4, '2021-06-25 11:54:27', 'donné à STAS', null, null, null, null, '', 0, null,
        'Gros buzz sur le canal gauche de l''ampli dès la mise en tension, testé avec enceintes inversées',
        '106406620430701', 'STAS', 0, null, null);


INSERT INTO backOffice.batch_catalog (article_id, article_url, article_name, common_article_name, common_content_id,
                                      score_average, introduction, basket_description, short_description, domain_id,
                                      domain_name, category_id, category_name, sub_category_id, sub_category_name,
                                      media_largest_uri, media_180_uri, media_300_uri, media_300_square_uri,
                                      media_450_uri, media_450_square_uri, media_600_uri, media_1200_uri, strong_points)
VALUES (13895, 'https://www.son-video.com/article/recepteurs-bluetooth/arcam/rblink', 'Arcam rBlink', 'Arcam rBlink',
        31200, 4.88889, 'introduction du produit', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 6,
        'Accessoires', 42, 'Distributeurs et transmetteurs', 180, 'Récepteurs Bluetooth',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
        'R&eacute;ception Bluetooth compatible apt-X<br />DAC audio haute qualit&eacute;<br />Excellent rapport signal/bruit<br />Fabrication soign&eacute;e')
;
