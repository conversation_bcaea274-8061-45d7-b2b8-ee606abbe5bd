INSERT INTO backOffice.WMS_area_type (area_type_id, label)
VALUES (9, 'Emport')
;

INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES (10, 9, 1, '21.emport', 'Zone d''emport de commande (Champigny 2)')
;

INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES (4, '03.05.a.01.01.01', 2, '03.05.A$01.01.01', 1),
       (5, '03.04.a.01.01.01', 2, '03.04.A$01.01.01', 1),
       (6, '03.06.a.01.01.01', 1, '03.06.A$01.01.01', 1),
       (10, '21.emport', 10, 'Zone d''emport de commande', 1)
;

INSERT INTO backOffice.BO_INV_zone_location (zone_id, location_id)
VALUES (1, 5),
       (1, 6)
;

INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id,
                                          inv_id_utilisateur_validation, statut, type, name)
VALUES (2, '2019-08-08 11:38:32', '2019-08-15 09:43:56', 1, 2, 1000, 'created', 'partial', null),
       (3, '2019-08-08 11:38:32', null, 1, 2, 1000, 'created', 'partial', null),
       (4, '2019-08-08 11:38:32', null, 2, 2, 1000, 'created', 'full', null),
       (5, '2019-08-08 11:38:32', null, 1, 2, 1000, 'on-going', 'full', null),
       (6, '2019-08-08 11:38:32', null, 1, 2, 1000, 'created', 'partial', null),
       (7, '2019-08-08 11:38:32', null, 2, 2, 1000, 'created', 'full', null)
;

INSERT INTO backOffice.BO_INV_zone_inventory (inventory_id, zone_id)
VALUES (2, 1),
       (2, 3),
       (3, 1),
       (3, 3),
       (3, 4)
;

UPDATE backOffice.BO_INV_inventaire
SET statut = 'closed'
WHERE id = 2;

-- add a move mission and a delivery note to the warehouse 2
INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES (3, 4, 2, '99.01', 'elsewhere');

INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES (7, '99.01.a.01.01.01', 3, '99.01.A$01.01.01', 1);

INSERT INTO backOffice.WMS_move_mission (move_mission_id, move_mission_type_id, created_at, assigned_to, product_id,
                                         quantity)
VALUES (3, 1, '2022-06-06 12:00:30', 1000, 81078, 3);

INSERT INTO backOffice.WMS_product_location (product_location_id, location_id, product_id, delivery_ticket_id,
                                             move_mission_id, quantity)
VALUES (40, 7, 81078, 4263252, 3, 10);

-- add a location with a delivery note in the 'Emport' location
INSERT INTO backOffice.bon_livraison (semaphore, id_bon_livraison, id_depot, id_commande, id_transfert, id_facture,
                                      is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation,
                                      status, detaxe_export, id_transporteur, id_pdt_transporteur,
                                      date_export_transporteur, utilisateur_export_transporteur, date_validation,
                                      utilisateur_validation, email_validation, sms_validation, numero_enquete,
                                      date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition,
                                      impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe,
                                      cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville,
                                      cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                      cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd,
                                      scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni,
                                      numero_kbis, numero_dbc)
VALUES (125191468862064, 123456, 1, 1712826, null, 1211490, 0, null, '2019-08-29 04:05:37', 'backoffice', 'au depart',
        'non', 2, 1, null, 'backoffice', '2019-08-29 20:15:09', 'backoffice', null, null, null, null, null, null, 'oui',
        '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark',
        '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null,
        null, null, null, null, null);

INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite,
                                              prix_vente, tva, remise_type, remise_montant, remise_description,
                                              duree_garantie_ext, prix_garantie_ext, tva_garantie_ext,
                                              commission_garantie_ext, tva_commission_garantie_ext,
                                              vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc,
                                              commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc,
                                              poids, nombre_colis)
VALUES (123456, 123456, 81078, 2, 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 15, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1);

INSERT INTO backOffice.WMS_product_location (product_location_id, location_id, product_id, delivery_ticket_id,
                                             move_mission_id, quantity)
VALUES (51, 10, 81078, 123456, null, 1);

-- add a location with a move mission and a delivery note with a quantity of 0
INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type,
                                derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva,
                                V_taux_marge, V_taux_marque, V_marge)
VALUES (1, 1, 154357533114364, 81079, 'TEST', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80);

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status,
                                prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente,
                                prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids,
                                poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur,
                                id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur,
                                V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit,
                                V_qte_dispo_resa, V_delai_lvr, V_delai_lvr_ferme, etat_statut, etat_devalorisation,
                                etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert,
                                V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page,
                                url_image, comparateur, recherche, description_panier, description_courte, diagonale,
                                description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max,
                                prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine,
                                code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage,
                                compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants,
                                fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre,
                                rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost,
                                is_main)
VALUES ('2019-09-03 01:06:38', 1, 0, 81079, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00,
        0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null,
        null, null, null, 0, 0, 0, 0, 4, 2, '',
        'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html',
        'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg',
        'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00,
        199.00, '2015-03-24', 67,
        '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6,
        15, 2, 0, 1, 1);

INSERT INTO backOffice.bon_livraison (semaphore, id_bon_livraison, id_depot, id_commande, id_transfert, id_facture,
                                      is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation,
                                      status, detaxe_export, id_transporteur, id_pdt_transporteur,
                                      date_export_transporteur, utilisateur_export_transporteur, date_validation,
                                      utilisateur_validation, email_validation, sms_validation, numero_enquete,
                                      date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition,
                                      impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe,
                                      cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville,
                                      cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                      cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd,
                                      scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni,
                                      numero_kbis, numero_dbc)
VALUES (125191468862064, 4263253, 1, 1712826, null, 1211490, 0, null, '2019-08-29 04:05:37', 'backoffice', 'au depart',
        'non', 2, 1, null, 'backoffice', '2019-08-29 20:15:09', 'backoffice', null, null, null, null, null, null, 'oui',
        '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark',
        '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null,
        null, null, null, null, null);

INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite,
                                              prix_vente, tva, remise_type, remise_montant, remise_description,
                                              duree_garantie_ext, prix_garantie_ext, tva_garantie_ext,
                                              commission_garantie_ext, tva_commission_garantie_ext,
                                              vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc,
                                              commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc,
                                              poids, nombre_colis)
VALUES (3268081, 4263253, 81079, 2, 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 15, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1);

INSERT INTO backOffice.WMS_product_location (product_location_id, location_id, product_id, delivery_ticket_id,
                                             move_mission_id, quantity)
VALUES (41, 6, 81079, 4263253, 2, 0);

-- add a delivery note and a move mission for an already counted product

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible,
                                     generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible,
                                     expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone,
                                     email, id_user, description, surcout_emport, product_emport, nom_transfert,
                                     nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre,
                                     horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code,
                                     tpe_id, abreviation, allowed_inventory_types)
VALUES (3, 'Champigny 3', 5, 1, 1, 1, 1, 1, 'Ici', '94506', 'Champigny sur Marne', 67, '0155091779',
        '<EMAIL>', 1, 'Blablabla', null, null, 'Son-Vidéo.com', 'centre logistique', 'là bas',
        'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert', 1, 'du lundi au lundi',
        'de 9h &agrave; 9h30', '', 1, 1, '09', null, 'Ch2', JSON_ARRAY('full'));

INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES (101, 4, 3, '09.01', 'Stock');

INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (14, '09.01.b.01.00.01', 101, '09.01.B$01.00.01', 1),
    (15, '09.01.b.01.01.01', 101, '09.01.B$01.01.01', 1),
    (16, '09.01.b.01.01.02', 101, '09.01.B$01.01.02', 1)
;

INSERT INTO backOffice.BO_INV_zone (zone_id, name, warehouse_id)
VALUES (101, 'Zone 1', 3),
       (102, 'Zone 2', 3)
;

INSERT INTO backOffice.BO_INV_zone_location (zone_id, location_id)
VALUES (101, 14),
       (101, 15),
       (102, 16)
;

INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type,
                                derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva,
                                V_taux_marge, V_taux_marque, V_marge)
VALUES (1, 1, 154357533114364, 81080, 'TEST_COUNTED', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366,
        75.80);

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status,
                                prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente,
                                prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids,
                                poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur,
                                id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur,
                                V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit,
                                V_qte_dispo_resa, V_delai_lvr, V_delai_lvr_ferme, etat_statut, etat_devalorisation,
                                etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert,
                                V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page,
                                url_image, comparateur, recherche, description_panier, description_courte, diagonale,
                                description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max,
                                prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine,
                                code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage,
                                compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants,
                                fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre,
                                rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost,
                                is_main)
VALUES ('2019-09-03 01:06:38', 1, 0, 81080, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00,
        0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null,
        null, null, null, 0, 0, 0, 0, 4, 2, '',
        'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html',
        'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg',
        'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00,
        199.00, '2015-03-24', 67,
        '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6,
        15, 2, 0, 1, 1);

INSERT INTO backOffice.bon_livraison (semaphore, id_bon_livraison, id_depot, id_commande, id_transfert, id_facture,
                                      is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation,
                                      status, detaxe_export, id_transporteur, id_pdt_transporteur,
                                      date_export_transporteur, utilisateur_export_transporteur, date_validation,
                                      utilisateur_validation, email_validation, sms_validation, numero_enquete,
                                      date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition,
                                      impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe,
                                      cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville,
                                      cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                      cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd,
                                      scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni,
                                      numero_kbis, numero_dbc)
VALUES (125191468862065, 4263270, 3, 1712826, null, 1211490, 0, null, '2019-08-29 04:05:37', 'backoffice', 'au depart',
        'non', 2, 1, null, 'backoffice', '2019-08-29 20:15:09', 'backoffice', null, null, null, null, null, null, 'oui',
        '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark',
        '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null,
        null, null, null, null, null);

INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite)
VALUES (3268089, 4263270, 81080, 3, 'BL sur un produit déjà compté', 1);

INSERT INTO backOffice.WMS_move_mission (move_mission_id, move_mission_type_id, created_at, assigned_to, product_id,
                                         quantity)
VALUES (9, 1, '2022-06-06 12:00:30', 1000, 81078, 3);

INSERT INTO backOffice.WMS_product_location (product_location_id, location_id, product_id, delivery_ticket_id,
                                             move_mission_id, quantity)
VALUES
    (1000, 14, 81078, 4263270, 9, 3), -- never counted
    (1001, 15, 81078, 4263270, 9, 3), -- never counted
    (1002, 16, 81080, 4263270, 9, 3) -- counted this year
;

INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id,
                                          inv_id_utilisateur_validation, statut, type, name, inv_date_closed)
VALUES
    (8, '2019-08-08 11:38:32', null, 3, 2, 1000, 'created', 'product', null, null),
    (9, '2019-08-08 11:38:32', null, 3, 2, 1000, 'created', 'product', null, null),
    (10, '2019-08-08 11:38:32', '2019-08-12 09:43:56', 3, 2, 1000, 'closed', 'full', null, NOW()),
    (11, '2019-08-08 11:38:32', null, 3, 2, 1000, 'on-going', 'product', null, null)
;

INSERT INTO backOffice.BO_INV_collecte (id, BO_INV_inventaire_id, collecte_type, numero)
VALUES
    (101, 10, 'global', 1),
    (102, 10, 'produit', 2)
;

INSERT INTO backOffice.BO_INV_zone_inventory (inventory_id, zone_id)
VALUES
    (8, 101),
    (9, 102)
;

INSERT INTO backOffice.BO_INV_differential (inventory_id, product_id, brand_id, buy_price, expected_quantity,
                                            expected_total_value, counted_quantity, counted_total_value,
                                            computed_stock_difference, computed_total_value_difference,
                                            is_validated_manually)
VALUES (10, 81080, 262, 131.58, 1, 1052.64, 1, 0.00, 0, 0.00, 0);

INSERT INTO backOffice.BO_INV_inventory_location (inventory_id, location_id)
VALUES
    (11, 15),
    (10, 15)
;
