INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
    (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;
INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page, menu, comprendre_pour_choisir, meta_description, presentation)
VALUES
    (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', ''),
    (13, 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null)
;
INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_2, garantie_5, garantie_5_bis, garantie_vc_1, garantie_vc_2, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
VALUES
    (70, 1, 0, 1, 0, 0, 1, 'Services', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 0.00, 0.200, 'non', '', '', '', 'marque', '0', '', '', 'N', null, '', null, null, 0, 0, '610', '0000000000'),
    (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 11, 9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080', '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080'),
    (144, 1, 0, 1, 1, 0, 1, 'Santé', '', 6, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 0.00, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, null, '/sante-connectee.html', null, 0, 0, '610', '0000000000'),
    (1, 1, 0, 1, 0, 0, 1, 'Indéfinie', '', 13, 'non', 'non', 0, 'non', 'non', 'non', 'non', 1, 9.90, 0.200, 'non', '', '', '', 'marque', '0', '', '', 'N', null, '', null, null, 0, 0, '610', '0000000000')
;
INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;
INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture, port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav, id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay, pixmania_segment_id, hors_gabarit, illustration, redoute_nomenclature_node)
VALUES
    (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null, null, null, null, '79323', '1', 9630, 0, 'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg', null),
    (56, 1, 1, 'Câbles d''enceintes', '/Rayons/Cables/EspaceCable/CablesA_Enceintes.html', 5.99, 0.200, 144, 1, 'MC-4702', '/Parts/Nav/NavR_Cable_Enceintes.html', 6, 6, '137917', '1', 118, 0, 'http://www.son-video.com/images/dynamic/Cables_audio/composes/NORSTCL40010M/NorStone-CL400-Classic-2-x-4-mm2-10-m-_P_180.jpg', ''),
    (296, 1, 0, 'Catalogues et parutions', null, 0.00, 0.200, 70, 0, null, null, null, null, null, null, null, 0, 'http://www.son-video.com/images/', null),
    (184, 1, 1, 'Indéfinie', null, 9.99, 0.200, 1, 1, null, null, null, null, null, null, null, 0, 'http://www.son-video.com/images/', '')
;
INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
VALUES
    (1, 1, 154357533114364, 81078, 'ARCAMRBLINKNR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 449698107735904, 143088, 'QEDQE6119', 'article', '2019-11-16 13:22:03', 56, 144, 6, 0.200, 1.206, 0.547, 6.37),
    (1, 1, 536515430115643, 143169, 'SVCATAPREMHIVER', 'generique', '2019-12-11 12:27:51', 296, 70, 6, 0.200, null, null, -0.01),
    (1, 1, 536515430115644, 1, 'INSTOCKBPRODUCT', 'article', '2019-12-11 12:27:51', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 124307721969027, 139789, 'CARTECADEAUSVD1120', 'article', '2020-01-29 22:10:48', 184, 1, 13, 0.200, null, 1.000, 933.33),
    (1, 1, 564968765468744, 81079, 'ARCAMRBLINKNR2', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 987356498706535, 81080, 'ARCAMRBLINKNR3', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80)
;
INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte, id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte, encours_maximum, encours_consomme, marque_disponible, marque_en_vente, siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax, siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse, siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile, commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile, comptabilite_email, technique_contact, technique_telephone, technique_mobile, technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen, frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape, hors_delai_auto, reliquat_attente_auto)
VALUES
    (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
    (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
    (943050784937153, 400, 'LA BOITE CONCEPT', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;
INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, gamme_qualite, public, type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif, prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis, V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES
    (864984545730971, 262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
    (415502164899839, 156, 'QED', 'http://www.son-video.com/images/static/marques/QED.gif', 'oui', 'GT Audio', '', '', '', '', '', 5, '', '', '', '', 162, 0.000, 0.000, 'non', 'true', 22777, 284, 4.725, 271, 0, '', '', ''),
    (244836806547676, 1219, 'SVD Boutique', null, 'oui', '', '', '', '', '', '', 0, '', '', '', '', 40, 0.000, 0.000, 'non', 'true', null, 0, null, 0, null, '', '', ''),
    (455644551157620, 819, 'Carte Cadeau', '', 'oui', '', '', '', '', '', '', 0, '', '', '', '', 5, 0.000, 0.000, 'non', 'true', null, 0, null, 0, 0, '', '', '')
;
INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES
    (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41'),
    (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1, '2019-03-01 10:35:41')
;
INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, V_delai_lvr_ferme, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main, is_auto_picked)
VALUES
    ('2019-09-03 01:06:38', 1, 0, 81078, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1, 0),
    ('2019-11-17 00:34:01', 1, 0, 143088, '2019-11-16', 'todo', 5.28, null, 0.00, 14.00, 14.00, 0.02, 0.00, 0.00, 0.460, 'Y', 1, 1, 'N', null, 'QE6119', 162, 156, 'Performance Audio 40i (1 m)', 'performance', 1, 1.00, 0, 25, 'a', null, 0, null, 0, null, null, null, null, 0, 0, 0, 0, 0, 99, '', 'http://www.son-video.com/Rayons/Cable-Enceinte/QED-Performance-Audio-40.html', '', 'oui', 'oui', 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 'QED Performance Audio 40i (1 m)', 0, '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, 67, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1, 0),
    ('2019-12-11 12:27:51', 1, 1, 143169, '2017-11-20', 'oui', 0.01, null, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.800, 'N', 1, 1, 'Y', null, null, 162, 1219, 'Catalogue 2020', 'Catalogue 2020', 1, 0.00, 1, 0, 'a', null, 1, null, 1, null, null, null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'oui', 'Catalogue 2020 Son-Vidéo.com, finition premium', 'Catalogue 2020 Son-Vidéo.com, finition premium', 0, null, 0.000, 0.000, 0, 0, 0.00, 0.01, '2017-12-15', null, '85189000', null, 0.00, 0.000, 0.00, 0.00, 0, 0, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 0, 0),
    ('2019-09-03 01:06:38', 1, 0, 1, '2013-02-20', 'last', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 1, 2, 'b', 81078, 3, 0, 1, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1, 0),
    ('2020-01-30 00:34:00', 1, 0, 139789, '2019-07-23', 'oui', 0.00, 0.00, 0.00, 1120.00, 0.00, 0.00, 0.00, 0.00, 0.000, 'Y', 1, 1, 'N', null, null, 1, 819, '1120 euros', null, 1, 0.00, 900, 0, 'a', null, 900, 0, 1, null, null, null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'non', '', '', 0, '', 0.000, 0.000, 0, 0, 0.00, 1120.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1, 1),
    ('2019-09-03 01:06:38', 1, 0, 81079, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK2', 162, 262, 'rBlink version 2', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink v2', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1, 0),
    ('2019-09-03 01:06:38', 1, 0, 81080, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK3', 162, 262, 'rBlink version 3', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, 1, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink v3', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1, 0)
;


-- data directly related to supplier orders
INSERT INTO backOffice.commande_fournisseur (id_commande_fournisseur, id_fournisseur, id_depot, date_creation, status, id_paiement_fournisseur, id_delai_paiement_fournisseur, escompte, commentaire)
VALUES
    (1, 162, 1, '2019-01-16', 'en cours', 1, 1, 'non', ''),
    (2, 162, 1, '2020-10-02', 'stockee', 1, 1, 'non', ''),
    (3, 163, 1, '2020-10-02', 'en preparation', 1, 1, 'non', '')
;

INSERT INTO backOffice.produit_commande_fournisseur
    (id,id_commande_fournisseur, id_produit, tva, quantite_commandee, quantite_livree, prix_achat, date_livraison_prevue, date_livraison_effective)
VALUES
    (1, 1, 81079, 0.200, 10, 10, 48.54, NULL, DATE(NOW() - INTERVAL 3 MONTH)),
    (2, 1, 81080, 0.200, 10, 0, 48.54, NULL, NULL),
    (3, 1, 81078, 0.200, 10, 0, 48.54, NULL, NULL),
    (4, 2, 81079, 0.200, 10, 10, 48.54, NULL, DATE(NOW() - INTERVAL 3 MONTH)),
    (5, 2, 81080, 0.200, 10, 5, 48.54, NULL, NULL),
    (6, 2, 81078, 0.200, 10, 0, 48.54, NULL, NULL)
;

SET @NO_CONSTRAINT_livraison_produit_commande_fournisseur_BI = 1;
INSERT INTO backOffice.livraison_produit_commande_fournisseur (id, id_produit_commande_fournisseur, date_livraison_prevue, quantite_livraison_prevue)
VALUES
    (1, 1, DATE(NOW() - INTERVAL 3 MONTH), 10),
    (2, 2, '2021-05-28', 5),
    (3, 4, DATE(NOW() - INTERVAL 3 MONTH), 10),
    (4, 5, '2021-06-01', 5),
    (5, 5, '2080-12-25', 3),
    (6, 5, '2011-01-01', 2)
;
SET @NO_CONSTRAINT_livraison_produit_commande_fournisseur_BI = null;
