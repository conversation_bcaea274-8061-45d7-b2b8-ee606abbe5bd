INSERT INTO backOffice.prospect (id_prospect, cnt_email, cnt_lvr_email, email)
VALUES
    (1, '<EMAIL>', '<EMAIL>', '<EMAIL>')
;

INSERT INTO backOffice.paiement (id_paiement, moyen, actif, operation, avoir, paiement, description, surcout, attente_paiement, creation_distante, autorisation_distante, annulation_distante, demande_remise_distante, remboursement_distant, remise_directe, declaration_impaye, interrogation_etat_distante, statut_source, garantie_source, justificatif_creation, justificatif_creation_source, justificatif_creation_type, justificatif_acceptation, justificatif_acceptation_source, justificatif_acceptation_type, justificatif_acceptation_motif, justificatif_remise, justificatif_remise_source, justificatif_remise_type, justificatif_remise_motif, bon_remise, bon_remise_motif, compte_bancaire, journal, emport_depot)
VALUES
    (11, 'CTPE', 'Y', 'paiement', 'N', 'CTPE', 'Carte terminal de paiement', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512000', 'BQ', 'Y')
;

INSERT INTO backOffice.commande (id_commande, id_prospect, cnt_fct_email, cnt_lvr_email, commentaire_facture, no_commande_origine, flux, V_statut_traitement, promotion_id, depot_emport, id_transporteur, id_pdt_transporteur, sales_channel_id, en_attente_de_livraison, date_creation)
VALUES
    (1, 1, '<EMAIL>', '<EMAIL>', 'test', '1', 'traitement', 'trcn_remise', null, null, 2, 1, 1, 1, '2025-04-02'),
    (2, 1, '<EMAIL>', '<EMAIL>', 'test', '2', 'traitement', '', null, 1, null, null, 1, 1, '2025-04-03'),
    (3, 1, '<EMAIL>', '<EMAIL>', 'test', '3', 'traitement', '', null, 1, null, null, 1, 1, '2025-04-04'),
    (4, 1, '<EMAIL>', '<EMAIL>', 'test', '4', 'traitement', 'trcn_remise', null, 1, null, null, 1, 1, '2025-04-05'),
    (5, 1, '<EMAIL>', '<EMAIL>', 'test', '5', 'traitement', 'trcn_remise', null, 1, null, null, 1, 1, '2025-04-06')
;

INSERT INTO backOffice.produit_commande (id, id_commande, id_produit, id_unique, tva, quantite, prix_vente, prix_achat, prix_achat_nc, description, prix_ecotaxe, duree_garantie_ext, prix_garantie_ext, tva_garantie_ext, commission_garantie_ext, tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc, remise_type, remise_montant, remise_description, groupe_type, groupe_description, id_bon_livraison, reservation, prix_sorecop, effective_supplier_order_delivery_date)
VALUES
    (11, 1, 81080, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null, '1990-01-01'),
    (12, 2, 81080, 2, 0.200, 3, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null, null),
    (13, 3, 81080, 3, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null, null),
    (14, 4, 81080, 4, 0.200, 2, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null, null),
    (15, 5, 81080, 5, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null, null)
;

INSERT INTO backOffice.paiement_commande (id, id_commande, id_unique, id_paiement, type, creation_date, creation_usr, creation_montant, creation_justificatif, creation_origine, acceptation_date, acceptation_usr, acceptation_montant, acceptation_justificatif, annulation_date, annulation_usr, annulation_montant, demande_remise_date, demande_remise_usr, remise_date, remise_usr, remise_montant, remise_justificatif, remise_bon, remise_taux, impaye_date, impaye_usr, impaye_montant, auto_statut, auto_statut_detail, auto_garantie, auto_garantie_detail, pays_ip, pays_origine)
VALUES
    (101, 1, 1, 11, 'paiement', '2019-10-08 19:15:18', 'admin', 249.00, null, 'son-video.com', '2019-10-08 19:15:40', 'admin', 249.00, 'XXXXXX', null, null, 0.00, null, null, now(), 'admin', 249.00, 'TPE-1', null, 1, null, null, 0.00, null, null, null, null, null, null),
    (102, 2, 1, 11, 'paiement', '2018-09-11 14:36:23', 'admin', 249.00, null, 'son-video.com', '2019-10-08 19:15:40', 'admin', 249.00, 'XXXXXX', now(), 'admin', 249.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
    (103, 2, 2, 11, 'paiement', '2018-09-11 14:36:23', 'admin', 249.00, null, 'son-video.com', '2018-09-11 14:36:23', 'admin', 249.00, 'XXXXXX', null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null)
;