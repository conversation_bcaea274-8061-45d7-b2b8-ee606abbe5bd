INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
VALUES
    (1000, 'backoffice', 'none', '', '', now(), now(), 1, 1)
;

INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES
    (0, 30, 'FREXP', 'France Express', 'oui', 0, 53, 1, 'Geodis / France Express', '', 'France', 0.000, 0.000, '48 heures', 0.000, 0, null, 'Compte n° : 056704', 5)
;

INSERT INTO backOffice.BO_STK_transfert (id, id_depot_depart, id_depot_arrivee, id_commande, date_creation, date_cloture, utilisateur_creation, type_transfert, statut, commentaire, id_pdt_transporteur)
VALUES
    (3333333, 21, 2, null, '2022-12-22 22:22:22', null, 'backoffice', 'demo', 'tmp', 'commentaire 1222222', 1),
    (4444444, 21, 2, null, '2022-12-22 22:22:22', null, 'backoffice', 'demo', 'tmp', 'commentaire 1222222', 1),
    (5555555, 1, 5, null, '2022-12-22 22:22:22', null, 'backoffice', 'demo', 'tmp', 'commentaire 1222222', 1),
    (6666666, 1, 5, null, '2022-12-22 22:22:22', null, 'backoffice', 'demo', 'tmp', 'commentaire 1222222', 1),
    (7777777, 21, 5, null, '2022-12-22 22:22:22', null, 'backoffice', 'demo', 'tmp', 'commentaire 1222222', 1)
;

UPDATE backOffice.BO_STK_transfert SET statut = 'expedie';

INSERT INTO backOffice.BO_STK_transfer_collection_sticker (id, amount, closed_at)
VALUES
    (1, 10, now()),
    (2, 1503, now())
;

SET FOREIGN_KEY_CHECKS = 0;
INSERT INTO backOffice.BO_STK_transfer_collection (id, transfer_id, sticker_id, collected)
VALUES
    (1, 3333333, 0, 0),
    (2, 4444444, 0, 0),
    (3, 5555555, 0, 0),
    (4, 6666666, 1, 0),
    (5, 7777777, 2, 0)
;
SET FOREIGN_KEY_CHECKS = 1;

INSERT INTO backOffice.prospect (id_prospect, cnt_email, cnt_lvr_email, email, cnt_prenom, cnt_nom, cnt_societe, cnt_code_postal, cnt_telephone, cnt_mobile, societe, date_naissance)
VALUES
    (1, '<EMAIL>', '<EMAIL>', '<EMAIL>', 'Arthur', 'Baudouin', 'SVD', '44100', '0200112233', '0600112233', 'The Entreprise', '2022-01-27')
;
INSERT INTO backOffice.BO_CONF_PDT_EXP_vendeur (id, vendeur, origine, quantite_dispo_min, taux_marque_min, port_offert, coeff_marge, display)
VALUES
    (1, 'amazon.fr', 'sellercentral-europe', 1, 16.00, 0, 0.00, 1)
;

INSERT INTO backOffice.commande (id_commande, id_prospect, cnt_fct_email, cnt_lvr_email, commentaire_facture, no_commande_origine, creation_origine, flux, promotion_id, depot_emport, id_transporteur, id_pdt_transporteur, warehouse_id, sales_channel_id)
VALUES
    (1, 1, '<EMAIL>', '<EMAIL>', 'test', '1', 'son-video.com', 'traitement', null, null, 2, 1, 5, 1),
    (2, 1, '<EMAIL>', '<EMAIL>', 'test', '2', 'backoffice.sonvideopro.com', 'traitement', null, 1, null, null, null, 1),
    (3, 1, '<EMAIL>', '<EMAIL>', 'test', '3', 'son-video.com', 'traitement', null, null, 7, 69, null, 1)
;

INSERT INTO backOffice.produit_commande (id, id_commande, id_produit, id_unique, tva, quantite, prix_vente, prix_achat, prix_achat_nc, description, prix_ecotaxe, duree_garantie_ext, prix_garantie_ext, tva_garantie_ext, commission_garantie_ext, tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc, remise_type, remise_montant, remise_description, groupe_type, groupe_description, id_bon_livraison, reservation, prix_sorecop)
VALUES
    (10, 1, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
    (11, 2, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
    (12, 3, 81078, 1, 0.200, 1, 249.00, 131.58, null, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null)
;

INSERT INTO backOffice.bon_livraison (semaphore, id_bon_livraison, id_depot, id_commande, id_transfert, id_facture, is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation, status, detaxe_export, id_transporteur, id_pdt_transporteur, date_export_transporteur, utilisateur_export_transporteur, date_validation, utilisateur_validation, email_validation, sms_validation, numero_enquete, date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition, impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax, cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd, scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni, numero_kbis, numero_dbc, store_pickup_started_at, picked_by)
VALUES
    (125191468862064, 123, 1, 1, 4444444, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'au depart', 'non', 30, 34, '2019-08-29 11:41:10', 'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null, null, null),
    (125191468862065, 456, 1, 2, 5555555, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'au depart', 'non', 30, 34, '2019-08-29 11:41:10', 'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null, '2020-03-21 15:15:00', 'gege'),
    (125191468862066, 789, 1, 3, 3333333, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'expedie', 'non', 30, 34, '2019-08-29 11:41:10', 'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null, null, null),
    (125191468862067, 012, 1, 3, 6666666, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'expedie', 'non', 30, 34, '2019-08-29 11:41:10', 'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null, null, null),
    (125191468862068, 345, 1, 3, 7777777, null, 0, null, '2019-08-29 04:05:37', 'backoffice', 'expedie', 'non', 30, 34, '2019-08-29 11:41:10', 'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null, null, null)
;

INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite, prix_vente, tva, remise_type, remise_montant, remise_description, duree_garantie_ext, prix_garantie_ext, tva_garantie_ext, commission_garantie_ext, tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc, poids, nombre_colis)
VALUES
    (1, 123, 81078, 2, 'rBlink', 2, 119.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
    (2, 456, 81078, 2, 'rBlink', 4, 119.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
    (3, 789, 81078, 2, 'rBlink', 1, 119.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
    (4, 012, 81078, 2, 'rBlink', 1, 119.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
    (5, 345, 81078, 2, 'rBlink', 4, 119.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type, mono_colis, spidy_tracking_number_mask)
VALUES
    (34, 30, 'XPK', 'Express', 1, 'France Express Livraison le lendemain partout en France. Vous pouvez changer le jour de livraison en répondant au mail/sms de notre transporteur.', 'messagerie', 0, null),
    (35, 30, 'ODX', 'Express', 1, 'France Express - 120 kg sur rendez-vous', 'messagerie', 0, null),
    (36, 30, 'CXI', 'Express', 1, 'France Express Livraison le lendemain partout en France. Vous pouvez changer le jour de livraison en répondant au mail/sms de notre transporteur.', 'expressiste', 0, null)
;

