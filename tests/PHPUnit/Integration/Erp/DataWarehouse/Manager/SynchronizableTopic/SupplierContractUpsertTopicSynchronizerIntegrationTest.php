<?php

declare(strict_types=1);

namespace PHPUnit\Integration\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SupplierContractRepository;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SupplierContractUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\SupplierContractUpsertTopicSynchronizer;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

class SupplierContractUpsertTopicSynchronizerIntegrationTest extends TestCase
{
    /** Teste que le synchronizer peut être instancié correctement */
    public function test_synchronizer_can_be_instantiated(): void
    {
        $repository = $this->createMock(SupplierContractRepository::class);
        $synchronizer = new SupplierContractUpsertTopicSynchronizer($repository);
        
        $this->assertInstanceOf(SupplierContractUpsertTopicSynchronizer::class, $synchronizer);
    }

    /** Teste que le synchronizer peut gérer le topic correct */
    public function test_synchronizer_can_handle_correct_topic(): void
    {
        $repository = $this->createMock(SupplierContractRepository::class);
        $synchronizer = new SupplierContractUpsertTopicSynchronizer($repository);
        
        $this->assertTrue($synchronizer->canHandle(SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT));
        $this->assertFalse($synchronizer->canHandle('other_topic'));
    }

    /** Teste que le topic content peut être créé avec des données valides */
    public function test_topic_content_can_be_created_with_valid_data(): void
    {
        $data = [
            'synchronizable_topic_id' => 123,
            'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
            'supplier_contract_id' => 456,
            'supplier_id' => 789,
            'brand_id' => 101,
            'year' => 2023,
            'discount_description' => ['description' => 'Test discount'],
            'pam' => ['pam_data' => 'test'],
            'rfa' => ['rfa_data' => 'test'],
            'additional_rewards' => ['reward_data' => 'test'],
            'unconditional_discount' => 5.5,
        ];

        $content = new SupplierContractUpsertTopicContent();
        foreach ($data as $key => $value) {
            $content->$key = $value;
        }

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $content);
        $this->assertEquals(123, $content->synchronizable_topic_id);
        $this->assertEquals(SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT, $content->topic);
        $this->assertEquals(456, $content->supplier_contract_id);
        $this->assertEquals(789, $content->supplier_id);
        $this->assertEquals(101, $content->brand_id);
        $this->assertEquals(2023, $content->year);
        $this->assertEquals(['description' => 'Test discount'], $content->discount_description);
        $this->assertEquals(['pam_data' => 'test'], $content->pam);
        $this->assertEquals(['rfa_data' => 'test'], $content->rfa);
        $this->assertEquals(['reward_data' => 'test'], $content->additional_rewards);
        $this->assertEquals(5.5, $content->unconditional_discount);
    }

    /** Teste que le topic content peut être créé avec des valeurs nullables */
    public function test_topic_content_can_be_created_with_nullable_values(): void
    {
        $data = [
            'synchronizable_topic_id' => 123,
            'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
            'supplier_contract_id' => 456,
            'supplier_id' => 789,
            'brand_id' => null,
            'year' => 2023,
            'discount_description' => null,
            'pam' => [],
            'rfa' => [],
            'additional_rewards' => [],
            'unconditional_discount' => 0.0,
        ];

        $content = new SupplierContractUpsertTopicContent();
        foreach ($data as $key => $value) {
            $content->$key = $value;
        }

        $this->assertInstanceOf(SupplierContractUpsertTopicContent::class, $content);
        $this->assertNull($content->brand_id);
        $this->assertNull($content->discount_description);
        $this->assertEquals([], $content->pam);
        $this->assertEquals([], $content->rfa);
        $this->assertEquals([], $content->additional_rewards);
    }

    /** Teste que le synchronizer peut traiter un topic content sans erreur */
    public function test_synchronizer_can_process_topic_content_without_error(): void
    {
        $repository = $this->createMock(SupplierContractRepository::class);
        $repository->expects($this->once())->method('upsert');

        $synchronizer = new SupplierContractUpsertTopicSynchronizer($repository);
        $synchronizer->setLogger(new NullLogger());

        // Mock serializer
        $serializer = $this->createMock(SerializerInterface::class);
        $serializer->method('normalize')->willReturn([
            'supplier_contract_id' => 456,
            'supplier_id' => 789,
            'brand_id' => 101,
            'year' => 2023,
            'discount_description' => ['description' => 'Test discount'],
            'pam' => ['pam_data' => 'test'],
            'rfa' => ['rfa_data' => 'test'],
            'additional_rewards' => ['reward_data' => 'test'],
            'unconditional_discount' => 5.5,
        ]);
        $synchronizer->setSerializer($serializer);

        $content = new SupplierContractUpsertTopicContent();
        $content->synchronizable_topic_id = 123;
        $content->topic = SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT;
        $content->supplier_contract_id = 456;
        $content->supplier_id = 789;
        $content->brand_id = 101;
        $content->year = 2023;
        $content->discount_description = ['description' => 'Test discount'];
        $content->pam = ['pam_data' => 'test'];
        $content->rfa = ['rfa_data' => 'test'];
        $content->additional_rewards = ['reward_data' => 'test'];
        $content->unconditional_discount = 5.5;

        // This should not throw any exception
        $synchronizer->synchronize($content);
        
        $this->assertTrue(true); // If we reach here, the test passed
    }
}
