<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderPaymentRepository;
use Psr\Log\NullLogger;
use Ramsey\Uuid\Uuid;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\CustomerOrderPaymentTopicSynchronizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderPaymentTopicSynchronizerTest extends KernelTestCase
{
    private CustomerOrderPaymentTopicSynchronizer $synchronizer;
    private CustomerOrderPaymentRepository $customer_order_payment_repository;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->customer_order_payment_repository = $this->createMock(CustomerOrderPaymentRepository::class);

        $this->synchronizer = new CustomerOrderPaymentTopicSynchronizer($this->customer_order_payment_repository);
        $this->synchronizer->setSerializer(self::$container->get(SerializerInterface::class));
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle('customer_order_payment.root'));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsertOne avec les données appropriées */
    public function test_synchronize_calls_upsert_one_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'customer_order_payment.root',
            json_encode(
                [
                    'customer_order_payment_id' => 456,
                    'customer_order_id' => 789,
                    'code' => 'PRESTO',
                    'label' => 'Label pour presto',
                    'type' => 'credit_card',
                    'amount' => '149.99',
                    'payment_country_code' => 'FR',
                    'invoice_country_code' => 'FR',
                    'operation_id' => Uuid::uuid4()->toString(),
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure pomm model mock to expect upsertOne call with CustomerOrderPayment instance
        $this->customer_order_payment_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container
                            ->get(SerializerInterface::class)
                            ->normalize($synchronizable_topic->getContent())
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'customer_order_payment.root',
            json_encode(
                [
                    'customer_order_payment_id' => 456,
                    'customer_order_id' => 789,
                    'code' => 'PRESTO',
                    'label' => 'Label pour presto',
                    'type' => 'credit_card',
                    'amount' => '149.99',
                    'payment_country_code' => 'FR',
                    'invoice_country_code' => 'FR',
                    'operation_id' => Uuid::uuid4()->toString(),
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure pomm model mock to throw exception
        $this->customer_order_payment_repository
            ->method('upsert')
            ->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to synchronize topic "customer_order_payment.root" with id 123');

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
