# Tests pour SupplierContractUpsertTopicContent

Ce document décrit les tests créés pour le topic `SupplierContractUpsertTopicContent` et son synchronizer associé.

## Structure des tests

### 1. SupplierContractUpsertTopicSynchronizerTest.php

Ce fichier contient les tests unitaires pour la classe `SupplierContractUpsertTopicSynchronizer`.

#### Tests inclus :

- **test_can_handle_returns_true_for_correct_topic()** : Vérifie que le synchronizer peut gérer le topic `supplier_contract.upsert`
- **test_can_handle_returns_false_for_incorrect_topic()** : Vérifie que le synchronizer rejette les topics incorrects
- **test_synchronize_calls_upsert_with_correct_data()** : Teste que la méthode `synchronize` appelle correctement `upsert` avec les données appropriées
- **test_synchronize_throws_exception_on_error()** : V<PERSON><PERSON><PERSON> que les exceptions sont correctement propagées en cas d'erreur
- **test_synchronize_handles_nullable_values()** : Teste la gestion des valeurs nullables (`brand_id` et `discount_description`)
- **test_synchronize_handles_complex_arrays()** : Teste la gestion des tableaux complexes pour `pam`, `rfa`, et `additional_rewards`
- **test_synchronize_handles_boundary_values()** : Teste les valeurs limites (IDs minimaux, pourcentage maximal)

### 2. SupplierContractUpsertTopicContentTest.php

Ce fichier contient les tests unitaires pour la classe `SupplierContractUpsertTopicContent`.

#### Tests inclus :

- **test_supplier_contract_upsert_topic_content_can_be_instantiated()** : Vérifie que la classe peut être instanciée
- **test_properties_can_be_set_and_retrieved()** : Teste l'assignation et la récupération des propriétés
- **test_nullable_values_are_handled_correctly()** : Teste la gestion des valeurs nullables
- **test_empty_arrays_are_handled_correctly()** : Teste la gestion des tableaux vides
- **test_boundary_values_are_handled_correctly()** : Teste les valeurs limites

### 3. SupplierContractUpsertTopicSynchronizerIntegrationTest.php

Ce fichier contient les tests d'intégration pour vérifier le fonctionnement global du système.

#### Tests inclus :

- **test_synchronizer_can_be_instantiated()** : Vérifie l'instanciation du synchronizer
- **test_synchronizer_can_handle_correct_topic()** : Teste la gestion des topics
- **test_topic_content_can_be_created_with_valid_data()** : Teste la création avec des données valides
- **test_topic_content_can_be_created_with_nullable_values()** : Teste la création avec des valeurs nullables
- **test_synchronizer_can_process_topic_content_without_error()** : Teste le traitement complet sans erreur

## Données de test

### Structure des données utilisées dans les tests :

```php
[
    'synchronizable_topic_id' => 123,
    'topic' => SynchronizableTopicName::SUPPLIER_CONTRACT_UPSERT,
    'supplier_contract_id' => 456,
    'supplier_id' => 789,
    'brand_id' => 101, // nullable
    'year' => 2023,
    'discount_description' => ['description' => 'Test discount'], // nullable
    'pam' => ['pam_data' => 'test'],
    'rfa' => ['rfa_data' => 'test'],
    'additional_rewards' => ['reward_data' => 'test'],
    'unconditional_discount' => 5.5,
]
```

### Cas de test spéciaux :

1. **Valeurs nullables** : `brand_id` et `discount_description` peuvent être `null`
2. **Tableaux vides** : `pam`, `rfa`, et `additional_rewards` peuvent être des tableaux vides
3. **Tableaux complexes** : Les tableaux peuvent contenir des structures de données complexes
4. **Valeurs limites** : Test avec des IDs minimaux (1) et pourcentage maximal (100.0)

## Exécution des tests

Pour exécuter les tests :

```bash
# Tests unitaires du synchronizer
php vendor/bin/phpunit tests/PHPUnit/Unit/Erp/DataWarehouse/Manager/SynchronizableTopic/SupplierContractUpsertTopicSynchronizerTest.php

# Tests unitaires du topic content
php vendor/bin/phpunit tests/PHPUnit/Unit/Erp/DataWarehouse/Manager/SynchronizableTopic/SupplierContractUpsertTopicContentTest.php

# Tests d'intégration
php vendor/bin/phpunit tests/PHPUnit/Integration/Erp/DataWarehouse/Manager/SynchronizableTopic/SupplierContractUpsertTopicSynchronizerIntegrationTest.php

# Tous les tests avec affichage détaillé
php vendor/bin/phpunit tests/PHPUnit/Unit/Erp/DataWarehouse/Manager/SynchronizableTopic/SupplierContract* --testdox
```

## Couverture de test

Les tests couvrent :

- ✅ Instanciation des classes
- ✅ Gestion des topics corrects et incorrects
- ✅ Appel correct de la méthode `upsert`
- ✅ Gestion des exceptions
- ✅ Valeurs nullables
- ✅ Tableaux vides et complexes
- ✅ Valeurs limites
- ✅ Sérialisation/désérialisation
- ✅ Intégration avec le système de logging

## Notes

Ces tests suivent les mêmes patterns que les tests existants pour les autres topics (SafetyStock, CustomerOrder, etc.) pour maintenir la cohérence dans la base de code.
