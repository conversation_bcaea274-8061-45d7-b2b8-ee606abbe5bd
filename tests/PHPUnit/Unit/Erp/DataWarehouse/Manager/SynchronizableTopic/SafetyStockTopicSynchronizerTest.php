<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SafetyStockRepository;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\SafetyStockTopicSynchronizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SafetyStockTopicSynchronizerTest extends KernelTestCase
{
    private SafetyStockTopicSynchronizer $synchronizer;
    private SafetyStockRepository $safety_stock_repository;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->safety_stock_repository = $this->createMock(SafetyStockRepository::class);

        $this->synchronizer = new SafetyStockTopicSynchronizer($this->safety_stock_repository);
        $this->synchronizer->setSerializer(self::$container->get(SerializerInterface::class));
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle('safety_stock'));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsert avec les données appropriées */
    public function test_synchronize_calls_upsert_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'safety_stock',
            json_encode(
                [
                    'product_id' => 456,
                    'supplier_id' => 789,
                    'brand_id' => 101,
                    'snapshotted_at' => '2023-01-01T12:00:00+00:00',
                    'subcategory_id' => 202,
                    'safety_stock' => 50,
                    'shipping_delay' => 3.5,
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure repository mock to expect upsert call with SafetyStock instance
        $this->safety_stock_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container
                            ->get(SerializerInterface::class)
                            ->normalize($synchronizable_topic->getContent())
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'safety_stock',
            json_encode(
                [
                    'product_id' => 456,
                    'supplier_id' => 789,
                    'brand_id' => 101,
                    'snapshotted_at' => '2023-01-01T12:00:00+00:00',
                    'subcategory_id' => 202,
                    'safety_stock' => 50,
                    'shipping_delay' => 3.5,
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure repository mock to throw exception
        $this->safety_stock_repository->method('upsert')->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to synchronize topic "safety_stock" with id 123');

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les valeurs nullables */
    public function test_synchronize_handles_nullable_values(): void
    {
        // Create a real SynchronizableTopic instance with nullable values
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'safety_stock',
            json_encode(
                [
                    'product_id' => 456,
                    'supplier_id' => 789,
                    'brand_id' => 101,
                    'snapshotted_at' => '2023-01-01T12:00:00+00:00',
                    'subcategory_id' => 202,
                    'safety_stock' => null,
                    'shipping_delay' => null,
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure repository mock to expect upsert call with SafetyStock instance
        $this->safety_stock_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container
                            ->get(SerializerInterface::class)
                            ->normalize($synchronizable_topic->getContent())
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
