<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\ConnectionProvider\PgDataWarehouseConnectionProvider;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderLineRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrder;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrderLine;
use Doctrine\DBAL\Connection;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\CustomerOrderUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\CustomerOrderUpsertTopicSynchronizer;
use SonVideo\Erp\DataWarehouse\Repository\Mysql\CustomerOrderLineForDataWarehouseRepository;
use SonVideo\Erp\PromoOffer\Manager\CachedOngoingPromoCodes;
use SonVideo\Erp\Referential\DataWarehouse\CustomerOrderLineStatus;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderUpsertTopicSynchronizerTest extends KernelTestCase
{
    private CustomerOrderUpsertTopicSynchronizer $synchronizer;
    private PgDataWarehouseConnectionProvider $pg_data_warehouse_connection_provider;
    private CustomerOrderLineForDataWarehouseRepository $customer_order_line_for_data_warehouse_repository;
    private CachedOngoingPromoCodes $cached_ongoing_promo_codes;
    private CustomerOrderRepository $customer_order_repository;
    private CustomerOrderLineRepository $customer_order_line_repository;
    private Connection $connection;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->pg_data_warehouse_connection_provider = $this->createMock(PgDataWarehouseConnectionProvider::class);
        $this->customer_order_line_for_data_warehouse_repository = $this->createMock(
            CustomerOrderLineForDataWarehouseRepository::class
        );
        $this->cached_ongoing_promo_codes = $this->createMock(CachedOngoingPromoCodes::class);
        $this->customer_order_repository = $this->createMock(CustomerOrderRepository::class);
        $this->customer_order_line_repository = $this->createMock(CustomerOrderLineRepository::class);

        $this->connection = $this->createMock(Connection::class);
        $this->pg_data_warehouse_connection_provider->method('getConnection')->willReturn($this->connection);

        $this->serializer = self::$container->get(SerializerInterface::class);

        $this->synchronizer = new CustomerOrderUpsertTopicSynchronizer(
            $this->pg_data_warehouse_connection_provider,
            $this->customer_order_line_for_data_warehouse_repository,
            $this->cached_ongoing_promo_codes,
            $this->customer_order_repository,
            $this->customer_order_line_repository
        );
        $this->synchronizer->setSerializer($this->serializer);
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle(SynchronizableTopicName::CUSTOMER_ORDER_UPSERT));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement les méthodes nécessaires avec les données appropriées */
    public function test_synchronize_calls_methods_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(CustomerOrderUpsertTopicContent::class, $synchronizable_topic);

        // Create sample customer order data
        $customer_order = new CustomerOrder();
        $customer_order->customer_order_id = 456;
        $customer_order->promo_code = '3001';

        // Create sample customer order lines data
        $line1 = new CustomerOrderLine();
        $line1->customer_order_line_id = 1001;
        $line1->customer_order_id = 456;
        $line1->reference = 'REF001';

        $line2 = new CustomerOrderLine();
        $line2->customer_order_line_id = 1002;
        $line2->customer_order_id = 456;
        $line2->reference = 'REF002';

        $lines = [$line1, $line2];

        // Configure promo codes mock
        $promo_codes = [3001 => 'BLUEWINE', 3002 => 'REDWINE'];
        $this->cached_ongoing_promo_codes
            ->expects($this->once())
            ->method('getAllLabelsById')
            ->willReturn($promo_codes);

        // Configure repository mock to return sample customer order
        $this->customer_order_line_for_data_warehouse_repository
            ->expects($this->once())
            ->method('findByIdForDataWarehouse')
            ->with(456)
            ->willReturn($customer_order);

        // Configure repository mock to return sample lines
        $this->customer_order_line_for_data_warehouse_repository
            ->expects($this->once())
            ->method('findAllLinesByIdForDataWarehouse')
            ->with(456)
            ->willReturn($lines);

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->once())->method('commit');
        $this->connection->expects($this->never())->method('rollBack');

        // Configure customer order repository mock to expect upsert call
        $this->customer_order_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) {
                    return 456 === $arg['customer_order_id'] && 'BLUEWINE' === $arg['promo_code'];
                })
            );

        // Configure customer order line repository mock to expect upsert calls for each line
        $this->customer_order_line_repository
            ->expects($this->exactly(2))
            ->method('upsert')
            ->withConsecutive(
                [
                    $this->callback(function ($arg) use ($line1) {
                        return $arg['customer_order_line_id'] === $line1->customer_order_line_id;
                    }),
                ],
                [
                    $this->callback(function ($arg) use ($line2) {
                        return $arg['customer_order_line_id'] === $line2->customer_order_line_id;
                    }),
                ]
            );

        // Configure customer order line repository mock to expect updateWhere call
        $this->customer_order_line_repository
            ->expects($this->once())
            ->method('updateWhere')
            ->with(
                $this->callback(function ($arg) {
                    return $arg['deleted_at'] instanceof \DateTimeInterface &&
                        CustomerOrderLineStatus::DELETED === $arg['order_status'];
                }),
                $this->callback(function ($arg) {
                    return 'customer_order_line_id NOT IN (:customer_order_line_id_0, :customer_order_line_id_1) AND customer_order_id = :customer_order_id_0' ===
                        $arg;
                }),
                $this->callback(function ($arg) {
                    return $arg === [
                        'customer_order_line_id_0' => 1001,
                        'customer_order_line_id_1' => 1002,
                        'customer_order_id_0' => 456,
                    ];
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les promo codes null */
    public function test_synchronize_handles_one_null_promo_code(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Create sample customer order data with null promo_code
        $customer_order = new CustomerOrder();
        $customer_order->customer_order_id = 456;
        $customer_order->promo_code = null;

        // Create sample line data to avoid empty array issue with WhereConditionMaker
        $line = new CustomerOrderLine();
        $line->customer_order_line_id = 1001;
        $line->customer_order_id = 456;
        $lines = [$line];

        // Configure promo codes mock
        $promo_codes = [3001 => 'BLUEWINE'];
        $this->cached_ongoing_promo_codes->method('getAllLabelsById')->willReturn($promo_codes);

        // Configure repository mocks
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findByIdForDataWarehouse')
            ->willReturn($customer_order);
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findAllLinesByIdForDataWarehouse')
            ->willReturn($lines);

        // Configure connection
        $this->connection->method('beginTransaction');
        $this->connection->method('commit');

        // Configure customer order repository mock to expect upsert call with null promo_code
        $this->customer_order_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) {
                    return 456 === $arg['customer_order_id'] && null === $arg['promo_code'];
                })
            );

        // Configure customer order line repository mock to expect upsert and updateWhere calls
        $this->customer_order_line_repository->expects($this->once())->method('upsert');
        $this->customer_order_line_repository->expects($this->once())->method('updateWhere');

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les promo codes null */
    public function test_synchronize_handles_no_promo_code(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Create sample customer order data with null promo_code
        $customer_order = new CustomerOrder();
        $customer_order->customer_order_id = 456;
        $customer_order->promo_code = 'NOT_FOUND';

        // Create sample line data to avoid empty array issue with WhereConditionMaker
        $line = new CustomerOrderLine();
        $line->customer_order_line_id = 1001;
        $line->customer_order_id = 456;
        $lines = [$line];

        // Configure promo codes mock
        $promo_codes = [];
        $this->cached_ongoing_promo_codes->method('getAllLabelsById')->willReturn($promo_codes);

        // Configure repository mocks
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findByIdForDataWarehouse')
            ->willReturn($customer_order);
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findAllLinesByIdForDataWarehouse')
            ->willReturn($lines);

        // Configure connection
        $this->connection->method('beginTransaction');
        $this->connection->method('commit');

        // Configure customer order repository mock to expect upsert call with null promo_code
        $this->customer_order_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) {
                    return 456 === $arg['customer_order_id'] && null === $arg['promo_code'];
                })
            );

        // Configure customer order line repository mock to expect upsert and updateWhere calls
        $this->customer_order_line_repository->expects($this->once())->method('upsert');
        $this->customer_order_line_repository->expects($this->once())->method('updateWhere');

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur lors de la récupération de la commande */
    public function test_synchronize_throws_exception_on_customer_order_repository_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Configure repository mock to throw exception
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findByIdForDataWarehouse')
            ->willThrowException(new \Exception('Database error'));

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->never())->method('commit');
        $this->connection->expects($this->once())->method('rollBack');

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::CUSTOMER_ORDER_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur lors de l'upsert de la commande */
    public function test_synchronize_throws_exception_on_customer_order_upsert_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Create sample customer order data
        $customer_order = new CustomerOrder();
        $customer_order->customer_order_id = 456;
        $customer_order->promo_code = null;

        // Configure repository mocks
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findByIdForDataWarehouse')
            ->willReturn($customer_order);
        $this->cached_ongoing_promo_codes->method('getAllLabelsById')->willReturn([]);

        // Configure customer order repository mock to throw exception on upsert
        $this->customer_order_repository->method('upsert')->willThrowException(new \Exception('Upsert error'));

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->never())->method('commit');
        $this->connection->expects($this->once())->method('rollBack');

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::CUSTOMER_ORDER_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur lors de l'upsert des lignes */
    public function test_synchronize_throws_exception_on_customer_order_line_upsert_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Create sample customer order data
        $customer_order = new CustomerOrder();
        $customer_order->customer_order_id = 456;
        $customer_order->promo_code = null;

        // Create sample line data
        $line = new CustomerOrderLine();
        $line->customer_order_line_id = 1001;
        $line->customer_order_id = 456;

        // Configure repository mocks
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findByIdForDataWarehouse')
            ->willReturn($customer_order);
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findAllLinesByIdForDataWarehouse')
            ->willReturn([$line]);
        $this->cached_ongoing_promo_codes->method('getAllLabelsById')->willReturn([]);

        // Configure customer order repository to succeed
        $this->customer_order_repository->method('upsert');

        // Configure customer order line repository mock to throw exception on upsert
        $this->customer_order_line_repository
            ->method('upsert')
            ->willThrowException(new \Exception('Line upsert error'));

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->never())->method('commit');
        $this->connection->expects($this->once())->method('rollBack');

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::CUSTOMER_ORDER_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur lors de l'updateWhere */
    public function test_synchronize_throws_exception_on_update_where_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Create sample customer order data
        $customer_order = new CustomerOrder();
        $customer_order->customer_order_id = 456;
        $customer_order->promo_code = null;

        // Create sample line data
        $line = new CustomerOrderLine();
        $line->customer_order_line_id = 1001;
        $line->customer_order_id = 456;

        // Configure repository mocks
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findByIdForDataWarehouse')
            ->willReturn($customer_order);
        $this->customer_order_line_for_data_warehouse_repository
            ->method('findAllLinesByIdForDataWarehouse')
            ->willReturn([$line]);
        $this->cached_ongoing_promo_codes->method('getAllLabelsById')->willReturn([]);

        // Configure repositories to succeed
        $this->customer_order_repository->method('upsert');
        $this->customer_order_line_repository->method('upsert');

        // Configure customer order line repository mock to throw exception on updateWhere
        $this->customer_order_line_repository
            ->method('updateWhere')
            ->willThrowException(new \Exception('UpdateWhere error'));

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->never())->method('commit');
        $this->connection->expects($this->once())->method('rollBack');

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::CUSTOMER_ORDER_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
