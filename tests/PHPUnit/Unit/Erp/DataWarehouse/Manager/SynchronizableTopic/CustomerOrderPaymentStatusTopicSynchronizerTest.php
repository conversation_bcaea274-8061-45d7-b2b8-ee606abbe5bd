<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderPaymentStatusRepository;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\CustomerOrderPaymentStatusTopicSynchronizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderPaymentStatusTopicSynchronizerTest extends KernelTestCase
{
    private CustomerOrderPaymentStatusTopicSynchronizer $synchronizer;
    private CustomerOrderPaymentStatusRepository $customer_order_payment_status_repository;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->customer_order_payment_status_repository = $this->createMock(
            CustomerOrderPaymentStatusRepository::class
        );

        $this->synchronizer = new CustomerOrderPaymentStatusTopicSynchronizer(
            $this->customer_order_payment_status_repository
        );
        $this->synchronizer->setSerializer(self::$container->get(SerializerInterface::class));
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle('customer_order_payment.status'));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsertOne avec les données appropriées */
    public function test_synchronize_calls_upsert_one_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'customer_order_payment.status',
            json_encode(
                [
                    'customer_order_payment_id' => 456,
                    'name' => 'paid',
                    'created_at' => '2023-01-01T12:00:00+00:00',
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure pomm model mock to expect upsertOne call with CustomerOrderPaymentStatus instance
        $this->customer_order_payment_status_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container
                            ->get(SerializerInterface::class)
                            ->normalize($synchronizable_topic->getContent())
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = new SynchronizableTopic(
            123,
            'customer_order_payment.status',
            json_encode(
                [
                    'customer_order_payment_id' => 456,
                    'name' => 'paid',
                    'created_at' => '2023-01-01T12:00:00+00:00',
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Configure pomm model mock to throw exception
        $this->customer_order_payment_status_repository
            ->method('upsert')
            ->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to synchronize topic "customer_order_payment.status" with id 123');

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
