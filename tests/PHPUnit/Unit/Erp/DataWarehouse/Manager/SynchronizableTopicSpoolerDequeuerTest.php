<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager;

use App\Adapter\Serializer\SerializerInterface;
use SonVideo\Erp\DataWarehouse\Collection\TopicSynchronizerCollection;
use SonVideo\Erp\DataWarehouse\Contract\SynchronizableTopicFeedbackHandlerInterface;
use SonVideo\Erp\DataWarehouse\Contract\TopicSynchronizerInterface;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicDequeueParameters;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicFeedback;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopicSpoolerDequeuer;
use SonVideo\Erp\DataWarehouse\Repository\Mysql\SynchronizableTopicReadRepository;
use SonVideo\Erp\DataWarehouse\Repository\Mysql\SynchronizableTopicWriteRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SynchronizableTopicSpoolerDequeuerTest extends KernelTestCase
{
    private SynchronizableTopicReadRepository $read_repository_mock;
    private SynchronizableTopicWriteRepository $write_repository_mock;
    private TopicSynchronizerCollection $topic_synchronizer_collection_mock;
    private SynchronizableTopicSpoolerDequeuer $dequeuer;
    private SynchronizableTopicFeedbackHandlerInterface $feedback_handler_mock;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->read_repository_mock = $this->createMock(SynchronizableTopicReadRepository::class);
        $this->write_repository_mock = $this->createMock(SynchronizableTopicWriteRepository::class);
        $this->topic_synchronizer_collection_mock = $this->createMock(TopicSynchronizerCollection::class);

        $this->dequeuer = new SynchronizableTopicSpoolerDequeuer(
            $this->read_repository_mock,
            $this->write_repository_mock,
            $this->topic_synchronizer_collection_mock
        );
        $this->dequeuer->setSerializer(self::$container->get(SerializerInterface::class));

        $this->feedback_handler_mock = $this->createMock(SynchronizableTopicFeedbackHandlerInterface::class);
    }

    /** Test that the dequeue method works correctly with no topics to process. */
    public function test_dequeue_with_no_topics(): void
    {
        $this->read_repository_mock
            ->expects($this->once())
            ->method('findHowManyRowsAreLocked')
            ->willReturn(0);

        $this->write_repository_mock
            ->expects($this->once())
            ->method('lockForSynchronization')
            ->with(
                0,
                $this->callback(function ($parameters) {
                    return $parameters instanceof SynchronizableTopicDequeueParameters &&
                        10000 === $parameters->getLimit() &&
                        null === $parameters->getSynchronizableTopicId();
                })
            );

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllTopicsTopicForFeedback')
            ->willReturn([]);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllLockedToSynchronize')
            ->willReturn($this->createGenerator([]));

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('start')
            ->with([]);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('progress')
            ->with(null);

        $this->feedback_handler_mock->expects($this->once())->method('end');

        $this->dequeuer->dequeue($this->feedback_handler_mock);
    }

    /** Test that the dequeue method works correctly with topics to process. */
    public function test_dequeue_with_topics(): void
    {
        $topics = [];

        $topics[] = [
            'synchronizable_topic_id' => 1,
            'topic' => 'customer_order.status',
            'content' => json_encode(['customer_order_id' => 123456, 'status_name' => 'dummy']),
        ];

        $topics[] = [
            'synchronizable_topic_id' => 2,
            'topic' => 'customer_order.status',
            'content' => json_encode(['customer_order_id' => 123458, 'status_name' => 'dummy2']),
        ];

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findHowManyRowsAreLocked')
            ->willReturn(0);

        $this->write_repository_mock
            ->expects($this->once())
            ->method('lockForSynchronization')
            ->with(
                0,
                $this->callback(function ($parameters) {
                    return $parameters instanceof SynchronizableTopicDequeueParameters &&
                        10000 === $parameters->getLimit() &&
                        null === $parameters->getSynchronizableTopicId();
                })
            );

        $feedback = new SynchronizableTopicFeedback('customer_order.status', 2);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllTopicsTopicForFeedback')
            ->willReturn([$feedback]);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllLockedToSynchronize')
            ->willReturn($this->createGenerator($topics));

        $handler_mock = $this->createMock(TopicSynchronizerInterface::class);
        $handler_mock
            ->expects($this->exactly(2))
            ->method('synchronize')
            ->with(
                $this->callback(function ($topic) {
                    return $topic instanceof SynchronizableTopic &&
                        in_array($topic->getSynchronizableTopicId(), [1, 2]) &&
                        'customer_order.status' === $topic->getTopic();
                })
            );

        $this->topic_synchronizer_collection_mock
            ->expects($this->exactly(2))
            ->method('getHandler')
            ->willReturn($handler_mock);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('start')
            ->with([$feedback]);

        $this->feedback_handler_mock
            ->expects($this->exactly(2))
            ->method('getByTopic')
            ->with('customer_order.status')
            ->willReturn($feedback);

        $this->feedback_handler_mock->expects($this->exactly(3))->method('progress');

        $this->feedback_handler_mock->expects($this->once())->method('end');

        $this->write_repository_mock
            ->expects($this->exactly(2))
            ->method('removeSynchronizedTopic')
            ->with(
                $this->callback(function ($id) {
                    return in_array($id, [1, 2]);
                })
            );

        $this->dequeuer->dequeue($this->feedback_handler_mock);
    }

    /** Test that the dequeue method works correctly with a specific topic ID. */
    public function test_dequeue_with_specific_topic_id(): void
    {
        $topics = [
            [
                'synchronizable_topic_id' => 5,
                'topic' => 'customer_order.status',
                'content' => json_encode(['customer_order_id' => 123456, 'status_name' => 'dummy']),
            ],
        ];

        $parameters = new SynchronizableTopicDequeueParameters(10000, 5);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findHowManyRowsAreLocked')
            ->willReturn(0);

        $this->write_repository_mock
            ->expects($this->once())
            ->method('lockForSynchronization')
            ->with(
                0,
                $this->callback(function ($param) {
                    return $param instanceof SynchronizableTopicDequeueParameters &&
                        10000 === $param->getLimit() &&
                        5 === $param->getSynchronizableTopicId();
                })
            );

        $feedback = new SynchronizableTopicFeedback('customer_order.status', 1);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllTopicsTopicForFeedback')
            ->willReturn([$feedback]);

        $this->read_repository_mock
            ->expects($this->once())
            ->method('findAllLockedToSynchronize')
            ->willReturn($this->createGenerator($topics));

        $handler_mock = $this->createMock(TopicSynchronizerInterface::class);
        $handler_mock
            ->expects($this->once())
            ->method('synchronize')
            ->with(
                $this->callback(function ($topic) {
                    return $topic instanceof SynchronizableTopic &&
                        5 === $topic->getSynchronizableTopicId() &&
                        'customer_order.status' === $topic->getTopic();
                })
            );

        $this->topic_synchronizer_collection_mock
            ->expects($this->once())
            ->method('getHandler')
            ->willReturn($handler_mock);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('start')
            ->with([$feedback]);

        $this->feedback_handler_mock
            ->expects($this->once())
            ->method('getByTopic')
            ->with('customer_order.status')
            ->willReturn($feedback);

        $this->feedback_handler_mock->expects($this->exactly(2))->method('progress');

        $this->feedback_handler_mock->expects($this->once())->method('end');

        $this->write_repository_mock
            ->expects($this->once())
            ->method('removeSynchronizedTopic')
            ->with(5);

        $this->dequeuer->dequeue($this->feedback_handler_mock, $parameters);
    }

    /** Helper method to create a generator from an array. */
    private function createGenerator(array $items): \Generator
    {
        foreach ($items as $item) {
            yield $item;
        }
    }
}
