<?php

namespace PHPUnit\Unit\Erp\SubCategory\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Referential\SubcategoryType;
use SonVideo\Erp\SubCategory\Exception\SubcategoryNameAlreadyExistException;
use SonVideo\Erp\SubCategory\Manager\SubcategoryCreator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SubcategoryCreatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'category/categories.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SubcategoryCreator
    {
        return self::$container->get(SubcategoryCreator::class);
    }

    /** Tests the create method. */
    public function test_create(): void
    {
        // Initialize
        $category_id = 137;
        $subcategory_name = 'subcategory test';
        $subcategory_creator = $this->getTestedInstance();

        // Create subcategory success case
        $new_subcategory_id = $subcategory_creator->create($category_id, $subcategory_name);
        $subcategory = $this->fetchSubcategory($new_subcategory_id);

        $this->assertEquals($new_subcategory_id, (int) $subcategory['id']);
        $this->assertEquals($subcategory_name, $subcategory['souscategorie']);
        $this->assertEquals($category_id, (int) $subcategory['dft_categorie_id']);
        $this->assertEquals(SubcategoryType::UNDEFINED, $subcategory['subcategory_type']);

        $subcategory_category = $this->fetchSubcategoryCategory($new_subcategory_id);
        $this->assertEquals(15, (int) $subcategory_category['id_liaison_dom_cat']);
        $this->assertEquals($new_subcategory_id, (int) $subcategory_category['id_souscategorie']);
        $this->assertEquals($category_id, (int) $subcategory_category['id_categorie']);

        // Create subcategory success with trim
        $subcategory_name = '   nom de categorie   ';
        $new_subcategory_id = $subcategory_creator->create($category_id, $subcategory_name);
        $subcategory = $this->fetchSubcategory($new_subcategory_id);

        $this->assertEquals($new_subcategory_id, (int) $subcategory['id']);
        $this->assertEquals(trim($subcategory_name), $subcategory['souscategorie']);
        $this->assertEquals($category_id, (int) $subcategory['dft_categorie_id']);

        // Create subcategory error name already exist
        $this->expectException(SubcategoryNameAlreadyExistException::class);
        $this->expectExceptionMessageMatches('/Subcategory name already exists/');
        $subcategory_creator->create($category_id, $subcategory_name);
    }

    /** Tests the create method with a non-existent category. */
    public function test_create_with_non_existent_category(): void
    {
        $subcategory_name = 'category id doesnt exist';
        $subcategory_creator = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessageMatches('/No category found with id 666/');
        $subcategory_creator->create(666, $subcategory_name);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a subcategory by ID. */
    protected function fetchSubcategory(int $subcategory_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.CTG_TXN_souscategorie
        WHERE id = :subcategory_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['subcategory_id' => $subcategory_id]);
    }

    /** Fetches a subcategory category by subcategory ID. */
    protected function fetchSubcategoryCategory(int $subcategory_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.CTG_TXN_souscategorie_CTG_TXN_categorie
        WHERE id_souscategorie = :subcategory_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['subcategory_id' => $subcategory_id]);
    }
}
