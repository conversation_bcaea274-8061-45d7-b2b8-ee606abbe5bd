<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderOverdueFlagger as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderOverdueFlaggerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/customer_order_overdue_flagger.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests adding unflagged customer orders. */
    public function test_add_unflagged_customer_orders(): void
    {
        $sql = <<<'MYSQL'
        SELECT id_commande FROM backOffice.commande_overdue
        MYSQL;

        // Check initial state
        $this->assertEquals(['12'], $this->getPdo()->fetchCol($sql));

        // Flag overdue orders
        $this->getTestedInstance()->flagOverdue();

        // Check final state
        $this->assertEquals(['11', '12', '14'], $this->getPdo()->fetchCol($sql));
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
