<?php

namespace PHPUnit\EndToEnd\Rpc\CustomerOrder;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class FetchForCmsTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'customer_order:fetch_for_cms';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/fetch_for_cms.sql',
        ]);
    }

    public function test_rpc_with_no_payload(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, []);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '-32602', with message ===
        Could not fetch unexistent argument 0 (customer_order_id).
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_payload_who_is_not_a_json(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['toto']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Customer order id "0" not found.
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_non_existing_customer_order_id(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, [666]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Customer order id "666" not found.
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_successfully_fetch_customer_order(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, [1]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals($response_data['result'], [
            'customer_order_id' => 1,
            'customer_id' => 1,
            'created_at' => '2018-09-11 00:00:00',
            'modified_at' => '2018-09-11 12:30:00',
            'flux' => 'traitement',
            'in_progress_flags' => null,
            'origin' => 'SON-VIDEO.COM',
            'total_price' => 736.5,
            'total_price_vat_excluded' => 613.75,
            'ecotax_price' => 0,
            'shipping_price' => 249,
            'shipment_method_id' => 1,
            'relay_id' => null,
            'store_pick_up_id' => null,
            'quote_id' => null,
            'preparation_email_sent_at' => null,
            'shipping_address' => [
                'city' => null,
                'phone' => null,
                'address' => 'tmp',
                'civility' => 'M.',
                'cellphone' => null,
                'last_name' => null,
                'first_name' => null,
                'postal_code' => null,
                'company_name' => null,
                'country_code' => 'FR',
            ],
            'billing_address' => [
                'city' => null,
                'phone' => null,
                'address' => 'tmp',
                'civility' => 'M.',
                'cellphone' => null,
                'last_name' => null,
                'first_name' => null,
                'postal_code' => null,
                'company_name' => null,
                'country_code' => 'FR',
            ],
            'tags' => [],
            'products' => [
                [
                    'sku' => 'ARCAMRBLINKNR',
                    'quantity' => 1,
                    'article_id' => 81078,
                    'total_amount' => 238.5,
                    'basket_description' => 'rBlink',
                    'estimated_delivery_time' => null,
                    'amount_extension_warranty' => 0,
                    'duration_extension_warranty' => null,
                    'amount_theft_break_extension_warranty' => 0,
                    'duration_theft_break_extension_warranty' => null,
                ],
            ],
            'payments' => [
                [
                    'code' => 'CBS-O',
                    'type' => 'PAYMENT',
                    'amount' => 249,
                    'created_at' => '2022-05-09 20:32:34.000000',
                    'extra_data' => [],
                    'cancel_date' => null,
                    'remitted_at' => null,
                    'remitted_amount' => 0,
                    'validation_date' => null,
                    'validation_proof' => null,
                    'customer_order_id' => 1,
                    'payment_method_id' => 32,
                    'validation_amount' => 0,
                    'back_payment_method_id' => 59,
                    'customer_order_payment_id' => 101,
                ],
                [
                    'code' => 'TEL',
                    'type' => 'PAYMENT',
                    'amount' => 249,
                    'created_at' => '2022-05-09 20:32:34.000000',
                    'extra_data' => [],
                    'cancel_date' => null,
                    'remitted_at' => null,
                    'remitted_amount' => 0,
                    'validation_date' => null,
                    'validation_proof' => null,
                    'customer_order_id' => 1,
                    'payment_method_id' => 9,
                    'validation_amount' => 0,
                    'back_payment_method_id' => 64,
                    'customer_order_payment_id' => 102,
                ],
                [
                    'code' => 'TEL',
                    'type' => 'PAYMENT',
                    'amount' => 249,
                    'created_at' => '2022-05-09 20:32:34.000000',
                    'extra_data' => [],
                    'cancel_date' => null,
                    'remitted_at' => null,
                    'remitted_amount' => 0,
                    'validation_date' => null,
                    'validation_proof' => null,
                    'customer_order_id' => 1,
                    'payment_method_id' => 9,
                    'validation_amount' => 0,
                    'back_payment_method_id' => 65,
                    'customer_order_payment_id' => 103,
                ],
            ],
        ]);

        $this->sendRpcRequest(self::RPC_METHOD, [3]);
        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEmpty($response_data['result']['payments']);
    }
}
