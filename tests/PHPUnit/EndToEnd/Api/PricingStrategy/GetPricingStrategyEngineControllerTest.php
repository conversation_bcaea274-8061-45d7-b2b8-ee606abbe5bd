<?php

namespace PHPUnit\EndToEnd\Api\PricingStrategy;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetPricingStrategyEngineControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'pricing_strategy/pricing_strategy_engine.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/81/engine',
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/81/engine',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_successful_response(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/81/engine',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        //        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertIsArray($response_data['data']['engine']);

        self::assertEquals(
            [
                [
                    'article_id' => 81078,
                    'article_name' => 'Arcam rBlink',
                    'category' => 'Distributeurs et transmetteurs',
                    'delay' => 0,
                    'ecotax' => 0.15,
                    'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                    'is_margin_rate_to_low' => false,
                    'last_scrapping_date' => '2025-05-09 12:44:58',
                    'lowest_competitor' => [
                        'competitor_code' => 'SONVIDEO',
                        'selling_price_with_taxes' => 122,
                    ],
                    'margin' => null,
                    'margin_rate' => null,
                    'margin_tax_excluded' => 75.8,
                    'new_prices' => [
                        [
                            'current_margin_rate' => 0.3654972875226039,
                            'current_selling_price_tax_included' => 249,
                            'margin' => -30.03833333333334,
                            'margin_rate' => -0.29582273286828076,
                            'margin_rate_if_cheapest' => -0.29582273286828076,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'easylounge.com',
                                'legacy_name' => 'ecranlounge',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 2,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 122,
                            'selling_price_tax_excluded' => 101.66666666666667,
                            'selling_price_tax_included' => 122,
                        ],
                        [
                            'current_margin_rate' => 0.3654972875226039,
                            'current_selling_price_tax_included' => 249,
                            'margin' => -30.03833333333334,
                            'margin_rate' => -0.29582273286828076,
                            'margin_rate_if_cheapest' => -0.29582273286828076,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'cilo',
                                'legacy_name' => 'cilo',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 3,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 122,
                            'selling_price_tax_excluded' => 101.66666666666667,
                            'selling_price_tax_included' => 122,
                        ],
                        [
                            'current_margin_rate' => 0.3654972875226039,
                            'current_selling_price_tax_included' => 249,
                            'margin' => -30.03833333333334,
                            'margin_rate' => -0.29582273286828076,
                            'margin_rate_if_cheapest' => -0.29582273286828076,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'cdiscount',
                                'legacy_name' => 'cdiscount',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 4,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 122,
                            'selling_price_tax_excluded' => 101.66666666666667,
                            'selling_price_tax_included' => 122,
                        ],
                    ],
                    'pricing_strategy_id' => 81,
                    'promo_budget_amount' => 0,
                    'purchase_price' => 131.58,
                    'pvgc' => 249,
                    'selling_price' => 122,
                    'sku' => 'ARCAMRBLINKNR',
                    'sorecop' => 0,
                    'status' => 'oui',
                    'stock' => 3,
                    'unconditional_discount' => 0,
                    'weighted_cost_tax_excluded' => 131.58,
                ],
                [
                    'article_id' => 81123,
                    'article_name' => 'La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130',
                    'category' => 'Meubles et supports',
                    'delay' => 0,
                    'ecotax' => 0,
                    'image' => '/images/dynamic/Enceintes/articles/La_Boite_Concept/LBCLD25BP/La-Boite-Concept-Pieds-pour-station-HiFi-LD120-Noir-laque_P_300_square.jpg',
                    'is_margin_rate_to_low' => false,
                    'last_scrapping_date' => '2025-05-09 12:44:58',
                    'lowest_competitor' => [
                        'competitor_code' => 'SONVIDEO',
                        'selling_price_with_taxes' => 389,
                    ],
                    'margin' => 0.838,
                    'margin_rate' => 0.456,
                    'margin_tax_excluded' => 147.82,
                    'new_prices' => [
                        [
                            'current_margin_rate' => 0,
                            'current_selling_price_tax_included' => 0,
                            'margin' => 147.8166666666667,
                            'margin_rate' => 0.45598971722365045,
                            'margin_rate_if_cheapest' => 0.45598971722365045,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'easylounge.com',
                                'legacy_name' => 'ecranlounge',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 2,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 389,
                            'selling_price_tax_excluded' => 324.1666666666667,
                            'selling_price_tax_included' => 389,
                        ],
                        [
                            'current_margin_rate' => 0,
                            'current_selling_price_tax_included' => 0,
                            'margin' => 147.8166666666667,
                            'margin_rate' => 0.45598971722365045,
                            'margin_rate_if_cheapest' => 0.45598971722365045,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'cilo',
                                'legacy_name' => 'cilo',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 3,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 389,
                            'selling_price_tax_excluded' => 324.1666666666667,
                            'selling_price_tax_included' => 389,
                        ],
                        [
                            'current_margin_rate' => 0,
                            'current_selling_price_tax_included' => 0,
                            'margin' => 147.8166666666667,
                            'margin_rate' => 0.45598971722365045,
                            'margin_rate_if_cheapest' => 0.45598971722365045,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'cdiscount',
                                'legacy_name' => 'cdiscount',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 4,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 389,
                            'selling_price_tax_excluded' => 324.1666666666667,
                            'selling_price_tax_included' => 389,
                        ],
                    ],
                    'pricing_strategy_id' => 81,
                    'promo_budget_amount' => 0,
                    'purchase_price' => 195,
                    'pvgc' => 489,
                    'selling_price' => 389,
                    'sku' => 'LBCLD25BP',
                    'sorecop' => 0,
                    'status' => 'oui',
                    'stock' => 2,
                    'unconditional_discount' => 0,
                    'weighted_cost_tax_excluded' => 176.35,
                ],
                [
                    'article_id' => 128416,
                    'article_name' => 'B&W BWCCM74',
                    'category' => 'Enceintes',
                    'delay' => null,
                    'ecotax' => 0,
                    'image' => '/images/dynamic/Enceintes/articles/La_Boite_Concept/LBCLD25BP/La-Boite-Concept-Pieds-pour-station-HiFi-LD120-Noir-laque_P_300_square.jpg',
                    'is_margin_rate_to_low' => false,
                    'last_scrapping_date' => '2025-05-09 12:44:58',
                    'lowest_competitor' => [
                        'competitor_code' => 'DARTY',
                        'selling_price_with_taxes' => 875,
                    ],
                    'margin' => 0.818,
                    'margin_rate' => 0.45,
                    'margin_tax_excluded' => 337.5,
                    'new_prices' => [
                        [
                            'current_margin_rate' => 0,
                            'current_selling_price_tax_included' => 0,
                            'margin' => 315,
                            'margin_rate' => 0.4329896907216495,
                            'margin_rate_if_cheapest' => 0.4329896907216495,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'easylounge.com',
                                'legacy_name' => 'ecranlounge',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 2,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 873,
                            'selling_price_tax_excluded' => 727.5,
                            'selling_price_tax_included' => 873,
                        ],
                        [
                            'current_margin_rate' => 0,
                            'current_selling_price_tax_included' => 0,
                            'margin' => 315,
                            'margin_rate' => 0.4329896907216495,
                            'margin_rate_if_cheapest' => 0.4329896907216495,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'cilo',
                                'legacy_name' => 'cilo',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 3,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 873,
                            'selling_price_tax_excluded' => 727.5,
                            'selling_price_tax_included' => 873,
                        ],
                        [
                            'current_margin_rate' => 0,
                            'current_selling_price_tax_included' => 0,
                            'margin' => 315,
                            'margin_rate' => 0.4329896907216495,
                            'margin_rate_if_cheapest' => 0.4329896907216495,
                            'sales_channel' => [
                                'average_commission_rate' => 0,
                                'display_order' => 1,
                                'label' => 'cdiscount',
                                'legacy_name' => 'cdiscount',
                                'maximum_selling_price' => 0,
                                'minimum_available_quantity' => 0,
                                'minimum_margin_rate' => 0,
                                'minimum_selling_price' => 0,
                                'sales_channel_id' => 4,
                                'statistics_sales_channel' => null,
                            ],
                            'selling_price_if_cheapest' => 873,
                            'selling_price_tax_excluded' => 727.5,
                            'selling_price_tax_included' => 873,
                        ],
                    ],
                    'pricing_strategy_id' => 81,
                    'promo_budget_amount' => 0,
                    'purchase_price' => 412.5,
                    'pvgc' => 900,
                    'selling_price' => 900,
                    'sku' => 'BWCCM74',
                    'sorecop' => 0,
                    'status' => 'tmp',
                    'stock' => 0,
                    'unconditional_discount' => 0,
                    'weighted_cost_tax_excluded' => 0,
                ],
            ],
            $response_data['data']['engine']
        );
    }
}
