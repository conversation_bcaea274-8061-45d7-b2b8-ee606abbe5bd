<?php

namespace PHPUnit\EndToEnd\Api\Carrier;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractCPostBasicWebTestCase;
use Symfony\Component\HttpFoundation\Response;

class CpostCarriersControllerTestAbstract extends AbstractCPostBasicWebTestCase
{
    protected const URL_ENDPOINT = '/api/v1/carriers';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'carrier/cpost_carriers.sql']);
    }

    public function test_get_list_of_carriers_without_filters(): void
    {
        $this->request->post(static::URL_ENDPOINT);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('success', $response['status']);
        $this->assertEquals(54, $response['data']['_pager']['total']);
        $this->assertEquals(50, $response['data']['_pager']['limit']);
        $this->assertCount(50, $response['data']['carriers']);
    }

    public function test_get_list_of_carriers_filtered_on_a_status(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [
                    {
                        "show_in_list": {
                            "_eq": "oui"
                        }
                    }
                ]
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('success', $response['status']);
        $this->assertEquals(37, $response['data']['_pager']['total']);
        $this->assertEquals(50, $response['data']['_pager']['limit']);
        $this->assertCount(37, $response['data']['carriers']);
    }

    public function test_get_carriers_with_all_columns(): void
    {
        $json = <<<JSON
        {
            "where": {
                "_and": [
                    {
                        "show_in_list": {
                            "_eq": "oui"
                        }
                    }
                ]
            },
            "limit": 10
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('success', $response['status']);
        $this->assertEquals(37, $response['data']['_pager']['total']);
        $this->assertEquals(10, $response['data']['_pager']['limit']);
        $this->assertCount(10, $response['data']['carriers']);

        $carrier = $response['data']['carriers'][0];
        $this->assertEquals(1, $carrier['carrier_id']);
        $this->assertEquals('IDFN', $carrier['code']);
        $this->assertEquals('Indéfini', $carrier['name']);
        $this->assertEquals('oui', $carrier['show_in_list']);
        $this->assertEquals(false, $carrier['is_express']);
        $this->assertEquals(999, $carrier['picking_order']);
        $this->assertEquals(0, $carrier['delivery_note_max_threshold']);
        $this->assertEquals('Indéfini', $carrier['description']);
        $this->assertEquals('', $carrier['coordinates']);
        $this->assertEquals('', $carrier['delivery_zone']);
        $this->assertEquals(0, $carrier['min_weight']);
        $this->assertEquals(0, $carrier['max_weight']);
        $this->assertNull($carrier['url_tracking']);
        $this->assertNull($carrier['comment']);
    }

    public function test_get_carriers_which_returns_only_specified_columns(): void
    {
        $json = <<<JSON
        {
            "fields": ["carrier_id", "code", "name"],
            "where": {
                "_and": [
                    {
                        "show_in_list": {
                            "_eq": "oui"
                        }
                    }
                ]
            },
            "limit": 1
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('success', $response['status']);
        $this->assertEquals(37, $response['data']['_pager']['total']);
        $this->assertEquals(1, $response['data']['_pager']['limit']);
        $this->assertCount(1, $response['data']['carriers']);

        $carrier = $response['data']['carriers'][0];
        $this->assertEquals(1, $carrier['carrier_id']);
        $this->assertEquals('IDFN', $carrier['code']);
        $this->assertEquals('Indéfini', $carrier['name']);
    }
}
