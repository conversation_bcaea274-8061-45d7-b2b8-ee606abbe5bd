[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
[remote "origin"]
	url = **************:son-video/erp-server.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
[branch "evol_7543_email_management"]
	remote = origin
	merge = refs/heads/evol_7543_email_management
[branch "feature_api_ezl/ezl_mapper"]
	remote = origin
	merge = refs/heads/feature_api_ezl/ezl_mapper
[branch "evol_carrier_170/issue_#1224"]
	remote = origin
	merge = "refs/heads/evol_carrier_170/issue_#1224"
[branch "fix_phone_optional"]
	remote = origin
	merge = refs/heads/fix_phone_optional
[branch "seller_commission_v14/subcategory_type"]
	remote = origin
	merge = refs/heads/seller_commission_v14/subcategory_type
[branch "identification_devis/cpost_quote_subtypes"]
	remote = origin
	merge = refs/heads/identification_devis/cpost_quote_subtypes
[branch "optiquote/release"]
	remote = origin
	merge = refs/heads/optiquote/release
[branch "optiquote/timeline"]
	remote = origin
	merge = refs/heads/optiquote/timeline
[branch "optiquote/slave"]
	remote = origin
	merge = refs/heads/optiquote/slave
[branch "source_customer_orders/add_source_tag"]
	remote = origin
	merge = refs/heads/source_customer_orders/add_source_tag
[pull]
	rebase = false
[branch "customer_messages/evol_1296"]
	remote = origin
	merge = refs/heads/customer_messages/evol_1296
[branch "shipment_delivery_note/list"]
	remote = origin
	merge = refs/heads/shipment_delivery_note/list
[branch "quote/remove_sales_channel"]
	remote = origin
	merge = refs/heads/quote/remove_sales_channel
[branch "chrono_shipment/fix_service_chrono_classic"]
	remote = origin
	merge = refs/heads/chrono_shipment/fix_service_chrono_classic
[branch "evol_supplier_api"]
	remote = origin
	merge = refs/heads/evol_supplier_api
[branch "evol_#1199/customer_order_in_data_warehouse"]
	remote = origin
	merge = "refs/heads/evol_#1199/customer_order_in_data_warehouse"
[branch "source_customer_orders/rename_tags"]
	remote = origin
	merge = refs/heads/source_customer_orders/rename_tags
[branch "source_customer_orders/release"]
	remote = origin
	merge = refs/heads/source_customer_orders/release
[branch "source_customer_orders/rename_source_tags"]
	remote = origin
	merge = refs/heads/source_customer_orders/rename_source_tags
[branch "change_quote_subtype_namespace"]
	remote = origin
	merge = refs/heads/change_quote_subtype_namespace
[branch "api_article/history_1310"]
	remote = origin
	merge = refs/heads/api_article/history_1310
[branch "source_customer_orders/1257_fix_sources"]
	remote = origin
	merge = refs/heads/source_customer_orders/1257_fix_sources
[branch "quote/issue_1256"]
	remote = origin
	merge = refs/heads/quote/issue_1256
[branch "quote/release"]
	remote = origin
	merge = refs/heads/quote/release
[branch "source_customer_orders/new_seller_role"]
	remote = origin
	merge = refs/heads/source_customer_orders/new_seller_role
[branch "supplier_order/release"]
	remote = origin
	merge = refs/heads/supplier_order/release
[branch "supplier_order/1342_supplier_brand_filter"]
	remote = origin
	merge = refs/heads/supplier_order/1342_supplier_brand_filter
[branch "supplier_order/1342_post_supplier_order"]
	remote = origin
	merge = refs/heads/supplier_order/1342_post_supplier_order
[branch "supplier_order/1342_post_supplier_order_product"]
	remote = origin
	merge = refs/heads/supplier_order/1342_post_supplier_order_product
[branch "customer_order_source/fix_source_tests"]
	remote = origin
	merge = refs/heads/customer_order_source/fix_source_tests
[branch "api_customer_order/1449"]
	remote = origin
	merge = refs/heads/api_customer_order/1449
[branch "article_media_upload/delete_1408"]
	remote = origin
	merge = refs/heads/article_media_upload/delete_1408
[branch "article_v2/editing_data_providers_1326"]
	remote = origin
	merge = refs/heads/article_v2/editing_data_providers_1326
[branch "stocktake/release"]
	remote = origin
	merge = refs/heads/stocktake/release
[branch "stocktake/1466_return_expected_quantities"]
	remote = origin
	merge = refs/heads/stocktake/1466_return_expected_quantities
[branch "api_customer_order/1449_2"]
	remote = origin
	merge = refs/heads/api_customer_order/1449_2
[branch "supplier_order/1371_fix_ordering"]
	remote = origin
	merge = refs/heads/supplier_order/1371_fix_ordering
[branch "article_v2/last_post_1382"]
	remote = origin
	merge = refs/heads/article_v2/last_post_1382
[branch "seller_commission_v16/retail_store"]
	remote = origin
	merge = refs/heads/seller_commission_v16/retail_store
[branch "supplier_order/1342_change_permission_name"]
	remote = origin
	merge = refs/heads/supplier_order/1342_change_permission_name
[branch "inventory/release"]
	remote = origin
	merge = refs/heads/inventory/release
[branch "inventory/1462_return_is_validated_automatically"]
	remote = origin
	merge = refs/heads/inventory/1462_return_is_validated_automatically
[branch "partial_inventory/release"]
	remote = origin
	merge = refs/heads/partial_inventory/release
[branch "partial_inventory/1457_inventory_zones"]
	remote = origin
	merge = refs/heads/partial_inventory/1457_inventory_zones
[branch "partial_inventory/1457_inventory_zones_2"]
	remote = origin
	merge = refs/heads/partial_inventory/1457_inventory_zones_2
[branch "partial_inventory/1457_inventory_zones_3"]
	remote = origin
	merge = refs/heads/partial_inventory/1457_inventory_zones_3
[branch "partial_inventory/1457_inventory_zones_4"]
	remote = origin
	merge = refs/heads/partial_inventory/1457_inventory_zones_4
[branch "partial_inventory/1458_activate_inventory"]
	remote = origin
	merge = refs/heads/partial_inventory/1458_activate_inventory
[branch "statistics/workflow"]
	remote = origin
	merge = refs/heads/statistics/workflow
[branch "partial_inventory/1462_fix_partial_inventory"]
	remote = origin
	merge = refs/heads/partial_inventory/1462_fix_partial_inventory
[branch "partial_inventory/release_2"]
	remote = origin
	merge = refs/heads/partial_inventory/release_2
[branch "partial_inventory/1515_cancel_inventory"]
	remote = origin
	merge = refs/heads/partial_inventory/1515_cancel_inventory
[branch "partial_inventory/1553_activation_constraints"]
	remote = origin
	merge = refs/heads/partial_inventory/1553_activation_constraints
[branch "partial_inventory/1157_export"]
	remote = origin
	merge = refs/heads/partial_inventory/1157_export
[branch "partial_inventory/release_3"]
	remote = origin
	merge = refs/heads/partial_inventory/release_3
[branch "partial_inventory/1576_bl_constraint"]
	remote = origin
	merge = refs/heads/partial_inventory/1576_bl_constraint
[branch "statistics/save_unavailable_product"]
	remote = origin
	merge = refs/heads/statistics/save_unavailable_product
[branch "partial_inventory/release_3.1"]
	remote = origin
	merge = refs/heads/partial_inventory/release_3.1
[branch "partial_inventory/1581_confirm_empty"]
	remote = origin
	merge = refs/heads/partial_inventory/1581_confirm_empty
[branch "shipment_delivery_note/print"]
	remote = origin
	merge = refs/heads/shipment_delivery_note/print
[branch "partial_inventory/release_4"]
	remote = origin
	merge = refs/heads/partial_inventory/release_4
[branch "partial_inventory/1556_check_empty"]
	remote = origin
	merge = refs/heads/partial_inventory/1556_check_empty
[branch "supplier_order/1606_qte_dispo_resa"]
	remote = origin
	merge = refs/heads/supplier_order/1606_qte_dispo_resa
[branch "supplier_order/release_2"]
	remote = origin
	merge = refs/heads/supplier_order/release_2
[branch "supplier_order/1587_rework_qte_attente"]
	remote = origin
	merge = refs/heads/supplier_order/1587_rework_qte_attente
[branch "deploy/1611_readme"]
	remote = origin
	merge = refs/heads/deploy/1611_readme
[branch "article_v2/1632_pondere"]
	remote = origin
	merge = refs/heads/article_v2/1632_pondere
[branch "article_v2/release_pondere"]
	remote = origin
	merge = refs/heads/article_v2/release_pondere
[branch "article_v2/1632_post_price_adjustment"]
	remote = origin
	merge = refs/heads/article_v2/1632_post_price_adjustment
[branch "article_v2/1632_put_cost_adjustment"]
	remote = origin
	merge = refs/heads/article_v2/1632_put_cost_adjustment
[branch "article_v2/1632_event_log_unit_test"]
	remote = origin
	merge = refs/heads/article_v2/1632_event_log_unit_test
[branch "partial_inventory/1697_display_pwa"]
	remote = origin
	merge = refs/heads/partial_inventory/1697_display_pwa
[branch "partial_inventory/release_product_inventory"]
	remote = origin
	merge = refs/heads/partial_inventory/release_product_inventory
[branch "article_v2/1629_cpost_brands"]
	remote = origin
	merge = refs/heads/article_v2/1629_cpost_brands
[branch "partial_inventory/1697_get_inventory_locations"]
	remote = origin
	merge = refs/heads/partial_inventory/1697_get_inventory_locations
[branch "partial_inventory/activate_product_inventory_1692"]
	remote = origin
	merge = refs/heads/partial_inventory/activate_product_inventory_1692
[branch "article_v2/release"]
	remote = origin
	merge = refs/heads/article_v2/release
[branch "article_v2/1630_put_basic_info"]
	remote = origin
	merge = refs/heads/article_v2/1630_put_basic_info
[branch "article_v2/coumpound/1729_header"]
	remote = origin
	merge = refs/heads/article_v2/coumpound/1729_header
[branch "article_v2/compound/display_components_1725"]
	remote = origin
	merge = refs/heads/article_v2/compound/display_components_1725
[branch "article_v2/compound/release"]
	remote = origin
	merge = refs/heads/article_v2/compound/release
[branch "article_v2/compound/post_components_1726"]
	remote = origin
	merge = refs/heads/article_v2/compound/post_components_1726
[branch "article_v2/refacto_article_name_model"]
	remote = origin
	merge = refs/heads/article_v2/refacto_article_name_model
[branch "article_v2/compound/put_components_1726"]
	remote = origin
	merge = refs/heads/article_v2/compound/put_components_1726
[branch "article_v2/compound/delete_components_1726"]
	remote = origin
	merge = refs/heads/article_v2/compound/delete_components_1726
[branch "article_v2/compound/fix_add_package"]
	remote = origin
	merge = refs/heads/article_v2/compound/fix_add_package
[branch "fix_updat_acount_info"]
	remote = origin
	merge = refs/heads/fix_updat_acount_info
[branch "article_v2/logistic_info_1376"]
	remote = origin
	merge = refs/heads/article_v2/logistic_info_1376
[branch "export_sales_1928"]
	remote = origin
	merge = refs/heads/export_sales_1928
[branch "logistic_stats/import"]
	remote = origin
	merge = refs/heads/logistic_stats/import
[branch "transfer/1971_get_stalled_transfers"]
	remote = origin
	merge = refs/heads/transfer/1971_get_stalled_transfers
[branch "transfer/release"]
	remote = origin
	merge = refs/heads/transfer/release
[branch "transfer/1972_get_report"]
	remote = origin
	merge = refs/heads/transfer/1972_get_report
[branch "transfer/1973_dispatch_notes"]
	remote = origin
	merge = refs/heads/transfer/1973_dispatch_notes
[branch "transfer/1753_stickers"]
	remote = origin
	merge = refs/heads/transfer/1753_stickers
	vscode-merge-base = origin/transfer/release
[branch "transfer/1753_reprint_sticker"]
	remote = origin
	merge = refs/heads/transfer/1753_reprint_sticker
[branch "transfer/2055_dispatch_note"]
	remote = origin
	merge = refs/heads/transfer/2055_dispatch_note
[branch "fix_redmine_13312"]
	remote = origin
	merge = refs/heads/fix_redmine_13312
[branch "promo_offer/2079_delete_po"]
	remote = origin
	merge = refs/heads/promo_offer/2079_delete_po
[branch "promo_budget/release"]
	remote = origin
	merge = refs/heads/promo_budget/release
[branch "promo_budget/2079_post"]
	remote = origin
	merge = refs/heads/promo_budget/2079_post
[branch "promo_budget/2079_delete"]
	remote = origin
	merge = refs/heads/promo_budget/2079_delete
[branch "promo_budget/2079_cpost"]
	remote = origin
	merge = refs/heads/promo_budget/2079_cpost
[branch "promo_budget/2079_put"]
	remote = origin
	merge = refs/heads/promo_budget/2079_put
[branch "promo_budget/2079"]
	remote = origin
	merge = refs/heads/promo_budget/2079
[branch "promo_budget/2118_unconditional_discount"]
	remote = origin
	merge = refs/heads/promo_budget/2118_unconditional_discount
[branch "2049_modification_edit_subcat"]
	remote = origin
	merge = refs/heads/2049_modification_edit_subcat
[branch "pricing_strategy/release"]
	remote = origin
	merge = refs/heads/pricing_strategy/release
[branch "pricing_strategy/engine_prototype"]
	remote = origin
	merge = refs/heads/pricing_strategy/engine_prototype
[branch "promo_budget/fix_null_amount"]
	remote = origin
	merge = refs/heads/promo_budget/fix_null_amount
[branch "pricing_strategy/2126_post"]
	remote = origin
	merge = refs/heads/pricing_strategy/2126_post
[branch "pricing_strategy/2126_sales_channel"]
	remote = origin
	merge = refs/heads/pricing_strategy/2126_sales_channel
[branch "pricing_strategy/2127_cpost"]
	remote = origin
	merge = refs/heads/pricing_strategy/2127_cpost
[branch "pricing_strategy/2127_put"]
	remote = origin
	merge = refs/heads/pricing_strategy/2127_put
[branch "support_2061"]
	remote = origin
	merge = refs/heads/support_2061
[branch "pricing_strategy/2218_connect_engine"]
	remote = origin
	merge = refs/heads/pricing_strategy/2218_connect_engine
[branch "pricing_strategy/2143_activate_deactivate"]
	remote = origin
	merge = refs/heads/pricing_strategy/2143_activate_deactivate
[branch "pricing_strategy/2218_update_prices"]
	remote = origin
	merge = refs/heads/pricing_strategy/2218_update_prices
[branch "feature/multi-channel-pricing"]
	remote = origin
	merge = refs/heads/feature/multi-channel-pricing
[branch "mutli-channel-pricing/locked-on-svd-price"]
	remote = origin
	merge = refs/heads/mutli-channel-pricing/locked-on-svd-price
[branch "customer_order_synchro/release"]
	remote = origin
	merge = refs/heads/customer_order_synchro/release
[branch "kpi_appro/2299"]
	remote = origin
	merge = refs/heads/kpi_appro/2299
[branch "pricing_strategy/2262_expired"]
	remote = origin
	merge = refs/heads/pricing_strategy/2262_expired
[branch "kpi_appro/2299_supplier_order_product"]
	remote = origin
	merge = refs/heads/kpi_appro/2299_supplier_order_product
[branch "kpi_appro/2299_rupture"]
	remote = origin
	merge = refs/heads/kpi_appro/2299_rupture
[branch "kpi_appro/fix_supplier_order_product"]
	remote = origin
	merge = refs/heads/kpi_appro/fix_supplier_order_product
[branch "fix_multichannel_margin"]
	remote = origin
	merge = refs/heads/fix_multichannel_margin
[branch "supplier_contract/2247_create"]
	remote = origin
	merge = refs/heads/supplier_contract/2247_create
[branch "multichannel_pricing/2323"]
	remote = origin
	merge = refs/heads/multichannel_pricing/2323
[branch "pricing_strategy/2332"]
	remote = origin
	merge = refs/heads/pricing_strategy/2332
[branch "pricing_strategy/conflicts"]
	remote = origin
	merge = refs/heads/pricing_strategy/conflicts
[branch "competitors"]
	remote = origin
	merge = refs/heads/competitors
[branch "pricing_strategy/2351"]
	remote = origin
	merge = refs/heads/pricing_strategy/2351
[branch "pricing_strategy/2356_mock_weekend"]
	remote = origin
	merge = refs/heads/pricing_strategy/2356_mock_weekend
[branch "fix_2332"]
	remote = origin
	merge = refs/heads/fix_2332
[branch "fix_kpi_rupture"]
	remote = origin
	merge = refs/heads/fix_kpi_rupture
[branch "redmine_13649"]
	remote = origin
	merge = refs/heads/redmine_13649
[branch "pricing-strategy/command-tagging"]
	remote = origin
	merge = refs/heads/pricing-strategy/command-tagging
[branch "pricing_strategy/2367_psychological_price"]
	remote = origin
	merge = refs/heads/pricing_strategy/2367_psychological_price
[branch "pricing_strategy/release2"]
	remote = origin
	merge = refs/heads/pricing_strategy/release2
[branch "pricing_strategy/fix_pricing_strategy"]
	remote = origin
	merge = refs/heads/pricing_strategy/fix_pricing_strategy
[branch "fix_export_product_ezl"]
	remote = origin
	merge = refs/heads/fix_export_product_ezl
[branch "pricing_strategy/fix_engine_promo"]
	remote = origin
	merge = refs/heads/pricing_strategy/fix_engine_promo
[branch "2380_cleanup_wiser"]
	remote = origin
	merge = refs/heads/2380_cleanup_wiser
[branch "2370_customer_order_api"]
	remote = origin
	merge = refs/heads/2370_customer_order_api
[branch "2370_release"]
	remote = origin
	merge = refs/heads/2370_release
[branch "validation"]
	remote = origin
	merge = refs/heads/validation
[branch "2370_customer_order_api_2"]
	remote = origin
	merge = refs/heads/2370_customer_order_api_2
[branch "fix_quote_to_order"]
	remote = origin
	merge = refs/heads/fix_quote_to_order
[branch "kpi_customer_order_payments/synchronize_customer_order_payments"]
	remote = origin
	merge = refs/heads/kpi_customer_order_payments/synchronize_customer_order_payments
[branch "kpi_customer_order_payments/feature_get_data-before_synchro"]
	remote = origin
	merge = refs/heads/kpi_customer_order_payments/feature_get_data-before_synchro
[branch "fix_clone_order"]
	remote = origin
	merge = refs/heads/fix_clone_order
[branch "fix_kpi_supplier_order_product_quantity"]
	remote = origin
	merge = refs/heads/fix_kpi_supplier_order_product_quantity
[branch "kpi_safety_stock"]
	remote = origin
	merge = refs/heads/kpi_security_stock
[branch "kpi_customer_order_payments/release"]
	remote = origin
	merge = refs/heads/kpi_customer_order_payments/release
[branch "kpi_safety_stock_release"]
	remote = origin
	merge = refs/heads/kpi_safety_stock_release
[branch "fix_kpi_safety_stock"]
	remote = origin
	merge = refs/heads/fix_kpi_safety_stock
[branch "support_2443/release"]
	remote = origin
	merge = refs/heads/support_2443/release
[branch "support_2443/supplier_order_type"]
	remote = origin
	merge = refs/heads/support_2443/supplier_order_type
