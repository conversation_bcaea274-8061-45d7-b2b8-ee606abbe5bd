0000000000000000000000000000000000000000 2cd4bb4c47389dd128ae7034110c1579f3879b71 RomainM <romain.mabil<PERSON>@son-video.com> 1742996287 +0100	branch: Created from HEAD
2cd4bb4c47389dd128ae7034110c1579f3879b71 37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 RomainM <<EMAIL>> 1742996319 +0100	commit: feat[support#2370]: WIP use customer order api when converting quote
37fe6fed926d6f8f0ce7257cfaba49ae2cb86291 b602fbf8816334684aafd4c9e36a702af2a209b2 RomainM <<EMAIL>> 1743067079 +0100	commit: feat[support#2370]: add country repository to get countrycode
b602fbf8816334684aafd4c9e36a702af2a209b2 08ca58ff0f0090dfc05dcee7d9c70a9eebf7a716 RomainM <<EMAIL>> 1743067121 +0100	commit: feat[support#2370]: rector
08ca58ff0f0090dfc05dcee7d9c70a9eebf7a716 e17496468b0f0df6ff590d85fc1229669b2fe8da RomainM <<EMAIL>> 1743069788 +0100	commit: feat[support#2370]: fix user in tests
e17496468b0f0df6ff590d85fc1229669b2fe8da c559061a8c4dd90de4c8ff4c055c3a68cac9dd72 RomainM <<EMAIL>> 1743093299 +0100	commit: feat[support#2370]: reorder use
c559061a8c4dd90de4c8ff4c055c3a68cac9dd72 9598e63d73584b691f5c0eea41de911cadd17793 RomainM <<EMAIL>> 1743093557 +0100	commit: feat[support#2370]: fix tests
9598e63d73584b691f5c0eea41de911cadd17793 afc15cbdf73aaa6437bcc64a2092de9a0947f753 RomainM <<EMAIL>> 1743180860 +0100	commit: feat[support#2370]: extract logic into abstractCreationDataMapper
afc15cbdf73aaa6437bcc64a2092de9a0947f753 b806f67f7777ce0c0ed2a928dc9f01f5e0c5a165 RomainM <<EMAIL>> 1743180937 +0100	commit: feat[support#2370]: fix tests
b806f67f7777ce0c0ed2a928dc9f01f5e0c5a165 444c0a4173f4ba882581f3bb44ea01ce49a10aec RomainM <<EMAIL>> 1743182930 +0100	commit: feat[support#2370]: remove logger
444c0a4173f4ba882581f3bb44ea01ce49a10aec 0dc69d0804a9105052c356e7374e2a306262f721 RomainM <<EMAIL>> 1743520107 +0200	commit: feat[support#2370]: add sorecop_price and fetc shipping product
0dc69d0804a9105052c356e7374e2a306262f721 382bb35ec9a93597c53ca4999060bad8c1f4c38d RomainM <<EMAIL>> 1743520198 +0200	commit: feat[support#2370]: add new source for customer order
382bb35ec9a93597c53ca4999060bad8c1f4c38d 0218690ec602fc73c83389d191bbe735b05a70e7 RomainM <<EMAIL>> 1743521222 +0200	commit: feat[support#2370]: add new source
0218690ec602fc73c83389d191bbe735b05a70e7 3956fa4d1b151949c02a8c3759f2cee37c7ed3fc RomainM <<EMAIL>> 1743521313 +0200	commit: feat[support#2370]: add comment if customer order is a clone
3956fa4d1b151949c02a8c3759f2cee37c7ed3fc c952936326d2f71bca893c55475cee7d45bde230 RomainM <<EMAIL>> 1743521373 +0200	commit: feat[support#2370]: new api postCloneCustomerOrder
c952936326d2f71bca893c55475cee7d45bde230 f2f21d507fb95cf753f27dd7ec26f4808bb7367e RomainM <<EMAIL>> 1743604040 +0200	commit: feat[support#2370]: add not found exception
f2f21d507fb95cf753f27dd7ec26f4808bb7367e 204f72ab2950a45df7961ab63ce0000eb08645cb RomainM <<EMAIL>> 1743604084 +0200	commit (amend): feat[support#2370]: add not found exception + tests behat
204f72ab2950a45df7961ab63ce0000eb08645cb ff998ef56b88a5ee505e54e523a9945c6a77f74b RomainM <<EMAIL>> 1743604155 +0200	commit: feat[support#2370]: add not found exception + tests behat
ff998ef56b88a5ee505e54e523a9945c6a77f74b 1512d2edd4da35617076417da3683ae846782579 RomainM <<EMAIL>> 1743604680 +0200	commit: feat[support#2370]: rename fixture file
1512d2edd4da35617076417da3683ae846782579 be22022876808c23d8b500c06745f45c7d08bce3 RomainM <<EMAIL>> 1743610904 +0200	commit: feat[support#2370]: unit tests
be22022876808c23d8b500c06745f45c7d08bce3 c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 RomainM <<EMAIL>> 1743667153 +0200	commit: feat[support#2370]: unit tests
c0e9e941eaf2390ae2ecf55ba8c5b6de93ade0b1 702621db665a8ea49960a172dcfca64d07e40d8e RomainM <<EMAIL>> 1743673941 +0200	commit: feat[support#2370]: remove source
702621db665a8ea49960a172dcfca64d07e40d8e f28fd6a108e06085bac8f9288d408cbf32d0edb1 RomainM <<EMAIL>> 1743675265 +0200	commit: feat[support#2370]: fix test
f28fd6a108e06085bac8f9288d408cbf32d0edb1 4920fa4d0ac47b7ab8a2424020c849215d417e02 RomainM <<EMAIL>> 1743675453 +0200	commit: revert
4920fa4d0ac47b7ab8a2424020c849215d417e02 13a7987e56a80c2a6325c53318aee2e22704f36c RomainM <<EMAIL>> 1743675484 +0200	commit: feat[support#2370]: fix test
13a7987e56a80c2a6325c53318aee2e22704f36c 69393dbd7f7e80f2c94843c2bec24ef81351d24f RomainM <<EMAIL>> 1743675628 +0200	commit: feat[support#2370]: fix test
69393dbd7f7e80f2c94843c2bec24ef81351d24f 5931f6f0229150a963740d1ef3cdd8fd4e0a5c9f RomainM <<EMAIL>> 1744278826 +0200	merge refs/heads/master: Merge made by the 'ort' strategy.
5931f6f0229150a963740d1ef3cdd8fd4e0a5c9f b00de900642dc3a6c5dd43fd51d32f61eb478c4d RomainM <<EMAIL>> 1744289905 +0200	commit: feat[support#2370]: remove print in test
b00de900642dc3a6c5dd43fd51d32f61eb478c4d aefd62ba7f0854b7d762b303065e8ee3ac0233de RomainM <<EMAIL>> 1744292572 +0200	commit: feat[support#2370]: add sprintf
aefd62ba7f0854b7d762b303065e8ee3ac0233de f89b54d21bf68d629bba1ce057599f36224cd273 RomainM <<EMAIL>> 1744301399 +0200	commit: feat[support#2370]: feedback review
f89b54d21bf68d629bba1ce057599f36224cd273 61a2217faea0f9f12008a981a5929af689639707 RomainM <<EMAIL>> 1744968092 +0200	commit (merge): Merge branch 'refs/heads/2370_release' into 2370_customer_order_api
