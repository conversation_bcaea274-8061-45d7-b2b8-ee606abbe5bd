0000000000000000000000000000000000000000 92826ac39bc02481f5bc441fe051940a536db6c4 RomainM <<EMAIL>> 1738165570 +0100	branch: Created from HEAD
92826ac39bc02481f5bc441fe051940a536db6c4 06401225a190da285c5f75adcc6511c40bc4be15 RomainM <<EMAIL>> 1738165577 +0100	commit: wip
06401225a190da285c5f75adcc6511c40bc4be15 dd995d545571c278383f8ddbc74ee7248e7843c9 RomainM <<EMAIL>> 1740406270 +0100	commit (merge): Merge branch 'refs/heads/master' into pricing_strategy/conflicts
dd995d545571c278383f8ddbc74ee7248e7843c9 112cd373f8bcdf8b0392ebb82991c95ca921b116 RomainM <<EMAIL>> 1740406678 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
112cd373f8bcdf8b0392ebb82991c95ca921b116 16db4751cfdd64dbd3db69bb6e8988f9a9ff2962 RomainM <<EMAIL>> 1740409067 +0100	commit (merge): Merge branch 'refs/heads/competitors' into pricing_strategy/conflicts
16db4751cfdd64dbd3db69bb6e8988f9a9ff2962 ae60df1bc5e9c6e5d9602ff4ffa20cae12505307 RomainM <<EMAIL>> 1740411068 +0100	commit (merge): Merge branch 'refs/heads/pricing_strategy/2332' into pricing_strategy/conflicts
