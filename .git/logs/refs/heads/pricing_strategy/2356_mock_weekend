0000000000000000000000000000000000000000 e0ef4cce81045081cdb91c820e175a9b5aeaee89 RomainM <<EMAIL>> 1740585645 +0100	branch: Created from pricing_strategy/release^0
e0ef4cce81045081cdb91c820e175a9b5aeaee89 d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 RomainM <<EMAIL>> 1740585724 +0100	commit: feat[support#2356]: add param to mock weekend for engine preview
d9ad415a53e4d2ae3736b74c490386cd04eb3bb8 e7a2beacd9a1735752f95942bc2002434897e4b8 RomainM <<EMAIL>> 1740653239 +0100	merge refs/heads/pricing_strategy/release: Merge made by the 'ort' strategy.
e7a2beacd9a1735752f95942bc2002434897e4b8 d2a400233540648e4896a1b36427daddb1296c1e RomainM <<EMAIL>> 1740673964 +0100	commit: feat[support#2356]: add tests on weekend
d2a400233540648e4896a1b36427daddb1296c1e e16d9edcea949c4b0c2bcf4fffaf39268f5beec8 RomainM <<EMAIL>> 1740674766 +0100	commit: feat[support#2356]: remove mock for weekend
e16d9edcea949c4b0c2bcf4fffaf39268f5beec8 866ad3ca7f087329b886d0802df5eb0e926a6d47 RomainM <<EMAIL>> 1740674793 +0100	commit: feat[support#2356]: rector
866ad3ca7f087329b886d0802df5eb0e926a6d47 d6e44b19dc9f72168c3f57271cc745cf2f338fa7 RomainM <<EMAIL>> 1741097178 +0100	commit: feat[support#2356]: remove mock bc unused
